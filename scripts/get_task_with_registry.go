package main

import (
	"context"
	"fmt"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
)

func main() {
	// Подключение к MongoDB
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	mongoURI := "mongodb://kvint:<EMAIL>:27017/caller?authSource=admin"

	client, err := mongo.Connect(ctx, options.Client().ApplyURI(mongoURI))
	if err != nil {
		log.Fatalf("Failed to connect to MongoDB: %v", err)
	}
	defer client.Disconnect(ctx)

	// Проверка соединения
	if err := client.Ping(ctx, nil); err != nil {
		log.Fatalf("Failed to ping MongoDB: %v", err)
	}
	fmt.Println("Connected to MongoDB successfully")

	// Получение коллекции
	collection := client.Database("caller").Collection("call_tasks")

	objId, _ := primitive.ObjectIDFromHex("680f3e314817efa7fd23e15a")
	_, err = collection.UpdateOne(ctx, bson.M{"_id": objId}, bson.M{"$set": bson.M{"registryId": primitive.NewObjectID()}})

	filter := bson.M{
		"_id": objId,
	}

	callTask := types.CallTask{}
	err = collection.FindOne(ctx, filter).Decode(&callTask)
	if err != nil {
		log.Fatalf("Failed to find task: %v", err)
	}

}
