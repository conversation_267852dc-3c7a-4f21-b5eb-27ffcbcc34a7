package main

import (
	"context"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/mongodb"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/tg_caller"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/telegram"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	logger "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/logger/typed_logger"
	_ "net/http/pprof"

	_ "go.uber.org/automaxprocs"
)

const (
	SHUTDOWN_TIMEOUT = 30 * time.Second
)

func main() {
	var (
		ctx, cancelCtx           = signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
		ctxMongo, ctxMongoCancel = context.WithTimeout(context.Background(), time.Minute)
		log                      = logger.DefaultLogger
		tgProvider               tg_caller.ITelegramProvider
		tgRepo                   tg_caller.ITelegramRepo
		dialerClient             tg_caller.IDialerClient
		tgCallerDone             chan struct{}
		err                      error
	)

	log.Info("pid: %d", os.Getpid())

	defer func() {
		ctxMongoCancel()
		cancelCtx()
		timer := time.NewTimer(SHUTDOWN_TIMEOUT)
		for tgCallerDone != nil {
			select {
			case <-timer.C:
				log.Error("shutdown timeout")
				os.Exit(1)
			case <-tgCallerDone:
				tgCallerDone = nil
			}
		}
		log.Info("gracefully stopped")
		os.Exit(0)
	}()

	go func() {
		if errP := http.ListenAndServe("0.0.0.0:8080", nil); errP != nil {
			log.Error("%s", errP)
		}
	}()

	tgRepo, err = mongodb.NewClient(
		ctxMongo,
		config.Cfg.MongoUri.String(),
		nil,
	)
	if err != nil {
		log.TraceError(err)
		return
	}

	rmqConn, err := rabbitmq.NewConn(config.Cfg.RmqUri.String())
	if err != nil {
		log.TraceError(err)
		return
	}
	defer rmqConn.Close()

	// todo отправлять метрики куда надо вместо консоли
	tgMon := tg_caller.NewTgMonitoring(tg_caller.NewStdMetricStorage(os.Stdout))
	tgMon.Run(ctx)

	tgProvider = telegram.NewTelegramLPProvider(tgMon)

	dialerClient, err = telegram.NewDialerClient(rmqConn)
	if err != nil {
		log.TraceError(err)
		return
	}
	//defer dialerClient.Close()

	tgCaller := tg_caller.New(tgProvider, tgRepo, dialerClient, tgMon)
	tgCallerDone = make(chan struct{}, 1)
	go func() {
		defer close(tgCallerDone)
		defer cancelCtx()
		if errC := tgCaller.Run(ctx); errC != nil {
			log.TraceError(errC)
		}
	}()

	controller := tg_caller.NewController(tgCaller, rmqConn)
	err = controller.Run(ctx)
	if err != nil {
		log.TraceError(err)
		return
	}

	log.Info("service started")
	<-ctx.Done()
	log.Info("main context canceled, shutting down")
}
