package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/incoming"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/mongodb"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	logger "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/logger/typed_logger"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"

	_ "go.uber.org/automaxprocs"
)

const SHUTDOWN_TIMEOUT = time.Second * 30

func main() {
	var (
		ctx, cancelCtx                      = signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
		rmqConn                             *rabbitmq.Conn
		err                                 error
		log                                 = logger.DefaultLogger
		ctxDatabaseConn, cancelDatabaseConn = context.WithTimeout(ctx, time.Minute)
		activeTasks                         incoming.IServiceActiveTasks
		activeTasksDone                     chan struct{}
		teleIncomingDone                    chan struct{}
	)

	defer func() {
		cancelDatabaseConn()
		cancelCtx()
		timer := time.NewTimer(SHUTDOWN_TIMEOUT)
		for activeTasksDone != nil || teleIncomingDone != nil {
			select {
			case <-timer.C:
				log.Error("shutdown timeout")
				os.Exit(1)
			case <-activeTasksDone:
				activeTasksDone = nil
			case <-teleIncomingDone:
				teleIncomingDone = nil
			}
		}
		log.Info("gracefully stopped")
		os.Exit(0)
	}()

	log.Info("pid: %d", os.Getpid())

	// connect to rabbitmq
	rmqConn, err = rabbitmq.NewConn(config.Cfg.RmqUri.String())
	if err != nil {
		log.TraceError(err)
		return
	}

	// connect to db with campaigns (caller)
	databaseProvider, err := mongodb.NewClient(ctxDatabaseConn, config.Cfg.MongoUri.String(), nil)
	if err != nil {
		log.TraceError(err)
		return
	}

	// run incoming caller
	activeTasks, activeTasksDone, err = incoming.RunServiceActiveTasks(ctx, log, rmqConn, databaseProvider)
	if err != nil {
		log.TraceError(err)
		return
	}

	teleIncoming := incoming.TeleIncoming{
		Ctx:              ctx,
		Log:              log,
		DatabaseProvider: databaseProvider,
		TrunksProvider:   databaseProvider,
		ActiveTasks:      activeTasks,
		RndGen:           utils.NewRandomGenerator(ctx),
		QueueGen:         utils.NewSyncRoundRobin(ctx, config.Cfg.DialerConsumeConcurrency),
	}

	teleIncomingDone = make(chan struct{}, 1)
	go func() {
		defer close(teleIncomingDone)
		teleIncoming.Run(ctx)
	}()

	log.Info("service started")
	<-ctx.Done()
	log.Info("main context canceled, shutting down")
}
