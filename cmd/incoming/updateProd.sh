#!/bin/bash

TAG=$1
NAME="caller-go-incoming"

if [ -z $TAG ]; then
    printf "image tag undefined.\n"
    exit 1
fi

cd $(dirname -- "$(readlink -f -- "$BASH_SOURCE")")

docker pull registry.prod.kvint.io/kvint-core/kvint-caller-go/incoming:$TAG
docker stop $NAME
docker rm $NAME
docker run -d -p 5004:5004 --security-opt seccomp=unconfined --name=$NAME --cpus="2.0" --env-file ../prod.env --restart always registry.prod.kvint.io/kvint-core/kvint-caller-go/incoming:$1
