#!/bin/bash

TAG="develop"
NAME="caller-go-incoming"

cd $(dirname -- "$(readlink -f -- "$BASH_SOURCE")")

docker pull registry.prod.kvint.io/kvint-core/kvint-caller-go/incoming:$TAG
docker stop $NAME
docker rm $NAME
docker run -d -p 5004:5004 --security-opt seccomp=unconfined --name=$NAME --cpus="2.0" --env-file ../staging.env --restart always registry.prod.kvint.io/kvint-core/kvint-caller-go/incoming:$TAG