# About test

1. Used this library - https://www.npmjs.com/package/autocannon (request below in examples)
2. Launch campaign with dialer branch `go-dialer-develop-mock-calls-2`
3. Make trunk rule for launched campaign, insert campaign id to `parent_id` in trunk `prod3` like this:
```
{
			"numA": "*",
			"numB": "*",
			"_id": "5f676dnfhrkca5012323",
			"parent_id": "666fcf30290a5bc4a960a885",
			"dialplan": [
				{
					"step": 1,
					"action": "dial",
					"answer": "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
					"params": {
						"call_limit": 10,
						"timeout": 20
					}
				}
			]
		},
```

# Cleanup after tests

Commands for mongodb to clean documents after tests:
1. `db.sip_logs.deleteMany({ "num_a": "79148050700" })`

### Example of test (26.05.2024. Concurrent requests = 100, time = 5min, cpu limit = 2)

Command: `autocannon -c 100 -d 300 "http://*************:5004/inboundCall?secret_key=SF7Bbtsa28&call_uuid=&trunk_name=prod3&num_a=79148050700&server=1-00&num_b=79990559618&extension=8&sipcallid=23c02b0426b0f6401e68fa8a41324ce7@*************:5060&diversion=79990559618"`

Result:
Running 300s test @ http://*************:5004/inboundCall?secret_key=SF7Bbtsa28&call_uuid=&trunk_name=prod3&num_a=79148050700&server=1-00&num_b=79990559618&extension=8&sipcallid=23c02b0426b0f6401e68fa8a41324ce7@*************:5060&diversion=79990559618
100 connections

┌─────────┬────────┬────────┬────────┬────────┬───────────┬──────────┬─────────┐
│ Stat    │ 2.5%   │ 50%    │ 97.5%  │ 99%    │ Avg       │ Stdev    │ Max     │
├─────────┼────────┼────────┼────────┼────────┼───────────┼──────────┼─────────┤
│ Latency │ 103 ms │ 130 ms │ 257 ms │ 425 ms │ 138.27 ms │ 55.68 ms │ 3242 ms │
└─────────┴────────┴────────┴────────┴────────┴───────────┴──────────┴─────────┘
┌───────────┬───────┬───────┬────────┬────────┬────────┬─────────┬─────────┐
│ Stat      │ 1%    │ 2.5%  │ 50%    │ 97.5%  │ Avg    │ Stdev   │ Min     │
├───────────┼───────┼───────┼────────┼────────┼────────┼─────────┼─────────┤
│ Req/Sec   │ 402   │ 473   │ 737    │ 822    │ 721.8  │ 76.4    │ 342     │
├───────────┼───────┼───────┼────────┼────────┼────────┼─────────┼─────────┤
│ Bytes/Sec │ 74 kB │ 87 kB │ 136 kB │ 151 kB │ 133 kB │ 14.1 kB │ 62.9 kB │
└───────────┴───────┴───────┴────────┴────────┴────────┴─────────┴─────────┘

Req/Bytes counts sampled once per second.
# of samples: 300

217k requests in 300.63s, 39.8 MB read

### Example of test (26.05.2024. Concurrent requests = 200, time = 5min, cpu limit = 2)

Command: `autocannon -c 200 -d 300 "http://*************:5004/inboundCall?secret_key=SF7Bbtsa28&call_uuid=&trunk_name=prod3&num_a=79148050700&server=1-00&num_b=79990559618&extension=8&sipcallid=23c02b0426b0f6401e68fa8a41324ce7@*************:5060&diversion=79990559618"`

Result:
Running 300s test @ http://*************:5004/inboundCall?secret_key=SF7Bbtsa28&call_uuid=&trunk_name=prod3&num_a=79148050700&server=1-00&num_b=79990559618&extension=8&sipcallid=23c02b0426b0f6401e68fa8a41324ce7@*************:5060&diversion=79990559618
200 connections

┌─────────┬────────┬────────┬────────┬────────┬───────────┬──────────┬─────────┐
│ Stat    │ 2.5%   │ 50%    │ 97.5%  │ 99%    │ Avg       │ Stdev    │ Max     │
├─────────┼────────┼────────┼────────┼────────┼───────────┼──────────┼─────────┤
│ Latency │ 216 ms │ 254 ms │ 426 ms │ 561 ms │ 262.95 ms │ 75.86 ms │ 4067 ms │
└─────────┴────────┴────────┴────────┴────────┴───────────┴──────────┴─────────┘
┌───────────┬─────────┬────────┬────────┬────────┬────────┬─────────┬─────────┐
│ Stat      │ 1%      │ 2.5%   │ 50%    │ 97.5%  │ Avg    │ Stdev   │ Min     │
├───────────┼─────────┼────────┼────────┼────────┼────────┼─────────┼─────────┤
│ Req/Sec   │ 453     │ 571    │ 776    │ 829    │ 760.08 │ 63.8    │ 421     │
├───────────┼─────────┼────────┼────────┼────────┼────────┼─────────┼─────────┤
│ Bytes/Sec │ 83.4 kB │ 105 kB │ 143 kB │ 153 kB │ 140 kB │ 11.7 kB │ 77.5 kB │
└───────────┴─────────┴────────┴────────┴────────┴────────┴─────────┴─────────┘

Req/Bytes counts sampled once per second.
# of samples: 300

228k requests in 300.54s, 42 MB read