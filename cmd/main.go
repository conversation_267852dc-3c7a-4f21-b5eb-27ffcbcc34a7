package main

//import (
//	"context"
//	"fmt"
//	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
//	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
//	"go.mongodb.org/mongo-driver/bson"
//	"go.mongodb.org/mongo-driver/bson/primitive"
//	"go.mongodb.org/mongo-driver/event"
//	"go.mongodb.org/mongo-driver/mongo"
//	"go.mongodb.org/mongo-driver/mongo/options"
//	"time"
//)
//
//func main() {
//	monitor := &event.CommandMonitor{
//		Started: func(_ context.Context, evt *event.CommandStartedEvent) {
//			fmt.Printf("MongoDB command: %s\n", evt.Command)
//		},
//	}
//	mongoClient, err := mongo.Connect(context.Background(),
//		options.Client().ApplyURI("mongodb://kvint:<EMAIL>:27017/caller?authSource=admin"),
//		options.Client().SetMonitor(monitor),
//	)
//	if err != nil {
//		fmt.Println("failed to connect to mongo", err)
//	}
//
//	// тест запросов
//	//_, err = GetCleanEveryDayCampaignsId(mongoClient)
//
//	//GetTasks(mongoClient)
//	//GetTasksByStatus(mongoClient)
//
//	// индексы
//	//list, err := mongoClient.Database("caller").Collection("call_tasks").Indexes().List(context.Background())
//	//if err != nil {
//	//	fmt.Println("failed to list indexes", err)
//	//	return
//	//}
//	//indexes := make([]bson.M, 0)
//	//if err = list.All(context.Background(), &indexes); err != nil {
//	//	fmt.Println("failed to parse indexes", err)
//	//	return
//	//}
//}
//
//func GetCleanEveryDayCampaignsId(mongoClient *mongo.Client) (result []types.CampaignId, err error) {
//	collection := mongoClient.Database("caller").Collection("campaigns")
//	filter := bson.M{
//		"taskSettings.cleanEveryDay": true,
//		"callDirection":              types.CAMPAIGN_CALL_DIRECTION_OUT,
//	}
//
//	ids, err := collection.Distinct(context.Background(), "_id", filter)
//	if err != nil {
//		fmt.Println("failed query to mongo", err)
//		return
//	}
//
//	for _, id := range ids {
//		if oid, ok := id.(primitive.ObjectID); ok {
//			result = append(result, types.CampaignId(oid.Hex()))
//		} else {
//			fmt.Println("unexpected type")
//		}
//	}
//
//	return result, nil
//}
//
//func GetTasksByStatus(mongoClient *mongo.Client) (result []types.CampaignId, err error) {
//	collection := mongoClient.Database("caller").Collection("call_tasks")
//	filter := bson.M{
//		"status": types.TASK_STATUS_DELAY,
//	}
//
//	collection.FindOne(context.Background(), filter)
//
//	return result, nil
//}
//
//func GetTasks(mongoClient *mongo.Client) (result []types.CampaignId, err error) {
//	t := time.Now()
//	collection := mongoClient.Database("caller").Collection("call_tasks")
//	match := bson.M{
//		"campaignId": primitive.NewObjectID(),
//		"status":     types.TASK_STATUS_DELAY,
//		"isTest":     false,
//		"nextCallAt": bson.M{
//			"$lte": t,
//		},
//		"startDate": bson.M{
//			"$not": bson.M{
//				"$gte": t,
//			},
//		},
//	}
//	pipeline := mongo.Pipeline{
//		bson.D{
//			bson.E{
//				Key:   "$match",
//				Value: match,
//			},
//		},
//		bson.D{
//			bson.E{
//				Key: "$sort",
//				Value: bson.D{
//					bson.E{
//						Key:   "priority",
//						Value: -1,
//					},
//					bson.E{
//						Key:   "nextTz",
//						Value: -1,
//					},
//					bson.E{
//						Key:   "nextCallAt",
//						Value: 1,
//					},
//					bson.E{
//						Key:   "_id",
//						Value: 1,
//					},
//				},
//			},
//		},
//		bson.D{
//			bson.E{
//				Key: "$lookup",
//				Value: bson.M{
//					"from":         config.Cfg.MongoCallResultCollection,
//					"localField":   "_id",
//					"foreignField": "callTaskId",
//					"as":           "callResults",
//				},
//			},
//		},
//	}
//
//	cur, err := collection.Aggregate(context.Background(), pipeline)
//	if err != nil {
//		return
//	}
//	for cur.Next(context.Background()) {
//		var task types.CallTask
//		if errDecode := cur.Decode(&task); errDecode != nil {
//			break
//		} else {
//			break
//		}
//	}
//	cur.Close(context.Background())
//
//	return result, nil
//}

func main() {
	//data, err := os.ReadFile("/home/<USER>/GolandProjects/kvint/kvint-caller-go/cmd/qwer")
	//if err != nil {
	//	log.Fatalf("failed to read file: %v", err)
	//}
	//fmt.Printf("%s", string(data))
	//CampaignProjection := mongodb.GetProjection(types.Campaign{})
	//
	//fmt.Printf("%+v\n", CampaignProjection)

	//log := logger.DefaultLogger
	//
	//rmqConn, err := rabbitmq.NewConn("amqp://admin:LnRvnsoF8eDf8@*************:5672/")
	//if err != nil {
	//	log.TraceError(err)
	//	return
	//}
	//publisher, err := rabbitmq.NewPublisher(rmqConn)
	//if err != nil {
	//	log.TraceError(err)
	//	return
	//}
	//defer publisher.Close()
	//publisher.NotifyReturn(func(r rabbitmq.Return) {
	//	log.Error("queue %s or exchange %s not exist", r.RoutingKey, r.Exchange)
	//})
	//
	//msg := struct {
	//	text string
	//}{text: "hello"}
	//
	//bytes, err := json.Marshal(msg)
	//if err != nil {
	//	return err
	//}
	//gzipped, err := utils.GzipData(bytes)
	//if err != nil {
	//	return err
	//}
	//queue := fmt.Sprintf("%s%s", PostResult_QueuePrefix, pr.Project)
	//return publisher.Publish(
	//	gzipped,
	//	[]string{queue},
	//	rabbitmq.WithPublishOptionsContentEncoding("utf-8"),
	//	rabbitmq.WithPublishOptionsContentType("application/data"),
	//	rabbitmq.WithPublishOptionsCorrelationID(string(pr.CallId)),
	//	rabbitmq.WithPublishOptionsMandatory,
	//)
}
