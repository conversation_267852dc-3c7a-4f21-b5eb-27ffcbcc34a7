{"namespace": "caller.call_tasks", "indexFilterSet": false, "parsedQuery": {"$and": [{"campaignId": {"$eq": new ObjectId("67ee9f4ebfb43749828d0361")}}, {"isTest": {"$eq": false}}, {"lastCallStatus": {"$eq": "request_recall"}}, {"status": {"$eq": "delay"}}, {"nextCallAt": {"$lte": new ISODate("2025-04-03T14:46:38.897Z")}}, {"nextTz": {"$in": [new NumberInt("1"), new NumberInt("2"), new NumberInt("3")]}}, {"startDate": {"$not": {"$gte": new ISODate("2025-04-03T14:46:38.897Z")}}}]}, "queryHash": "D854EEBE", "planCacheKey": "7C610A83", "optimizedPipeline": true, "maxIndexedOrSolutionsReached": false, "maxIndexedAndSolutionsReached": false, "maxScansToExplodeReached": false, "winningPlan": {"queryPlan": {"stage": "EQ_LOOKUP", "planNodeId": new NumberInt("4"), "foreignCollection": "caller.records", "localField": "_id", "foreignField": "callTaskId", "asField": "callResults", "strategy": "IndexedLoopJoin", "indexName": "callTaskId_1_end_ts_1", "indexKeyPattern": {"callTaskId": new NumberInt("1"), "end_ts": new NumberInt("1")}, "inputStage": {"stage": "SORT", "planNodeId": new NumberInt("3"), "sortPattern": {"priority": new NumberInt("-1"), "nextTz": new NumberInt("-1"), "nextCallAt": new NumberInt("1"), "_id": new NumberInt("1")}, "memLimit": new NumberInt("104857600"), "type": "simple", "inputStage": {"stage": "FETCH", "planNodeId": new NumberInt("2"), "inputStage": {"stage": "IXSCAN", "planNodeId": new NumberInt("1"), "keyPattern": {"campaignId": new NumberLong("1"), "status": new NumberLong("1"), "isTest": new NumberLong("1"), "priority": new NumberLong("-1"), "nextTz": new NumberLong("-1"), "nextCallAt": new NumberLong("1"), "_id": new NumberLong("1"), "lastCallStatus": new NumberLong("1"), "startDate": new NumberLong("1")}, "indexName": "campaignId_1_status_1_isTest_1_priority_-1_nextTz_-1_nextCallAt_1__id_1_lastCallStatus_1_startDate_1", "isMultiKey": true, "multiKeyPaths": {"campaignId": [], "status": [], "isTest": [], "priority": [], "nextTz": ["nextTz"], "nextCallAt": [], "_id": [], "lastCallStatus": [], "startDate": []}, "isUnique": false, "isSparse": false, "isPartial": false, "indexVersion": new NumberInt("2"), "direction": "forward", "indexBounds": {"campaignId": ["[ObjectId('67ee9f4ebfb43749828d0361'), ObjectId('67ee9f4ebfb43749828d0361')]"], "status": ["[\"delay\", \"delay\"]"], "isTest": ["[false, false]"], "priority": ["[MaxKey, MinKey]"], "nextTz": ["[3, 3]", "[2, 2]", "[1, 1]"], "nextCallAt": ["[new Date(-9223372036854775808), new Date(1743691598897)]"], "_id": ["[MinKey, MaxKey]"], "lastCallStatus": ["[\"request_recall\", \"request_recall\"]"], "startDate": ["[MinKey, new Date(1743691598897))", "(new Date(9223372036854775807), MaxKey]"]}}}}}, "slotBasedPlan": {"slots": "$$RESULT=s70 env: { s1 = TimeZoneDatabase(America/Goose_Bay...America/Detroit) (timeZoneDB), s3 = 1743766666010 (NOW), s2 = Nothing (SEARCH_META) }", "stages": "[4] mkbson s70 s22 [] drop [callResults = s69] true false \n[4] nlj [s22] [s45] \n    left \n        [4] nlj [s22] [s22] \n            left \n                [3] sort [s31, s34, s37, s40] [desc, desc, asc, asc] [s22] \n                [3] project [s40 = fillEmpty (s39, undefined)] \n                [3] traverse s39 s38 s27 {if (s38 <=> s39 < 0, s38, s39)} {} \n                from \n                    [3] project [s37 = fillEmpty (s36, undefined)] \n                    [3] traverse s36 s35 s26 {if (s35 <=> s36 < 0, s35, s36)} {} \n                    from \n                        [3] project [s34 = fillEmpty (s33, undefined)] \n                        [3] traverse s33 s32 s25 {if (s32 <=> s33 > 0, s32, s33)} {} \n                        from \n                            [3] project [s31 = fillEmpty (s30, undefined)] \n                            [3] traverse s30 s29 s24 {if (s29 <=> s30 > 0, s29, s30)} {} \n                            from \n                                [3] project [s28 = isArray (s24) <=> false + isArray (s25) <=> false + isArray (s26) <=> false + isArray (s27) <=> false <= 1 || fail ( 2 ,cannot sort with keys that are parallel arrays)] \n                                [3] project [s24 = fillEmpty (getField (s22, \"priority\"), null), s25 = fillEmpty (getField (s22, \"nextTz\"), null), s26 = fillEmpty (getField (s22, \"nextCallAt\"), null), s27 = fillEmpty (getField (s22, \"_id\"), null)] \n                                [2] nlj [] [s8, s4, s5, s6, s7] \n                                    left \n                                        [1] unique [s8] \n                                        [1] filter {isRecordId (s8)} \n                                        [1] lspool sp1 [s8, s4, s5, s6, s7] {! isRecordId (s8)} \n                                        [1] union [s8, s4, s5, s6, s7] [\n                                            [s13, s9, s10, s11, s12] [1] project [s9 = Nothing, s10 = Nothing, s11 = Nothing, s12 = Nothing, s13 = KS(6467EE9F4EBFB43749828D03613C64656C6179006E0FD4F97800000000000000000A3C726571756573745F726563616C6C000A0104)] \n                                            [1] limit 1 \n                                            [1] coscan , \n                                            [s21, s14, s15, s16, s17] [1] nlj [] [s19, s15, s17] \n                                                left \n                                                    [1] sspool sp1 [s19, s15, s17] \n                                                right \n                                                    [1] chkbounds s16 s18 s21 \n                                                    [1] nlj [s15, s17] [s20] \n                                                        left \n                                                            [1] project [s15 = \"campaignId_1_status_1_isTest_1_priority_-1_nextTz_-1_nextCallAt_1__id_1_lastCallStatus_1_startDate_1\", s17 = {\"campaignId\" : 1, \"status\" : 1, \"isTest\" : 1, \"priority\" : -1, \"nextTz\" : -1, \"nextCallAt\" : 1, \"_id\" : 1, \"lastCallStatus\" : 1, \"startDate\" : 1}, s20 = s19] \n                                                            [1] limit 1 \n                                                            [1] coscan \n                                                        right \n                                                            [1] ixseek s20 none s16 s18 s14 [] @\"dcef00e1-2ffc-4b5b-996c-28734f2020c4\" @\"campaignId_1_status_1_isTest_1_priority_-1_nextTz_-1_nextCallAt_1__id_1_lastCallStatus_1_startDate_1\" true \n                                                        \n                                                    \n                                                \n                                            \n                                       ] \n                                    right \n                                        [2] limit 1 \n                                        [2] seek s8 s22 s23 s4 s5 s6 s7 [] @\"dcef00e1-2ffc-4b5b-996c-28734f2020c4\" true false \n                                    \n                                \n                            in \n                                [3] project [s29 = s24] \n                                [3] limit 1 \n                                [3] coscan \n                            \n                        in \n                            [3] project [s32 = s25] \n                            [3] limit 1 \n                            [3] coscan \n                        \n                    in \n                        [3] project [s35 = s26] \n                        [3] limit 1 \n                        [3] coscan \n                    \n                in \n                    [3] project [s38 = s27] \n                    [3] limit 1 \n                    [3] coscan \n                \n            right \n                [4] project [s45 = if (getArraySize (s44) > 0, s44, [null])] \n                [4] group [] [s44 = addToSet (s42)] \n                [4] unwind s42 s43 s41 true \n                [4] project [s41 = getField (s22, \"_id\")] \n                [4] limit 1 \n                [4] coscan \n            \n        \n    right \n        [4] limit 1 \n        [4] union [s69] [\n            [s67] [4] project [s67 = getElement (s66, 0)] \n            [4] group [] [s66 = addToArrayCapped (s59, 104857600)] \n            [4] nlj [s59] [s59] \n                left \n                    [4] nlj [] [s56, s58, s54, s57, s55] \n                        left \n                            [4] nlj [s54, s55] [s52, s53] \n                                left \n                                    [4] nlj [] [s46] \n                                        left \n                                            [4] unwind s46 s47 s45 true \n                                            [4] limit 1 \n                                            [4] coscan \n                                        right \n                                            [4] project [s52 = ks (1, 0, s51, 1), s53 = ks (1, 0, s51, 2), s54 = \"callTaskId_1_end_ts_1\", s55 = {\"callTaskId\" : 1, \"end_ts\" : 1}] \n                                            [4] union [s51] [\n                                                [s48] [4] cfilter {isNull (s46)} \n                                                [4] project [s48 = undefined] \n                                                [4] limit 1 \n                                                [4] coscan , \n                                                [s49] [4] filter {isArray (s46) && ! isMember (s49, s45)} \n                                                [4] project [s49 = fillEmpty (getElement (s46, 0), undefined)] \n                                                [4] limit 1 \n                                                [4] coscan , \n                                                [s50] [4] project [s50 = s46] \n                                                [4] limit 1 \n                                                [4] coscan \n                                           ] \n                                        \n                                    \n                                right \n                                    [4] ixseek s52 s53 s57 s56 s58 [] @\"61d37848-7c4f-4cf3-b3df-89d7a2de13c1\" @\"callTaskId_1_end_ts_1\" true \n                                \n                            \n                        right \n                            [4] limit 1 \n                            [4] seek s56 s59 s60 s58 s54 s57 s55 [] @\"61d37848-7c4f-4cf3-b3df-89d7a2de13c1\" true false \n                        \n                    \n                right \n                    [4] limit 1 \n                    [4] filter {isMember (s65, s45)} \n                    [4] nlj [] [s61] \n                        left \n                            [4] project [s61 = fillEmpty (getField (s59, \"callTaskId\"), null)] \n                            [4] limit 1 \n                            [4] coscan \n                        right \n                            [4] branch {isArray (s61)} [s65] \n                            [s64] [4] union [s64] [\n                                [s62] [4] unwind s62 s63 s61 true \n                                [4] limit 1 \n                                [4] coscan , \n                                [s61] [4] limit 1 \n                                [4] coscan \n                           ] \n                            [s61] [4] limit 1 \n                            [4] coscan \n                        \n                    \n                \n            , \n            [s68] [4] project [s68 = []] \n            [4] limit 1 \n            [4] coscan \n       ] \n    \n"}}, "rejectedPlans": []}