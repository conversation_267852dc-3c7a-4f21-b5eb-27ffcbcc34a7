# Нагрузочное тестирование main коллера

1. <h4>Сборка консольной утилиты</h4>

    1. <h5>Подставить приватный ключ</h5>

        Утилите требуется ssh соединение с препрод сервером, поэтому перед сборкой нужно добавить приватный ключ в файл [/perf_test/id_rsa.pem](/perf_test/id_rsa.pem)
    
    2. <h5>Сборка</h5>

        `go build cmd/perf_test/main.go -o perftest`

2. <h4>Запуск кампаний</h4>

    Запустить на препроде минимум 3 кампании одновременно

        - Количество линий минимум 1000
        - Минимум по 1000000 заданий в каждой кампании
        - От 20 любых филдов в каждом задании
        - Время между перезвонами можно сделать минимальное
        - Дайлер на ветке go-dialer-develop-mock-calls-2, бот любой. По состоянию на 10.02.2025 уже есть три такие кампании с id 110, 100 и 158

3. <h4>Запуск мониторинга</h4>

    1. <h5>Запустить консольную утилиту</h5>

        Для записи статистики по main коллеру

        - Windows

        `.\perftest.exe monitorProc`

        - Linux

        `./perftest monitorProc`
    
    2. <h5>Остановить запись статистики</h5>
    
        По истечению достаточного времени (обычно от 5 до 60 минут). По нажатию `ctrl+c` запись статистики останавливается, и открываются графики в браузере

    3. <h5>Проанализировать результат</h5>
    
        В сравнении с последним успешным результатом, либо с эталонным, на наличие аномалий/отклонений (высокие или низкие пиковые или средние значения, нестабильный рваный график нагрузок, и т.д. Можно обсудить с разработчиком)

4. <h4>Очистка данных</h4>

    В процессе теста создаются записи результатов звонков и обновляются записи заданий в базе данных. Под каждый такой тест создавать задания заново долго и дорого по памяти, так что в рамках консольной утилиты реализована команда для сброса данных.

    - Windows

    `.\perftest.exe resetDB 667ae1c7ebcb8d247f01d807 6659b4aae37b946a93b6c778 66f261cafe1cd387d10a9e55`

    - Linux

    `./perftest resetDB 667ae1c7ebcb8d247f01d807 6659b4aae37b946a93b6c778 66f261cafe1cd387d10a9e55`

5. <h4>Тест кейсы</h4>

    - Остановка и запуск одной кампании
    - Остановка и запуск всех кампаний
