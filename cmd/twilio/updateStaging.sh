#!/bin/bash

TAG="develop"
NAME="caller-go-twilio"

cd $(dirname -- "$(readlink -f -- "$BASH_SOURCE")")

docker pull registry.prod.kvint.io/kvint-core/kvint-caller-go/twilio:$TAG
docker stop $NAME
docker rm $NAME
docker run -d -p 50002:50002 --security-opt seccomp=unconfined --name=$NAME --cpus="2.0" --env-file ../agentIncomingStaging.env --restart always registry.prod.kvint.io/kvint-core/kvint-caller-go/twilio:$TAG