package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/twilio"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/mongodb"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	logger "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/logger/typed_logger"
)

func main() {
	var log = logger.DefaultLogger
	log.Info("pid: %dn", os.Getpid())
	ctx, cancel := context.WithCancel(context.Background())

	rmqConn, err := rabbitmq.NewConn(config.Cfg.RmqUri.String())
	if err != nil {
		log.TraceError(err)
		return
	}
	defer rmqConn.Close()

	ctxProviderConn, cancelProviderConn := context.WithTimeout(context.Background(), time.Minute)
	defer cancelProviderConn()
	databaseProvider, err := mongodb.NewClient(ctxProviderConn, config.Cfg.MongoUri.String(), nil)
	if err != nil {
		log.TraceError(err)
		return
	}

	publisher, err := rabbitmq.NewPublisher(
		rmqConn,
		rabbitmq.WithPublisherOptionsLogging,
	)
	if err != nil {
		log.TraceError(err)
		return
	}
	defer publisher.Close()
	publisher.NotifyReturn(func(r rabbitmq.Return) {
		log.Error("queue not exist %s", r.RoutingKey)
	})

	// Прием звонков от твилио
	serviceActiveTasks := twilio.RunServiceActiveTasks(ctx, rmqConn, publisher)
	go twilio.RunTwilioService(ctx, databaseProvider, publisher, serviceActiveTasks)

	log.Info("Awaiting signal")
	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)
	sig := <-sigs
	cancel()
	log.Info("received signal - %s", sig)
}
