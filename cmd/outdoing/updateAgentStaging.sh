#!/bin/bash

TAG="develop"
NAME="caller-go-outdoing"

cd $(dirname -- "$(readlink -f -- "$BASH_SOURCE")")

docker pull registry.prod.kvint.io/kvint-core/kvint-caller-go/outdoing:$TAG
docker stop $NAME
docker rm $NAME
docker run -d -p 8081:8080 --security-opt seccomp=unconfined --name=$NAME --cpus="2.0" --memory="8GB" --env-file ../agentOutgoingStaging.env --restart always --log-opt max-size=1g registry.prod.kvint.io/kvint-core/kvint-caller-go/outdoing:$TAG