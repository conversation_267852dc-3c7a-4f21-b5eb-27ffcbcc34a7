package main

import (
	"context"
	"fmt"
	"log"
	"net/http"

	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/jivo"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/mongodb"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
)

var logger = log.Default()

func main() {
	rmqConn, err := rabbitmq.NewConn(config.Cfg.RmqUri.String())
	if err != nil {
		logger.Panic(err)
	}
	defer rmqConn.Close()
	dialerRPC, err := jivo.NewDialerRPC(fmt.Sprintf("%s%s", config.Cfg.Jivo.DialerQueuePrefix, config.Cfg.Jivo.CampaignId), rmqConn)
	if err != nil {
		logger.Panic(err)
	}

	ctx := context.Background()
	databaseProvider, err := mongodb.NewClient(
		ctx,
		config.Cfg.MongoUri.String(),
		nil,
	)
	if err != nil {
		logger.Panic(err)
	}

	campaignCh := make(chan chan types.Campaign)
	go jivo.CampaignLoop(ctx, databaseProvider, types.CampaignId(config.Cfg.Jivo.CampaignId), campaignCh)

	http.HandleFunc(
		fmt.Sprintf("/%s", config.Cfg.Jivo.BotId),
		jivo.HandleEvent(
			ctx,
			campaignCh,
			dialerRPC,
			databaseProvider,
		),
	)
	err = http.ListenAndServe(fmt.Sprintf(":%s", config.Cfg.Jivo.Port), nil)
	if err != nil {
		logger.Panic(err)
	}
}
