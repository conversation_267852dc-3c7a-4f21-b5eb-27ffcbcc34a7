server {
    root /var/www/html;
    index index.html;
    client_max_body_size 32m;
    server_name web-audio.staging.infra.kvint.io;

    location ~/dialer/(?<extaddr>([^/]*))/(?<extport>([^/]*))/(?<oldurl>([^:]+)) {
        proxy_pass http://$extaddr:$extport/$oldurl;
        proxy_set_header Host $host;

        proxy_http_version 1.1;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header   X-Real-IP        $remote_addr;
    }
}
