image: docker:20.10.21

variables:
  DOCKER_HOST: tcp://localhost:2376
  DOCKER_TLS_CERTDIR: ""

services:
  - name: docker:20.10.21-dind
    command: ["--mtu=1450","--tls=false","--host=tcp://0.0.0.0:2376"]

stages:
  - lint
  - test
  - build

build:
  stage: build
  rules:
    - if: $CI_COMMIT_BRANCH == "develop" || $CI_COMMIT_BRANCH == "master"
      variables:
        TAG: $CI_COMMIT_BRANCH
    - if: $CI_COMMIT_TAG != null
      variables:
        TAG: $CI_COMMIT_TAG
  variables:
    OUTDOING_IMG: $CI_REGISTRY_IMAGE/outdoing:$TAG
    INCOMING_IMG: $CI_REGISTRY_IMAGE/incoming:$TAG
    TWILIO_IMG: $CI_REGISTRY_IMAGE/twilio:$TAG
    JIVO_IMG: $CI_REGISTRY_IMAGE/jivo:$TAG
    WEB_IMG: $CI_REGISTRY_IMAGE/web:$TAG
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build --tag $OUTDOING_IMG --file cmd/outdoing/Dockerfile .
    - docker build --tag $INCOMING_IMG --file cmd/incoming/Dockerfile .
    - docker build --tag $TWILIO_IMG --file cmd/twilio/Dockerfile .
    - docker build --tag $JIVO_IMG --file cmd/jivo/Dockerfile .
    - docker build --tag $WEB_IMG --file cmd/web/Dockerfile .
    - docker push $OUTDOING_IMG
    - docker push $INCOMING_IMG
    - docker push $TWILIO_IMG
    - docker push $JIVO_IMG
    - docker push $WEB_IMG

golangci_lint:
  stage: lint
  image: golangci/golangci-lint:v1.58.2
  script:
    - golangci-lint run -v

unit_tests:
  image: golang:1.21.6-bullseye
  stage: test
  script:
    - /usr/local/go/bin/go test -race --timeout 5m -v ./...
