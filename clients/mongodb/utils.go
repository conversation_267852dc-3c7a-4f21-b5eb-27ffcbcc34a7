package mongodb

import (
	"reflect"
	"strings"
	"time"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func GetProjection(val any) bson.M {
	projection := bson.M{}
	reflectType := utils.ReflectToType(val)
	if reflectType == nil {
		return projection
	}
	for i := 0; i < reflectType.NumField(); i++ {
		var path []string
		field := reflectType.Field(i)
		if fieldValues := strings.Split(field.Tag.Get("bson"), ","); len(fieldValues) > 0 && fieldValues[0] != "" {
			path = append(path, fieldValues[0])
		} else if len(fieldValues) == 0 {
			continue
		}
		type_ := utils.ReflectToType(reflect.Zero(field.Type).Interface())
		if type_ == nil {
			if len(path) > 0 {
				projection[strings.Join(path, ".")] = 1
			}
			continue
		}
		kind := type_.Kind()
		switch {
		case kind == reflect.Struct && type_ != reflect.TypeOf(time.Time{}):
			for k, v := range GetProjection(reflect.Zero(type_).Interface()) {
				if k == "" {
					continue
				}
				if !field.Anonymous {
					k = strings.Join(append(path, k), ".")
				}
				projection[k] = v
			}
		case len(path) > 0:
			projection[strings.Join(path, ".")] = 1
		}
	}
	return projection
}
