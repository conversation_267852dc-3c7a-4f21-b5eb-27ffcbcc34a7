package mongodb

import (
	"context"
	"fmt"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/tg_caller/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	"go.mongodb.org/mongo-driver/bson"
)

func (provider Client) GetBotInfoList(ctx context.Context) ([]*types.BotInfo, error) {
	collection := provider.mongoClient.Database(config.Cfg.Mongo.MongoDbName).Collection(config.Cfg.MongoCampaignCollection)
	filter := bson.M{
		//"isActive": true,
		"tgToken": bson.M{"$ne": nil},
	}

	cur, err := collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	log := log.QueryId(fmt.Sprintf("%d", cur.ID()))
	log.Debug("start mongodb query GetBotInfoList: %+v", filter)

	botInfoList := make([]*types.BotInfo, 0, 1)
	for cur.Next(ctx) {
		var campaign types.Campaign
		if errDecode := cur.Decode(&campaign); errDecode != nil {
			log.Error("%s", errDecode)
		} else {
			botInfoList = append(botInfoList, &types.BotInfo{
				CampaignId: string(campaign.Id),
				BuildId:    string(campaign.BuildId),
				Token:      campaign.TgToken,
			})
		}
	}

	log.Debug("end mongodb query GetBotInfoList result size: %d", len(botInfoList))
	return botInfoList, err
}

func (provider Client) SaveSessionLogs(campaignId string, chatId int64, sessionId string, logs []types.TgSessionLog) error {
	// покачто не нужно сохранять в бд т.к. бот тоже сохраняет логи
	return nil
}
