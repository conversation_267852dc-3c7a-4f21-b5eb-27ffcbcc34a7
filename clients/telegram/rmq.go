package telegram

import (
	"encoding/json"
	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

func RmqConsumerAutoAck[T any](
	conn *rabbitmq.Conn,
	queueListen string,
	handler func(result T, err error),
) (*rabbitmq.Consumer, error) {
	consumer, err := rabbitmq.NewConsumer(
		conn,
		func(d rabbitmq.Delivery) rabbitmq.Action {
			var data T
			raw, err := utils.UnzipData(d.Body)
			if err != nil {
				handler(data, err)
				return rabbitmq.NackDiscard
			}
			err = json.Unmarshal(raw, &data)
			if err != nil {
				handler(data, err)
				return rabbitmq.NackDiscard
			}

			handler(data, nil)
			return rabbitmq.Ack
		},
		queueListen,
		rabbitmq.WithConsumerOptionsQueueDurable,
	)
	return consumer, err
}

func RmqConsumerAutoAckNozip[T any](
	conn *rabbitmq.Conn,
	queueListen string,
	handler func(result T, err error),
) (*rabbitmq.Consumer, error) {
	consumer, err := rabbitmq.NewConsumer(
		conn,
		func(d rabbitmq.Delivery) rabbitmq.Action {
			var data T
			err := json.Unmarshal(d.Body, &data)
			if err != nil {
				handler(data, err)
				return rabbitmq.NackDiscard
			}

			handler(data, nil)
			return rabbitmq.Ack
		},
		queueListen,
		rabbitmq.WithConsumerOptionsQueueDurable,
	)
	return consumer, err
}
