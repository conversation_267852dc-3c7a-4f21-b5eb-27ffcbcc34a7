package telegram

import (
	"context"
	"fmt"
	"sync"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/tg_caller/types"
)

const FinishMessage = "finish"

type DialerClientMock struct {
	ctx           context.Context
	chInMap       map[string]chan *types.DialerTGMessageIn
	muChIn        *sync.RWMutex
	chCallResults chan *types.DialerResultResponse
	chOut         chan *types.DialerTGMessageOut
	sessionMap    map[string]*DialerSessionMock
	muSession     *sync.RWMutex
	CountTestMsgs int
}

type DialerSessionMock struct {
	ReplyTo string
}

func NewDialerClientMock(ctx context.Context) *DialerClientMock {
	client := &DialerClientMock{
		ctx:        ctx,
		chInMap:    make(map[string]chan *types.DialerTGMessageIn),
		muChIn:     &sync.RWMutex{},
		sessionMap: make(map[string]*DialerSessionMock),
		muSession:  &sync.RWMutex{},
		chOut:      make(chan *types.DialerTGMessageOut, 10),
	}

	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			case msg, ok := <-client.chOut:
				if !ok {
					return
				}
				fmt.Printf("message sent to dialer %+v\n", msg)
				if msg.Func == FuncDialingTask {
					client.muSession.Lock()
					client.sessionMap[msg.SessionId] = &DialerSessionMock{ReplyTo: msg.ReplyTo}
					client.muSession.Unlock()
					client.muChIn.RLock()
					client.chInMap[msg.ReplyTo] <- &types.DialerTGMessageIn{
						Func:        FuncCreateSession,
						DialerQueue: "queue1",
						SessionId:   msg.SessionId,
					}
					client.muChIn.RUnlock()
				} else if msg.Func == FuncCloseSession {
					client.muSession.Lock()
					client.sessionMap[msg.SessionId] = &DialerSessionMock{ReplyTo: msg.ReplyTo}
					client.muSession.Unlock()
					client.muChIn.RLock()
					client.chInMap[msg.ReplyTo] <- &types.DialerTGMessageIn{
						Func:        FuncCloseSession,
						DialerQueue: "queue1",
						SessionId:   msg.SessionId,
					}
					client.muChIn.RUnlock()
				} else {
					client.muSession.RLock()
					dialerSession, ok := client.sessionMap[msg.SessionId]
					client.muSession.RUnlock()
					if !ok {
						panic("dialer mock error: couldn`t find session  " + msg.SessionId)
					}

					if msg.Text == FinishMessage {
						client.muChIn.RLock()
						client.chInMap[dialerSession.ReplyTo] <- &types.DialerTGMessageIn{
							IsFinalState: true,
							SessionId:    msg.SessionId,
						}
						client.muChIn.RUnlock()
					} else {
						client.muChIn.RLock()
						client.chInMap[dialerSession.ReplyTo] <- &types.DialerTGMessageIn{
							Func:      FuncHandleMessage,
							Text:      "(generated_text)" + msg.Text,
							SessionId: msg.SessionId,
						}
						client.muChIn.RUnlock()
					}
				}
			}
		}
	}()

	go func() {
		<-ctx.Done()
		close(client.chOut)
	}()

	return client
}

func (d *DialerClientMock) SendTask(message *types.DialerTGMessageOut, queue string) error {
	d.chOut <- message
	fmt.Printf("[dialer mock] sent task to dialer queue %s, msg %+v\n", queue, message)
	return nil
}
func (d *DialerClientMock) SendMessage(message *types.DialerTGMessageOut, queue string) error {
	if d.ctx.Err() != nil {
		return nil
	}
	d.chOut <- message
	fmt.Printf("[dialer mock] sent message to dialer queue %s, msg %+v\n", queue, message)
	return nil
}
func (d *DialerClientMock) RunConsumer(ctx context.Context, queue string) (<-chan *types.DialerTGMessageIn, error) {
	ch := make(chan *types.DialerTGMessageIn, 10)
	d.muChIn.Lock()
	d.chInMap[queue] = ch
	d.muChIn.Unlock()

	go func() {
		<-ctx.Done()
	}()

	fmt.Printf("[dialer mock] consumer started for queue %s", queue)
	return ch, nil
}

func (d *DialerClientMock) ConsumeResults(ctx context.Context) (chan *types.DialerResultResponse, error) {
	// todo
	d.chCallResults = make(chan *types.DialerResultResponse, 10)
	return d.chCallResults, nil
}
