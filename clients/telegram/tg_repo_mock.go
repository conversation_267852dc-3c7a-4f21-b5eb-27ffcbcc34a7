package telegram

import (
	"context"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/tg_caller/types"
	"sync"
)

type TelegramRepoMock struct {
	mapBotInfo []*types.BotInfo
	// key = sessionId
	logs        map[string][]types.TgSessionLog
	lMu         *sync.RWMutex
	sessionChat map[string]int64
	scMu        *sync.RWMutex
}

func NewTelegramRepoMock(mapBotInfo []*types.BotInfo) *TelegramRepoMock {
	return &TelegramRepoMock{
		mapBotInfo:  mapBotInfo,
		logs:        make(map[string][]types.TgSessionLog),
		lMu:         &sync.RWMutex{},
		sessionChat: make(map[string]int64),
		scMu:        &sync.RWMutex{},
	}
}

func (m *TelegramRepoMock) GetBotInfoList(ctx context.Context) ([]*types.BotInfo, error) {
	return m.mapBotInfo, nil
}

func (m *TelegramRepoMock) SaveSessionLogs(campaignId string, chatId int64, sessionId string, logs []types.TgSessionLog) error {
	m.lMu.Lock()
	m.logs[sessionId] = logs
	m.lMu.Unlock()

	m.scMu.Lock()
	m.sessionChat[sessionId] = chatId
	m.scMu.Unlock()
	return nil
}

// GetLogs returns logs and sessionChat map
func (m *TelegramRepoMock) GetLogs() (map[string][]types.TgSessionLog, map[string]int64) {
	m.lMu.RLock()
	newLogs := make(map[string][]types.TgSessionLog)
	for k, v := range m.logs {
		newV := make([]types.TgSessionLog, len(v))
		_ = copy(newV, v)
		newLogs[k] = newV
	}
	m.lMu.RUnlock()

	m.scMu.RLock()
	newSCMap := make(map[string]int64)
	for k, v := range m.sessionChat {
		newSCMap[k] = v
	}
	m.scMu.RUnlock()

	return newLogs, newSCMap
}
