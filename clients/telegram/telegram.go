package telegram

import (
	"context"
	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/tg_caller/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	logger "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/logger/typed_logger"
	"golang.org/x/time/rate"
	"sync"
	"time"
)

type TelegramLongPollingBot struct {
	ctx     context.Context
	api     *tgbotapi.BotAPI
	chIn    chan *types.TGMessageIn
	chOut   chan *types.TGMessageOut
	limiter *rate.Limiter
}

func NewTelegramLongPollingBot(ctx context.Context, api *tgbotapi.BotAPI) *TelegramLongPollingBot {
	bot := &TelegramLongPollingBot{
		ctx:     ctx,
		api:     api,
		chIn:    make(chan *types.TGMessageIn, 10),
		chOut:   make(chan *types.TGMessageOut, 10),
		limiter: rate.NewLimiter(rate.Limit(config.Cfg.TgCaller.LimitSendMsgsPerSecond), 1),
	}

	return bot
}

func (tb *TelegramLongPollingBot) GetMessagesCh() (<-chan *types.TGMessageIn, chan<- *types.TGMessageOut) {
	return tb.chIn, tb.chOut
}

type TelegramLPProvider struct {
	log *logger.Logger
	mon ITgMonitoring
}

type ITgMonitoring interface {
	BotError(botID int64, err error)
	BotStarted(botID int64)
	BotStopped(botID int64)
	MessageReceived(botID int64)
	MessageSent(botID int64)
}

func NewTelegramLPProvider(mon ITgMonitoring) *TelegramLPProvider {
	return &TelegramLPProvider{
		log: &logger.DefaultLogger,
		mon: mon,
	}
}

// RunBot запускает бота с указанным токеном, возвращает каналы для приема/отправки сообщений, ID бота и ошибку
func (tp *TelegramLPProvider) RunBot(ctx context.Context, token string) (<-chan *types.TGMessageIn, chan<- *types.TGMessageOut, int64, error) {
	// Создаем нового бота
	botApi, err := tgbotapi.NewBotAPI(token)
	if err != nil {
		return nil, nil, 0, err
	}
	tp.log.Info("Bot authorized: %s", botApi.Self.UserName)

	// Создаем экземпляр TelegramBot
	tgBot := NewTelegramLongPollingBot(ctx, botApi)

	// Настраиваем long polling
	updateConfig := tgbotapi.NewUpdate(0)
	updateConfig.Timeout = 60
	updates := botApi.GetUpdatesChan(updateConfig)

	wg := &sync.WaitGroup{}

	// Горутина для получения обновлений от Telegram
	wg.Add(1)
	go func() {
		defer wg.Done()
		for {
			select {
			case <-ctx.Done():
				tp.log.Info("Завершаем горутину приёма для бота %s", botApi.Self.UserName)
				botApi.StopReceivingUpdates()
				close(tgBot.chIn)
				return
			case update, ok := <-updates:
				if !ok {
					tp.log.Info("Канал обновлений закрыт для бота %s", botApi.Self.UserName)
					close(tgBot.chIn)
					return
				}
				if update.Message != nil {
					msg := &types.TGMessageIn{
						ChatId: update.Message.Chat.ID,
						Text:   update.Message.Text,
						IsCmd:  update.Message.IsCommand(),
					}

					select {
					case tgBot.chIn <- msg:
					default:
						tp.log.Error("Канал входящих сообщений переполнен для бота %s", botApi.Self.UserName)
					}
				}
			}
		}
	}()

	// Горутина для отправки сообщений пользователям
	wg.Add(1)
	go func() {
		defer wg.Done()
		for {
			select {
			case <-ctx.Done():
				tp.log.Info("Завершаем горутину отправки для бота %s", botApi.Self.UserName)
				return
			case msg, ok := <-tgBot.chOut:
				if !ok {
					return
				}

				telegramMsg := tgbotapi.NewMessage(msg.ChatId, msg.Text)
				err := tgBot.limiter.Wait(ctx)
				if err != nil {
					// может возникать, если контекст завершен
					tp.log.Error("Ошибка ожидания лимита отправки сообщения: %v", err)
					continue
				}
				if _, err := botApi.Send(telegramMsg); err != nil {
					if msg.FaledAttempts < config.Cfg.TgCaller.TgSendAttempts {
						msg.FaledAttempts++

						// Переотправляем сообщение через заданное время
						go func() {
							timer := time.NewTimer(time.Duration(msg.FaledAttempts) * time.Second)
							select {
							case <-ctx.Done():
								return
							case <-timer.C:
								tgBot.chOut <- msg
							}
						}()
					} else {
						tp.mon.BotError(botApi.Self.ID, err)
						tp.log.Error("Ошибка отправки сообщения: %v", err)
					}
				}
			}
		}
	}()

	tp.mon.BotStarted(botApi.Self.ID)

	go func() {
		wg.Wait()
		tp.mon.BotStopped(botApi.Self.ID)
	}()

	return tgBot.chIn, tgBot.chOut, botApi.Self.ID, nil
}
