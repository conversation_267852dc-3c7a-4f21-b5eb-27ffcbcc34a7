package telegram

import (
	"context"
	"fmt"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/tg_caller/types"
	"math/rand"
	"sync"
	"time"
)

type TelegramBotMock struct {
	ctx   context.Context
	token string
	chIn  chan *types.TGMessageIn
	chOut chan *types.TGMessageOut
}

type TgMsgMock struct {
	Msg   *types.TGMessageIn
	Delay time.Duration
}

func NewTelegramBotMock(ctx context.Context, token string, messages []*TgMsgMock) *TelegramBotMock {
	bot := &TelegramBotMock{
		ctx:   ctx,
		token: token,
		chIn:  make(chan *types.TGMessageIn, 10),
		chOut: make(chan *types.TGMessageOut, 10),
	}

	go func() {
		for _, msg := range messages {
			// минимальная задержка для правильной работы лимитера
			time.Sleep(5 * time.Millisecond)
			fmt.Printf("[tg mock] user wrote message to tg with token %s, msg: %+v\n", bot.token, msg.Msg)
			if msg.Delay > 0 {
				time.Sleep(msg.Delay)
			}
			bot.chIn <- msg.Msg
		}
	}()

	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			case msg, ok := <-bot.chOut:
				if !ok {
					return
				}
				_ = msg
			}
		}
	}()

	go func() {
		<-ctx.Done()
	}()

	return bot
}

func (m *TelegramBotMock) SendMessage(msg *types.TGMessageOut, sessionId int64) error {
	m.chOut <- msg
	return nil
}

func (m *TelegramBotMock) GetMessagesCh() (<-chan *types.TGMessageIn, chan<- *types.TGMessageOut) {
	return m.chIn, m.chOut
}

type TelegramProviderMock struct {
	activeBots map[string]*TelegramBotMock
	mu         sync.RWMutex
	messages   []*TgMsgMock
}

func NewTelegramProviderMock(messages []*TgMsgMock) *TelegramProviderMock {
	return &TelegramProviderMock{
		activeBots: make(map[string]*TelegramBotMock, 1),
		messages:   messages,
	}
}

func (m *TelegramProviderMock) RunBot(ctx context.Context, token string) (<-chan *types.TGMessageIn, chan<- *types.TGMessageOut, int64, error) {
	bot := NewTelegramBotMock(ctx, token, m.messages)
	m.mu.Lock()
	m.activeBots[token] = bot
	m.mu.Unlock()
	chIn, chOut := bot.GetMessagesCh()
	return chIn, chOut, rand.Int63(), nil
}

func (m *TelegramProviderMock) SendToAll(messages []*TgMsgMock) {
	m.mu.RLock()
	for _, tgBot := range m.activeBots {
		for _, msg := range messages {
			time.Sleep(5 * time.Millisecond)
			tgBot.chIn <- msg.Msg
		}
	}
	m.mu.RUnlock()
}
