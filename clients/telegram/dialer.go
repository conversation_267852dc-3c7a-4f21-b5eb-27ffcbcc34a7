package telegram

import (
	"context"
	"encoding/json"
	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/tg_caller/types"
	logger "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/logger/typed_logger"
	t "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

const (
	FuncDialingTask   = "dialing_task"
	FuncCreateSession = "create_session"
	FuncHandleMessage = "handle_message"
	FuncHandleAction  = "handle_action"
	FuncCloseSession  = "close_session"

	QueueResults = "kv.tg-caller.results"
)

type DialerClient struct {
	rmqConn   *rabbitmq.Conn
	log       *logger.Logger
	publisher *rabbitmq.Publisher
}

func NewDialerClient(rmqConn *rabbitmq.Conn) (*DialerClient, error) {
	client := &DialerClient{
		rmqConn: rmqConn,
		log:     &logger.DefaultLogger,
	}
	publisher, err := rabbitmq.NewPublisher(rmqConn)
	if err != nil {
		return nil, err
	}
	publisher.NotifyReturn(func(r rabbitmq.Return) {
		client.log.Error("queue not exist %s", r.RoutingKey)
	})
	client.publisher = publisher
	return client, nil
}

func (d *DialerClient) SendTask(message *types.DialerTGMessageOut, queue string) error {
	wrappedTask := t.DialerTask{
		Func: FuncDialingTask,
		Data: t.DialerTaskData{
			Id:                t.CallId(message.SessionId),
			TaskId:            t.CallTaskId(message.TaskId),
			BotData:           "{}",
			CampaignData:      "{}",
			DialerTaskDataWeb: &t.DialerTaskDataWeb{CallerQueueToMakeReplies: message.ReplyTo},
		},
	}

	b, err := json.Marshal(wrappedTask)
	if err != nil {
		return err
	}

	gzipped, err := utils.GzipData(b)
	if err != nil {
		return err
	}

	err = d.publisher.Publish(
		gzipped,
		[]string{queue},
		rabbitmq.WithPublishOptionsReplyTo(QueueResults),
		rabbitmq.WithPublishOptionsMandatory,
	)
	if err != nil {
		return err
	}
	d.log.Debug("sent message to dialer queue %s, msg %+v\n", queue, message)
	return nil
}

func (d *DialerClient) SendMessage(message *types.DialerTGMessageOut, queue string) error {
	celeryTask := t.RmqCeleryPacket{} // todo: MakeCeleryPacket с предзаполненными полями

	switch message.Func {
	case FuncHandleMessage:
		celeryTask.Data = types.BotRequest_SendMessage{
			ReqParams: types.BotRequestParams{
				CallId:   message.SessionId,
				FuncType: message.Func,
			},
			Func:      FuncHandleMessage,
			SessionId: message.SessionId,
			ReplyTo:   message.ReplyTo,
			Message:   message.Text,
		}
	case FuncCloseSession:
		celeryTask.Data = types.BotRequest_CloseSession{
			ReqParams: types.BotRequestParams{
				CallId:   message.SessionId,
				FuncType: message.Func,
			},
			Func:      FuncCloseSession,
			SessionId: message.SessionId,
			ReplyTo:   message.ReplyTo,
			Status:    "success",
		}
	}

	b, err := json.Marshal(celeryTask)
	if err != nil {
		return err
	}

	gzipped, err := utils.GzipData(b)
	if err != nil {
		return err
	}

	err = d.publisher.Publish(
		gzipped,
		[]string{queue},
		rabbitmq.WithPublishOptionsReplyTo(message.ReplyTo),
		rabbitmq.WithPublishOptionsMandatory,
	)
	if err != nil {
		return err
	}
	d.log.Debug("sent message to dialer queue %s, msg %+v\n", queue, message)
	return nil
}

type AnyDialerResponse struct {
	t.RmqCeleryPacket
	Data struct {
		Func          string `json:"func"`
		SessionId     string `json:"session_id"`
		GeneratedText string `json:"generated_text"`
		IsFinalState  bool   `json:"is_final_state"`
		InstanceQueue string `json:"instance_queue"`
	} `json:"data"`
}

func (d *DialerClient) RunConsumer(ctx context.Context, queue string) (<-chan *types.DialerTGMessageIn, error) {
	ch := make(chan *types.DialerTGMessageIn, 10)

	cons, err := RmqConsumerAutoAck(d.rmqConn, queue, func(payload AnyDialerResponse, err error) {
		d.log.Debug("[dialer client] consumed value: %+v", payload)
		val := &types.DialerTGMessageIn{
			Func:         payload.Data.Func,
			SessionId:    payload.Data.SessionId,
			Text:         payload.Data.GeneratedText,
			DialerQueue:  payload.Data.InstanceQueue,
			IsFinalState: payload.Data.IsFinalState,
		}
		ch <- val
	})
	if err != nil {
		return nil, err
	}
	go func() {
		<-ctx.Done()
		cons.Close()
	}()

	d.log.Info("[dialer client] consumer started for queue %s", queue)
	return ch, nil
}

func (d *DialerClient) ConsumeResults(ctx context.Context) (chan *types.DialerResultResponse, error) {
	ch := make(chan *types.DialerResultResponse, 10)

	cons, err := RmqConsumerAutoAck(d.rmqConn, QueueResults, func(payload types.DialerResultResponse, err error) {
		d.log.Debug("[dialer result] consumed value: %+v", payload)

		ch <- &payload
		// todo
	})
	if err != nil {
		return nil, err
	}
	go func() {
		<-ctx.Done()
		cons.Close()
	}()

	d.log.Info("[dialer client] consumer started for queue %s", QueueResults)
	return ch, nil
}
