package types

import (
	"fmt"
	"testing"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestCallerMultiphone(t *testing.T) {
	t.<PERSON>lle<PERSON>()
	// support multiple scheme formats, for database scheme transfer
	t.Run("Unmarshal bson", func(t *testing.T) {
		t.<PERSON>llel()
		multiphoneFromBson := func(m bson.M) CallerMultiphone {
			b, err := bson.Marshal(m)
			if err != nil {
				panic(err)
			}
			var multiphone CallerMultiphone
			err = bson.Unmarshal(b, &multiphone)
			if err != nil {
				panic(err)
			}
			return multiphone
		}

		objId := primitive.NewObjectID()
		bsonOld := bson.M{
			"_id": objId,
			"phones": bson.A{
				"79999999999",
				"79999999998",
			},
		}
		bsonNew := bson.M{
			"_id": objId,
			"phones": bson.A{
				bson.M{
					"phone": "79999999999",
				},
				bson.M{
					"phone": "79999999998",
				},
			},
		}
		multiphoneOld := multiphoneFromBson(bsonOld)
		multiphoneNew := multiphoneFromBson(bsonNew)
		if !multiphoneOld.Equal(multiphoneNew) {
			t.Errorf("%+v != %+v", multiphoneOld, multiphoneNew)
		}
	})
}

func TestAttemptByStatus(t *testing.T) {
	t.Parallel()
	// support multiple scheme formats, for database scheme transfer
	t.Run("Unmarshal bson", func(t *testing.T) {
		t.Parallel()

		type Wrap struct {
			Value AttemptByStatus `bson:"value"`
		}
		attemptByStatusFromBson := func(m bson.M) AttemptByStatus {
			b, err := bson.Marshal(m)
			if err != nil {
				panic(err)
			}
			var wrap Wrap
			err = bson.Unmarshal(b, &wrap)
			if err != nil {
				panic(err)
			}
			return wrap.Value
		}

		bsonOld := bson.M{
			"value": bson.A{
				bson.M{
					"status":  "down",
					"number":  1,
					"timeout": 10,
				},
			},
		}
		bsonNew := bson.M{
			"value": bson.M{
				"down": bson.M{
					"number":  1,
					"timeout": 10,
				},
			},
		}
		attemptByStatusOld := attemptByStatusFromBson(bsonOld)
		attemptByStatusNew := attemptByStatusFromBson(bsonNew)
		stringOld := fmt.Sprintf("%+v", attemptByStatusOld)
		stringNew := fmt.Sprintf("%+v", attemptByStatusNew)
		if stringOld != stringNew {
			t.Errorf("%s != %s", stringOld, stringNew)
		}
	})
}

func TestCallerCallStatusMetricsList(t *testing.T) {
	t.Parallel()
	// support multiple scheme formats, for database scheme transfer
	t.Run("Unmarshal bson", func(t *testing.T) {
		t.Parallel()

		obj := CallerCallStatusMetricsList{
			CALLER_CALL_STATUS_EMPTY:                 0,
			CALLER_CALL_STATUS_PREPARING:             1,
			CALLER_CALL_STATUS_CALLING_TO_USER:       2,
			CALLER_CALL_STATUS_IN_CALL_WITH_USER:     3,
			CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   4,
			CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 5,
		}
		bType, b, err := bson.MarshalValue(obj)
		if err != nil {
			t.Error(err)
			return
		}
		var obj2 CallerCallStatusMetricsList
		err = bson.UnmarshalValue(bType, b, &obj2)
		if err != nil {
			t.Error(err)
			return
		}
		if obj != obj2 {
			t.Errorf("%+v != %+v", obj, obj2)
		}
	})
}
