package types

const (
	PostResult_QueuePrefix  = "kv.postresult."
	PostResult_TimeTemplate = "2006-01-02 15:04:05"
)

const (
	Trunk_CurrentServer = "current_server"
)

const (
	TASK_STATUS_DELAY TaskStatus = iota
	TASK_STATUS_PROGRESS
	TASK_STATUS_STOP
)

// Статусы кампаний
const (
	CAMPAIGN_STATUS_ACTIVE CampaignStatus = iota
	CAMPAIGN_STATUS_PAUSED_BY_ALERT
	CAMPAIGN_STATUS_PAUSED_BY_TIME
	CAMPAIGN_STATUS_NO_TASKS
)

// Промежуточные статусы от дайлера
const (
	CALLER_CALL_STATUS_EMPTY CallerCallStatus = iota
	CALLER_CALL_STATUS_PREPARING
	CALLER_CALL_STATUS_CALLING_TO_USER
	CALLER_CALL_STATUS_IN_CALL_WITH_USER
	CALLER_CALL_STATUS_CALLING_TO_OPERATOR
	CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR
)

const (
	CALL_STATUS_UNKNOWN CallStatus = iota
	CALL_STATUS_SUCCESS
	CALL_STATUS_REQUEST_RECALL
	CALL_STATUS_DOWN
	CALL_STATUS_BUSY
	CALL_STATUS_ANSWERING_MACHINE
	CALL_STATUS_CALL_TRANSFER_SUCCESS
	CALL_STATUS_CALL_TRANSFER_NO_PICK_UP
	CALL_STATUS_CALL_TRANSFER_HANG_UP
	CALL_STATUS_SHORT_CALL
	CALL_STATUS_SILENCE_ON_FIRST_MESSAGE
	CALL_STATUS_ERROR_BEFORE_UP
	CALL_STATUS_ERROR_AFTER_UP
	CALL_STATUS_IN_PROGRESS
)

const (
	COUNTER_TYPE_BY_ALL CounterType = iota
	COUNTER_TYPE_BY_ONE
)

const (
	CAMPAIGN_CALL_DIRECTION_OUT CampaignCallDirection = iota
	CAMPAIGN_CALL_DIRECTION_IN
)

const (
	CALL_DIRECTION_OUT CallDirection = iota
	CALL_DIRECTION_IN
)

const (
	CELERY_STATUS_COMPLETE CeleryStatus = iota
	CELERY_STATUS_PROGRESS
	CELERY_STATUS_ERROR
)

const (
	RMQ_TASK_CALL_STARTED RmqTaskStatus = iota
	RMQ_TASK_CALL_RESULT
)

const (
	CampaignSortType_default           = "default"
	CampaignSortType_zeroAttemptsFirst = "zero_attempts_first"
	CampaignSortType_farTzFirst        = "far_tz_first"
)
