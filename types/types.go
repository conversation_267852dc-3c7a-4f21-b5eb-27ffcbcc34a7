package types

import (
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/bsontype"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/x/bsonx/bsoncore"
)

type ILogger interface {
	Debug(msg string, args ...interface{})
	Info(msg string, args ...interface{})
	Warn(msg string, args ...interface{})
	Error(msg string, args ...interface{})
	TraceError(err error)
	Fatal(msg string, args ...interface{})
	Panic(msg string, args ...interface{})
}

type ITypedLogger interface {
	ILogger
	CampaignId(CampaignId) ITypedLogger
	TaskId(CallTaskId) ITypedLogger
	CallId(CallId) ITypedLogger
	QueryId(string) ITypedLogger
}

type M map[string]interface{}

type CallId string

type DataBaseId string

func (id DataBaseId) MarshalBSONValue() (bsontype.Type, []byte, error) {
	if id == "" {
		return bson.MarshalValue(primitive.NilObjectID)
	}
	objId, err := primitive.ObjectIDFromHex(string(id))
	if err != nil {
		return 0, nil, err
	}
	return bson.MarshalValue(objId)
}

type AlertSettings struct {
	Error              int `json:"error" bson:"error"`
	TrunkError         int `json:"trunk_error" bson:"trunk_error"`
	TimeoutError       int `json:"timeout_error" bson:"timeout_error"`
	Down               int `json:"down" bson:"down"`
	AnsweringMachine   int `json:"answering_machine" bson:"answering_machine"`
	TimeoutKill        int `json:"timeout_kill" bson:"timeout_kill"`
	ErrorBeforeUp      int `json:"error_before_up" bson:"error_before_up"`
	ErrorAfterUp       int `json:"error_after_up" bson:"error_after_up"`
	CallTransferHangUp int `json:"call_transfer_hang_up" bson:"call_transfer_hang_up"`
}

func (as AlertSettings) StatusMap() map[CallStatus]int {
	return map[CallStatus]int{
		CALL_STATUS_DOWN:                  as.Down,
		CALL_STATUS_ANSWERING_MACHINE:     as.AnsweringMachine,
		CALL_STATUS_ERROR_BEFORE_UP:       as.ErrorBeforeUp,
		CALL_STATUS_ERROR_AFTER_UP:        as.ErrorAfterUp,
		CALL_STATUS_CALL_TRANSFER_HANG_UP: as.CallTransferHangUp,
	}
}

type CallerSettings struct {
	ShortCallTimeout         int         `json:"shortCallTimeout" bson:"shortCallTimeout"`
	SilenceAsDown            bool        `json:"silenceAsDown" bson:"silenceAsDown"`
	PhonesAttemptCounterType CounterType `json:"phonesAttemptCounterType" bson:"phonesAttemptCounterType"` // "by_one", "by_all"
	PostResult               bool        `json:"postResult" bson:"postResult"`
	PreventTransitLines      bool        `json:"preventTransitLines" bson:"preventTransitLines"`
	TaskPriorityType         string      `json:"taskPriorityType" bson:"taskPriorityType"` // "zero_attempts_first", "far_tz_first"
}

type AttemptByStatus map[string]struct {
	Number  int `bson:"number"`
	Timeout int `bson:"timeout"`
}

func (a *AttemptByStatus) UnmarshalBSONValue(t bsontype.Type, value []byte) error {
	type Alias AttemptByStatus
	switch t {
	case bson.TypeArray:
		var arr []struct {
			Status  string `bson:"status"`
			Number  int    `bson:"number"`
			Timeout int    `bson:"timeout"`
		}
		err := bson.UnmarshalValue(t, value, &arr)
		if err != nil {
			return err
		}
		alias := Alias{}
		for _, v := range arr {
			alias[v.Status] = struct {
				Number  int `bson:"number"`
				Timeout int `bson:"timeout"`
			}{
				Number:  v.Number,
				Timeout: v.Timeout,
			}
		}
		*a = AttemptByStatus(alias)

	case bson.TypeEmbeddedDocument:
		var alias Alias
		err := bson.UnmarshalValue(t, value, &alias)
		if err != nil {
			return err
		}
		*a = AttemptByStatus(alias)

	default:
		return fmt.Errorf("invalid type %s", t)
	}
	return nil
}

type RecallSettings struct {
	DefaultAttempts     int             `bson:"defaultAttempts"`
	DefaultTimeout      int             `bson:"defaultTimeout"`
	AttemptsByStatus    AttemptByStatus ` bson:"attemptsByStatus"`
	RecallsByCallResult AttemptByStatus `bson:"recallsByCallResult"`
}

type TrunkContext struct {
	Trunk  string `json:"trunk" bson:"trunk"`
	Server string `json:"server" bson:"server"`
}

func (t TrunkContext) Name() string {
	server := t.Server
	if server == "" {
		server = Trunk_CurrentServer
	}
	return fmt.Sprintf("%s:%s", t.Trunk, server)
}

func (t TrunkContext) Dest(phone string) string {
	return fmt.Sprintf("Local/%s@%s:%s", phone, t.Trunk, t.Server)
}

func TrunkContextFromString(s string) TrunkContext {
	result := TrunkContext{}
	arr := strings.Split(s, ":")
	if len(arr) == 2 {
		result.Trunk = arr[0]
		result.Server = arr[1]
	}
	return result
}

type TrunkSettings struct {
	Context      []TrunkContext     `json:"context" bson:"context"`
	CallerId     string             `json:"caller_id" bson:"caller_id"`
	Timeout      int                `json:"timeout" bson:"timeout"`
	CallTTL      int                `json:"call_ttl" bson:"call_ttl"`
	MultiphoneId CallerMultiphoneId `json:"multiphone_id" bson:"multiphone_id"`
}

type Interval struct {
	Start int `json:"start" bson:"start"`
	End   int `json:"end" bson:"end"`
}

func (i Interval) ToTime(loc *time.Location) (start, end time.Time) {
	start = utils.ParseTimeFromDayTimeInt(i.Start, loc)
	end = utils.ParseTimeFromDayTimeInt(i.End, loc)
	if start.After(end) {
		end = end.Add(time.Hour * 24)
	}
	return start, end
}

type ScheduleSettings struct {
	DateStart     time.Time  `json:"dateStart" bson:"dateStart"`
	DateEnd       time.Time  `json:"dateEnd" bson:"dateEnd"`
	CampaignTime  Interval   `json:"campaignTime" bson:"campaignTime"`
	LocalUserTime Interval   `json:"localUserTime" bson:"localUserTime"`
	OptimalTimes  []Interval `json:"optimalTimes" bson:"optimalTimes"`
	AvoidTimes    []Interval `json:"avoidTimes" bson:"avoidTimes"`
}

func (s ScheduleSettings) InActiveTime() bool {
	now := time.Now().In(utils.LocationByOffset(3))
	nowDayTimeSec := utils.SecondOfDay(now)
	if s.CampaignTime.Start > nowDayTimeSec {
		return false
	}
	if s.CampaignTime.End < nowDayTimeSec {
		return false
	}
	if s.DateStart.After(now) {
		return false
	}
	if s.DateEnd.Unix() > 0 && s.DateEnd.Before(now) {
		return false
	}
	return true
}

type DialogueId DataBaseId

func (id DialogueId) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return DataBaseId(id).MarshalBSONValue()
}

type Dialogue struct {
	Id      DialogueId `json:"id" bson:"id"`
	Name    string     `json:"name" bson:"name"`
	Version int        `json:"version" bson:"version"`
}

type BuildInfo struct {
	Description string   `json:"description" bson:"description"`
	Project     string   `json:"project" bson:"project"`
	Dialogue    Dialogue `json:"dialogue" bson:"dialogue"`
}

type TaskSettings struct {
	CleanEveryDay bool `json:"cleanEveryDay" bson:"cleanEveryDay"`
}

type CampaignId DataBaseId

func (id CampaignId) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return DataBaseId(id).MarshalBSONValue()
}

type BuildId DataBaseId

func (id BuildId) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return DataBaseId(id).MarshalBSONValue()
}

type RegistryId DataBaseId

func (id RegistryId) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return DataBaseId(id).MarshalBSONValue()
}

type Campaign struct {
	Id               CampaignId            `json:"_id,omitempty" bson:"_id,omitempty"`
	Name             string                `json:"name" bson:"name"`
	PublicId         int                   `json:"publicId" bson:"publicId"`
	Lines            int                   `json:"lines" bson:"lines"`
	CallDirection    CampaignCallDirection `json:"callDirection" bson:"callDirection"`
	BuildId          BuildId               `json:"buildId" bson:"buildId"`
	Status           CampaignStatus        `json:"status" bson:"status"`
	IsActive         bool                  `json:"isActive" bson:"isActive"`
	IsTest           bool                  `json:"isTest" bson:"isTest"`
	CallerSettings   CallerSettings        `json:"callerSettings" bson:"callerSettings"`
	TrunkSettings    TrunkSettings         `json:"trunkSettings" bson:"trunkSettings"`
	RecallSettings   RecallSettings        `json:"recallSettings" bson:"recallSettings"`
	AlertSettings    AlertSettings         `json:"alertSettings" bson:"alertSettings"`
	ScheduleSettings ScheduleSettings      `json:"schedule" bson:"schedule"`
	BuildInfo        BuildInfo             `json:"buildInfo" bson:"buildInfo"`
	TaskSettings     TaskSettings          `json:"taskSettings" bson:"taskSettings"`
	FieldData        M                     `json:"fieldData" bson:"fieldData"`
}

func (c Campaign) TimePriority(t time.Time) int {
	for _, interval := range c.ScheduleSettings.OptimalTimes {
		start, end := interval.ToTime(t.Location())
		if !t.Before(start) && t.Before(end) {
			return 0
		}
	}
	for _, interval := range c.ScheduleSettings.AvoidTimes {
		start, end := interval.ToTime(t.Location())
		if !t.Before(start) && t.Before(end) {
			return 2
		}
	}
	return 1
}

func (c Campaign) OptimalOffsetsToCall(t time.Time) map[int]struct{} {
	res := map[int]struct{}{}
	for i := range c.ScheduleSettings.OptimalTimes {
		for offset := range utils.MatchOffsets(
			t,
			c.ScheduleSettings.OptimalTimes[i].Start,
			c.ScheduleSettings.OptimalTimes[i].End,
		) {
			res[offset] = struct{}{}
		}
	}
	return res
}

func (c Campaign) AvoidOffsetsToCall(t time.Time) map[int]struct{} {
	res := map[int]struct{}{}
	for i := range c.ScheduleSettings.AvoidTimes {
		for offset := range utils.MatchOffsets(
			t,
			c.ScheduleSettings.AvoidTimes[i].Start,
			c.ScheduleSettings.AvoidTimes[i].End,
		) {
			res[offset] = struct{}{}
		}
	}
	return res
}

func (c Campaign) SortedOffsets(t time.Time, offsets map[int]struct{}) (sorted [3][]int) {
	if len(offsets) == 0 {
		return sorted
	}
	optimalOffsets := c.OptimalOffsetsToCall(t)
	for offset := range optimalOffsets {
		if _, ok := offsets[offset]; !ok {
			delete(optimalOffsets, offset)
		}
	}
	avoidOffsets := c.AvoidOffsetsToCall(t)
	for offset := range avoidOffsets {
		if _, ok := offsets[offset]; !ok {
			delete(avoidOffsets, offset)
		}
	}
	normalOffsets := make(map[int]interface{}, len(offsets)-len(optimalOffsets)-len(avoidOffsets))
	for offset := range offsets {
		if _, ok := optimalOffsets[offset]; ok {
			delete(avoidOffsets, offset)
			continue
		}
		if _, ok := avoidOffsets[offset]; ok {
			continue
		}
		normalOffsets[offset] = struct{}{}
	}
	sortedOptimal := make([]int, 0, len(optimalOffsets))
	for offset := range optimalOffsets {
		sortedOptimal = append(sortedOptimal, offset)
	}
	sortedNormal := make([]int, 0, len(normalOffsets))
	for offset := range normalOffsets {
		sortedNormal = append(sortedNormal, offset)
	}
	sortedAvoid := make([]int, 0, len(avoidOffsets))
	for offset := range avoidOffsets {
		sortedAvoid = append(sortedAvoid, offset)
	}

	sort.Slice(sortedOptimal, func(i int, j int) bool {
		return sortedOptimal[i] > sortedOptimal[j]
	})
	sort.Slice(sortedNormal, func(i int, j int) bool {
		return sortedNormal[i] > sortedNormal[j]
	})
	sort.Slice(sortedAvoid, func(i int, j int) bool {
		return sortedAvoid[i] > sortedAvoid[j]
	})

	sorted[0] = sortedOptimal
	sorted[1] = sortedNormal
	sorted[2] = sortedAvoid
	return sorted
}

func (c Campaign) QueueName(suf string) string {
	return fmt.Sprintf(
		"%s.%s.%s",
		suf,
		c.Id,
		c.BuildId,
	)
}

func (c Campaign) QueueNameByServer(serverIdent, suf string) string {
	return fmt.Sprintf(
		"%s.%s.%s.%s",
		suf,
		c.Id,
		c.BuildId,
		serverIdent,
	)
}

func (c Campaign) CallerIdGen(multiphone CallerMultiphone) func() string {
	if len(multiphone.Phones) == 0 {
		callerId := c.TrunkSettings.CallerId
		return func() string {
			return callerId
		}
	}
	var currentItem int
	return func() string {
		result := multiphone.Phones[currentItem]
		currentItem = (currentItem + 1) % len(multiphone.Phones)
		return string(result)
	}
}

type TaskPhone struct {
	Phone           string `json:"phone" bson:"phone"`
	NormalizedPhone string `json:"normalizedPhone" bson:"normalizedPhone"`
	Timezone        int    `json:"timezone" bson:"timezone"`
	Region          string `json:"region" bson:"region"`
}

type CallData struct {
	AgentId   any `json:"agent_id,omitempty" bson:"agent_id,omitempty"` // it can be 1 or `1`
	*JivoData `bson:",inline"`
}

type JivoData struct {
	ChatId        string `json:"chat_id"`
	ClientId      string `json:"client_id"`
	JivoURL       string `json:"jivo_url"`
	InstanceQueue string `json:"instance_queue"`
}

type CallTaskId DataBaseId

func (id CallTaskId) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return DataBaseId(id).MarshalBSONValue()
}

type CallTask struct {
	Id             CallTaskId   `json:"_id,omitempty" bson:"_id,omitempty"`
	CampaignId     CampaignId   `json:"campaignId" bson:"campaignId"`
	ExternalId     string       `json:"externalId" bson:"externalId"`
	Phones         []TaskPhone  `json:"phones" bson:"phones"`
	LastCallId     CallId       `json:"lastCallId,omitempty" bson:"lastCallId,omitempty"`
	NextPhoneIndex int          `json:"nextPhoneIndex,omitempty" bson:"nextPhoneIndex,omitempty"`
	NextCallAt     time.Time    `json:"nextCallAt" bson:"nextCallAt"`
	NextTz         int          `json:"nextTz" bson:"nextTz"`
	EndDate        time.Time    `json:"endDate,omitempty" bson:"endDate,omitempty"`
	LastTrunk      TrunkContext `json:"lastTrunk,omitempty" bson:"lastTrunk,omitempty"`
	LastCallerId   string       `json:"lastCallerId,omitempty" bson:"lastCallerId,omitempty"`
	IsTest         bool         `json:"isTest" bson:"isTest"`
	CallResults    []Attempt    `json:"callResults,omitempty" bson:"callResults,omitempty"`
	CreatedAt      time.Time    `json:"createdAt" bson:"createdAt"`
	FullCallData   M            `json:"callData" bson:"callData"`
	RegistryId     RegistryId   `json:"registryId,omitempty" bson:"registryId,omitempty"`
}

type ExtendedCallTask struct {
	Status                TaskStatus `json:"status" bson:"status"`
	Project               string     `json:"project" bson:"project"`
	LastRequestToDialerAt time.Time  `json:"lastRequestToDialerAt,omitempty" bson:"lastRequestToDialerAt,omitempty"`
	StartDate             time.Time  `json:"startDate,omitempty" bson:"startDate,omitempty"`
	AttemptNumber         int        `json:"attemptNumber,omitempty" bson:"attemptNumber,omitempty"`
	LastCallStatus        CallStatus `json:"lastCallStatus,omitempty" bson:"lastCallStatus,omitempty"`
	UpdatedAt             time.Time  `json:"updatedAt" bson:"updatedAt"`
	NextTz                int        `json:"nextTz,omitempty" bson:"nextTz,omitempty"`
	Priority              int        `json:"priority,omitempty" bson:"priority,omitempty"`
	CallData              CallData
	CallTask              `bson:",inline"`
}

func (t *ExtendedCallTask) UnmarshalBSON(b []byte) error {
	type Alias ExtendedCallTask
	var task Alias
	err := bson.Unmarshal(b, &task)
	if err != nil {
		return err
	}
	b, err = bson.Marshal(task.CallTask.FullCallData)
	if err != nil {
		return err
	}
	err = bson.Unmarshal(b, &task.CallData)
	if err != nil {
		return err
	}
	*t = ExtendedCallTask(task)
	return nil
}

func (t *ExtendedCallTask) UnmarshalJSON(b []byte) error {
	type Alias ExtendedCallTask
	var task Alias
	err := json.Unmarshal(b, &task)
	if err != nil {
		return err
	}
	b, err = json.Marshal(task.CallTask.FullCallData)
	if err != nil {
		return err
	}
	err = json.Unmarshal(b, &task.CallData)
	if err != nil {
		return err
	}
	*t = ExtendedCallTask(task)
	return nil
}

type CampaignCallDirection uint8

var campaignCallDirections = map[CampaignCallDirection]string{
	CAMPAIGN_CALL_DIRECTION_OUT: "out",
	CAMPAIGN_CALL_DIRECTION_IN:  "in",
}

func (m CampaignCallDirection) String() string {
	return campaignCallDirections[m]
}

func (m CampaignCallDirection) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return utils.BsonMarshalStringFromMap(m, campaignCallDirections)
}

func (m *CampaignCallDirection) UnmarshalBSONValue(t bsontype.Type, value []byte) error {
	return utils.BsonUnmarshalStringFromMap(m, campaignCallDirections, t, value)
}

func (m CampaignCallDirection) MarshalJSON() ([]byte, error) {
	return utils.MarshalJsonStringFromMap(m, campaignCallDirections)
}

func (m *CampaignCallDirection) UnmarshalJSON(data []byte) error {
	return utils.UnmarshalJsonStringFromMap(m, campaignCallDirections, data)
}

func (m *CampaignCallDirection) ToCallDirection() CallDirection {
	if *m == CAMPAIGN_CALL_DIRECTION_IN {
		return CALL_DIRECTION_IN
	}
	return CALL_DIRECTION_OUT
}

type CallDirection uint8

var callDirections = map[CallDirection]string{
	CALL_DIRECTION_OUT: "outgoing",
	CALL_DIRECTION_IN:  "incoming",
}

func (m CallDirection) String() string {
	return callDirections[m]
}

func (m CallDirection) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return utils.BsonMarshalStringFromMap(m, callDirections)
}

func (m *CallDirection) UnmarshalBSONValue(t bsontype.Type, value []byte) error {
	return utils.BsonUnmarshalStringFromMap(m, callDirections, t, value)
}

func (m CallDirection) MarshalJSON() ([]byte, error) {
	return utils.MarshalJsonStringFromMap(m, callDirections)
}

func (m *CallDirection) UnmarshalJSON(data []byte) error {
	return utils.UnmarshalJsonStringFromMap(m, callDirections, data)
}

type CallStatus uint8

var callStatuses = map[CallStatus]string{
	CALL_STATUS_UNKNOWN:                  "",
	CALL_STATUS_SUCCESS:                  "success",
	CALL_STATUS_REQUEST_RECALL:           "request_recall",
	CALL_STATUS_DOWN:                     "down",
	CALL_STATUS_BUSY:                     "busy",
	CALL_STATUS_ANSWERING_MACHINE:        "answering_machine",
	CALL_STATUS_CALL_TRANSFER_SUCCESS:    "call_transfer_success",
	CALL_STATUS_CALL_TRANSFER_NO_PICK_UP: "call_transfer_no_pick_up",
	CALL_STATUS_CALL_TRANSFER_HANG_UP:    "call_transfer_hang_up",
	CALL_STATUS_SHORT_CALL:               "short_call",
	CALL_STATUS_SILENCE_ON_FIRST_MESSAGE: "silence_on_first_message",
	CALL_STATUS_ERROR_BEFORE_UP:          "error_before_up",
	CALL_STATUS_ERROR_AFTER_UP:           "error_after_up",
	CALL_STATUS_IN_PROGRESS:              "in_progress", // twilio call. Call transfered to operator and not finished yet
}

func (m CallStatus) String() string {
	return callStatuses[m]
}

func (m CallStatus) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return utils.BsonMarshalStringFromMap(m, callStatuses)
}

func (m *CallStatus) UnmarshalBSONValue(t bsontype.Type, value []byte) error {
	if t == bson.TypeNull || t == bson.TypeUndefined {
		*m = CALL_STATUS_UNKNOWN
		return nil
	}
	return utils.BsonUnmarshalStringFromMap(m, callStatuses, t, value)
}

func (m CallStatus) MarshalJSON() ([]byte, error) {
	return utils.MarshalJsonStringFromMap(m, callStatuses)
}

func (m *CallStatus) UnmarshalJSON(data []byte) error {
	return utils.UnmarshalJsonStringFromMap(m, callStatuses, data)
}

type CallerCallStatus uint8

var callerCallStatuses = map[CallerCallStatus]string{
	CALLER_CALL_STATUS_EMPTY:                 "",
	CALLER_CALL_STATUS_PREPARING:             "preparing",
	CALLER_CALL_STATUS_CALLING_TO_USER:       "calling_to_user",
	CALLER_CALL_STATUS_IN_CALL_WITH_USER:     "in_call_with_user",
	CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   "calling_to_operator",
	CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: "in_call_with_operator",
}

func (s CallerCallStatus) String() string {
	return callerCallStatuses[s]
}

func (s CallerCallStatus) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return utils.BsonMarshalStringFromMap(s, callerCallStatuses)
}

func (s *CallerCallStatus) UnmarshalBSONValue(t bsontype.Type, value []byte) error {
	if t == bson.TypeNull || t == bson.TypeUndefined {
		*s = CALLER_CALL_STATUS_EMPTY
		return nil
	}
	return utils.BsonUnmarshalStringFromMap(s, callerCallStatuses, t, value)
}

func (s CallerCallStatus) MarshalJSON() ([]byte, error) {
	return utils.MarshalJsonStringFromMap(s, callerCallStatuses)
}

func (s *CallerCallStatus) UnmarshalJSON(data []byte) error {
	return utils.UnmarshalJsonStringFromMap(s, callerCallStatuses, data)
}

type RmqTaskStatus uint8

var rmqTaskStatuses = map[RmqTaskStatus]string{
	RMQ_TASK_CALL_STARTED: "CALL_STARTED",
	RMQ_TASK_CALL_RESULT:  "CALL_RESULT",
}

func (m RmqTaskStatus) String() string {
	return rmqTaskStatuses[m]
}

func (m RmqTaskStatus) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return utils.BsonMarshalStringFromMap(m, rmqTaskStatuses)
}

func (m *RmqTaskStatus) UnmarshalBSONValue(t bsontype.Type, value []byte) error {
	return utils.BsonUnmarshalStringFromMap(m, rmqTaskStatuses, t, value)
}

func (m RmqTaskStatus) MarshalJSON() ([]byte, error) {
	return utils.MarshalJsonStringFromMap(m, rmqTaskStatuses)
}

func (m *RmqTaskStatus) UnmarshalJSON(data []byte) error {
	return utils.UnmarshalJsonStringFromMap(m, rmqTaskStatuses, data)
}

type CeleryStatus uint8

var celeryStatuses = map[CeleryStatus]string{
	CELERY_STATUS_COMPLETE: "Complete",
	CELERY_STATUS_PROGRESS: "Progress",
	CELERY_STATUS_ERROR:    "Error",
}

func (m CeleryStatus) String() string {
	return celeryStatuses[m]
}

func (m CeleryStatus) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return utils.BsonMarshalStringFromMap(m, celeryStatuses)
}

func (m *CeleryStatus) UnmarshalBSONValue(t bsontype.Type, value []byte) error {
	return utils.BsonUnmarshalStringFromMap(m, celeryStatuses, t, value)
}

func (m CeleryStatus) MarshalJSON() ([]byte, error) {
	return utils.MarshalJsonStringFromMap(m, celeryStatuses)
}

func (m *CeleryStatus) UnmarshalJSON(data []byte) error {
	return utils.UnmarshalJsonStringFromMap(m, celeryStatuses, data)
}

type CampaignStatus uint8

var campaignStatuses = map[CampaignStatus]string{
	CAMPAIGN_STATUS_ACTIVE:          "",
	CAMPAIGN_STATUS_PAUSED_BY_ALERT: "paused_by_alert",
	CAMPAIGN_STATUS_PAUSED_BY_TIME:  "paused_by_time",
	CAMPAIGN_STATUS_NO_TASKS:        "no_tasks",
}

func (m CampaignStatus) String() string {
	return campaignStatuses[m]
}

func (m CampaignStatus) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return utils.BsonMarshalStringFromMap(m, campaignStatuses)
}

func (m *CampaignStatus) UnmarshalBSONValue(t bsontype.Type, value []byte) error {
	if t == bson.TypeNull || t == bson.TypeUndefined {
		*m = CAMPAIGN_STATUS_ACTIVE
		return nil
	}
	return utils.BsonUnmarshalStringFromMap(m, campaignStatuses, t, value)
}

func (m CampaignStatus) MarshalJSON() ([]byte, error) {
	return utils.MarshalJsonStringFromMap(m, campaignStatuses)
}

func (m *CampaignStatus) UnmarshalJSON(data []byte) error {
	return utils.UnmarshalJsonStringFromMap(m, campaignStatuses, data)
}

type TaskStatus uint8

var taskStatuses = map[TaskStatus]string{
	TASK_STATUS_DELAY:    "delay",
	TASK_STATUS_PROGRESS: "progress",
	TASK_STATUS_STOP:     "stop",
}

func (m TaskStatus) String() string {
	return taskStatuses[m]
}

func (m TaskStatus) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return utils.BsonMarshalStringFromMap(m, taskStatuses)
}

func (m *TaskStatus) UnmarshalBSONValue(t bsontype.Type, value []byte) error {
	return utils.BsonUnmarshalStringFromMap(m, taskStatuses, t, value)
}

func (m TaskStatus) MarshalJSON() ([]byte, error) {
	return utils.MarshalJsonStringFromMap(m, taskStatuses)
}

func (m *TaskStatus) UnmarshalJSON(data []byte) error {
	return utils.UnmarshalJsonStringFromMap(m, taskStatuses, data)
}

type CallerCallStatusMetricsList [6]int

func (c CallerCallStatusMetricsList) MarshalBSONValue() (bsontype.Type, []byte, error) {
	statusMap := make(bson.M, len(c))
	for i, v := range c {
		if v > 0 {
			statusMap[callerCallStatuses[CallerCallStatus(i)]] = v
		}
	}
	return bson.MarshalValue(statusMap)
}

func (c *CallerCallStatusMetricsList) UnmarshalBSONValue(t bsontype.Type, value []byte) error {
	if t != bson.TypeEmbeddedDocument {
		return fmt.Errorf("invalid type %v", t)
	}
	var statusMap map[string]int
	err := bson.UnmarshalValue(t, value, &statusMap)
	if err != nil {
		return err
	}
	for k, v := range statusMap {
		if val, ok := utils.ReverseMapSearch(k, callerCallStatuses); ok {
			(*c)[val] = v
		}
	}
	return nil
}

func (c CallerCallStatusMetricsList) MarshalJSON() ([]byte, error) {
	statusMap := make(map[string]int, len(c))
	for i, v := range c {
		if v > 0 {
			statusMap[callerCallStatuses[CallerCallStatus(i)]] = v
		}
	}
	return json.Marshal(statusMap)
}

func (c *CallerCallStatusMetricsList) UnmarshalJSON(data []byte) error {
	var statusMap map[string]int
	if err := json.Unmarshal(data, &statusMap); err != nil {
		return err
	}
	for k, v := range statusMap {
		if val, ok := utils.ReverseMapSearch(k, callerCallStatuses); ok {
			(*c)[val] = v
		}
	}
	return nil
}

type CounterType uint8

var counterTypes = map[CounterType]string{
	COUNTER_TYPE_BY_ALL: "by_all",
	COUNTER_TYPE_BY_ONE: "by_one",
}

func (m CounterType) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return utils.BsonMarshalStringFromMap(m, counterTypes)
}

func (m *CounterType) UnmarshalBSONValue(t bsontype.Type, value []byte) error {
	return utils.BsonUnmarshalStringFromMap(m, counterTypes, t, value)
}

func (m CounterType) MarshalJSON() ([]byte, error) {
	return utils.MarshalJsonStringFromMap(m, counterTypes)
}

func (m *CounterType) UnmarshalJSON(data []byte) error {
	return utils.UnmarshalJsonStringFromMap(m, counterTypes, data)
}

type CampaignLoadPointId DataBaseId

func (id CampaignLoadPointId) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return DataBaseId(id).MarshalBSONValue()
}

type CampaignLoadPoint struct {
	Id              CampaignLoadPointId         `json:"_id,omitempty" bson:"_id,omitempty"`
	CampaignId      CampaignId                  `json:"campaignId" bson:"campaignId"`
	Lines           int                         `json:"lines" bson:"lines"`
	LinesPerSlotMin map[string]int              `json:"linesPerSlotMin" bson:"linesPerSlotMin"`
	LinesPerSlotMax map[string]int              `json:"linesPerSlotMax" bson:"linesPerSlotMax"`
	LinesPerSlotCur map[string]int              `json:"linesPerSlotCur" bson:"linesPerSlotCur"`
	BusyLinesMin    int                         `json:"busyLinesMin" bson:"busyLinesMin"`
	BusyLinesMax    int                         `json:"busyLinesMax" bson:"busyLinesMax"`
	BusyLinesCur    int                         `json:"busyLinesCur" bson:"busyLinesCur"`
	CallStatusesMin CallerCallStatusMetricsList `json:"callStatusesMin" bson:"callStatusesMin"`
	CallStatusesMax CallerCallStatusMetricsList `json:"callStatusesMax" bson:"callStatusesMax"`
	CallStatusesCur CallerCallStatusMetricsList `json:"callStatusesCur" bson:"callStatusesCur"`
	SavedAt         time.Time                   `json:"savedAt" bson:"savedAt"`
}

func (i CampaignLoadPoint) Equal(j CampaignLoadPoint) bool {
	if i.Id != j.Id {
		return false
	}
	if i.CampaignId != j.CampaignId {
		return false
	}
	if i.Lines != j.Lines {
		return false
	}
	if i.BusyLinesCur != j.BusyLinesCur {
		return false
	}
	if i.BusyLinesMin != j.BusyLinesMin {
		return false
	}
	if i.BusyLinesMax != j.BusyLinesMax {
		return false
	}
	if i.SavedAt != j.SavedAt {
		return false
	}
	if i.CallStatusesMin != j.CallStatusesMin {
		return false
	}
	if i.CallStatusesMax != j.CallStatusesMax {
		return false
	}
	if i.CallStatusesCur != j.CallStatusesCur {
		return false
	}
	if !utils.EqualMaps(i.LinesPerSlotMin, j.LinesPerSlotMin) {
		return false
	}
	if !utils.EqualMaps(i.LinesPerSlotMax, j.LinesPerSlotMax) {
		return false
	}
	if !utils.EqualMaps(i.LinesPerSlotCur, j.LinesPerSlotCur) {
		return false
	}
	return true
}

type RmqPublisher interface {
	Close()
	NotifyReturn(handler func(r rabbitmq.Return))
	Publish(data []byte, routingKeys []string, optionFuncs ...func(*rabbitmq.PublishOptions)) error
}

type DialerTask struct {
	Func string
	Data DialerTaskData
}

func (dt DialerTask) Push(publisher RmqPublisher, queue, replyTo string) error {
	bytes, err := json.Marshal(dt)
	if err != nil {
		return err
	}
	bytes, err = utils.GzipData(bytes)
	if err != nil {
		return err
	}
	correlationId := uuid.New().String()
	headers := rabbitmq.Table{
		"correlation_id": correlationId,
		"reply_to":       replyTo,
		"id":             string(dt.Data.TaskId),
	}
	return publisher.Publish(
		bytes,
		[]string{queue},
		rabbitmq.WithPublishOptionsHeaders(headers),
		rabbitmq.WithPublishOptionsCorrelationID(correlationId),
		rabbitmq.WithPublishOptionsReplyTo(replyTo),
		rabbitmq.WithPublishOptionsMandatory,
	)
}

type DialerTaskData struct {
	Id           CallId         `json:"id"`
	RegistryId   RegistryId     `json:"registry_id"`
	TaskId       CallTaskId     `json:"task_id"`
	Phone        string         `json:"phone"`
	CompanyId    CampaignId     `json:"company_id"`
	BotData      string         `json:"bot_data"`     // json
	CampaignData string         `json:"company_data"` // json
	Timezone     int            `json:"tz"`
	CallTtl      int            `json:"call_ttl"`
	IsTest       bool           `json:"is_test"`
	Error        string         `json:"error"`
	CallData     DialerCallData `json:"call_data"`
	*DialerTaskDataIn
	*DialerTaskDataWeb
}

type DialerTaskDataIn struct {
	CallerQueueOnTaskReceived string `json:"caller_queue_on_task_received"` // For incoming caller, we should reply which server/container was selected to call
}

type DialerTaskDataWeb struct {
	CallerQueueToMakeReplies string `json:"caller_queue_to_make_replies"`
}

type DialerResponseAny struct {
	RmqCeleryPacket
	Data json.RawMessage `json:"data"`
}

type DialerCallData struct {
	CallerId            string     `json:"callerid"`
	Trunk               string     `json:"trunk"`
	SecondsForSuccess   int        `json:"seconds_for_success"`
	CallTaskId          CallTaskId `json:"caller_queue_id"`
	Dest                string     `json:"dest"`
	Timeout             int        `json:"timeout"`
	Phone               string     `json:"phone"`
	ChannelId           string     `json:"channel_id"`
	ExternalId          string     `json:"external_id"`
	Region              string     `json:"region"`
	EnableSilenceAsDown bool       `json:"enable_silence_as_down"`
	CallDate            int64      `json:"call_date"` // Unix
	AttemptNumber       int        `json:"attempt_number"`
	*DialerCallDataIn
	*DialerCallDataTwilio
	*JivoData
}

type DialerCallDataIn struct {
	PhoneTo      string `json:"phone_to"`
	ExtensionNum string `json:"ext_num"`
}

type DialerCallDataTwilio struct {
	AgentId string `json:"agent_id"` // twilio
}

type DialerResultData struct {
	ContextData interface{}   `json:"context_data,omitempty"` // филды
	Log         []interface{} `json:"log,omitempty"`          // лог бота
}

type DialerResult struct {
	Id                   CallId           `json:"id"`
	CallTaskId           CallTaskId       `json:"caller_queue_id"`
	CampaignId           CampaignId       `json:"campaign_id"`
	BotResult            string           `json:"bot_result"`
	Status               CallStatus       `json:"status"`
	DataBaseId           CallResultId     `json:"mongo_id"`
	Error                string           `json:"error"`
	DialerId             string           `json:"dialer_id"`
	BotId                string           `json:"bot_id"`
	AudioFile            string           `json:"audio_file"`
	PreviousCallEndState string           `json:"previous_call_end_state"`
	StartDate            float64          `json:"start_date"`
	EndDate              float64          `json:"end_date"`
	Duration             float64          `json:"duration"`
	DurationBefore       float64          `json:"duration_before"`
	AudioStorageTime     *float64         `json:"audio_storage_time"`
	RecallDatetime       interface{}      `json:"recall_datetime"` // duration in seconds. Example: 300, "300" TODO: надо убедится что формат один и поправить, если нет
	Data                 DialerResultData `json:"data"`
}

func (d DialerResult) ParseRecallDatetime() (dt time.Time) {
	var intDur int
	var err error
	switch v := d.RecallDatetime.(type) {
	case string:
		intDur, err = strconv.Atoi(v)
		if err != nil {
			return dt
		}
	case int:
		intDur = v
	default:
		return dt
	}
	return time.Unix(int64(d.EndDate)+int64(intDur), 0)
}

type DialerStatus struct {
	CallId     CallId           `json:"call_id"`
	TaskId     CallTaskId       `json:"task_id"`
	CampaignId CampaignId       `json:"campaign_id"`
	DialerId   string           `json:"dialer_id"`
	Status     CallerCallStatus `json:"status"`
}

// DialerServerInfo data from dialer for incoming calls
type DialerServerInfo struct {
	CallId        string     `json:"call_id"`
	TaskId        CallTaskId `json:"task_id"`
	Server        string     `json:"server"`
	Container     string     `json:"container"`
	ContainerIp   string     `json:"container_ip"`
	ContainerPort string     `json:"container_port"`
}

type Attempt struct {
	CallId      CallId                `bson:"call_id"`
	Status      CallStatus            `bson:"status"`
	ResultCall  string                `bson:"result_call"`
	ContextData CallResultContextData `bson:"context_data"`
	Trunk       string                `bson:"trunk"`
	Phone       string                `bson:"phone"`
	CallerId    string                `bson:"caller_id"`
	Tz          int                   `bson:"tz"`
	CallData    struct {
		Region string `bson:"region"`
	} `bson:"call_data"`
}

type CallResultId DataBaseId

func (id CallResultId) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return DataBaseId(id).MarshalBSONValue()
}

type CallResult struct {
	Id                 CallResultId       `bson:"_id,omitempty"`
	CallTaskId         CallTaskId         `bson:"callTaskId,omitempty"`
	StartTs            int                `bson:"start_ts"`
	EndTs              int                `bson:"end_ts"`
	Duration           float64            `bson:"duration"`
	DurationBefore     float64            `bson:"duration_before"`
	AudioFile          string             `bson:"audio_file"`
	CampaignName       string             `bson:"campaign_name"`
	Delivery           *string            `bson:"delivery"`
	StopFromAmd        bool               `bson:"stop_from_amd"`
	StopFromAmdEm      bool               `bson:"stop_from_amd_em"`
	StartDatabase      time.Time          `bson:"start_mongo"`
	EndDatabase        time.Time          `bson:"end_mongo"`
	TaskAddedDatabase  time.Time          `bson:"task_added_mongo"`
	DuplicateMessages  map[string]string  `bson:"duplicate_messages"`
	StructuredLogs     []interface{}      `bson:"structured_logs"`
	ErrorMsg           string             `bson:"error_msg"`
	CallWithProblems   bool               `bson:"call_with_problems"`
	ConfigId           *string            `bson:"config_id"`
	CampaignId         CampaignId         `bson:"campaignId,omitempty"`
	RegistryId         RegistryId         `bson:"registryId,omitempty"`
	CompanyId          string             `bson:"company_id,omitempty"`
	CallerQueueId      string             `bson:"caller_queue_id,omitempty"`
	IsTest             bool               `bson:"isTest"`
	TsOffset           int32              `bson:"ts_offset"`
	EmAudioFile        *string            `bson:"em_audio_file"`
	Lifecycle          int                `bson:"lifecycle,omitempty"`
	CurrentS3          *string            `bson:"current_s3"`
	BuildInfo          BuildInfo          `bson:"build_info"`
	Direction          CallDirection      `bson:"direction"`
	CategoryStatus     string             `bson:"category_status"`
	CallData           CallResultCallData `bson:"call_data"`
	InterruptionStatus int                `bson:"interruption_status"`
	Source             string             `bson:"source"`
	DialerId           string             `bson:"dialer_id"`
	BotId              string             `bson:"bot_id"`
	UpdatedAt          time.Time          `bson:"updatedAt,omitempty"`
	IsFirst            bool               `bson:"is_first"` // первая попытка
	IsLast             bool               `bson:"is_last"`  // последняя попытка
	Attempt            `bson:",inline"`
	*CallResultIn      `bson:",inline"`
}

type CallResultIn struct {
	PhoneTo string `json:"phone_to" bson:"phone_to"`
}

type CallResultCallData struct {
	CallDate            int32    `json:"call_date" bson:"call_date"`
	CallerId            string   `json:"callerid" bson:"callerid"`
	Phone               string   `json:"phone" bson:"phone"`
	Dest                string   `json:"dest" bson:"dest"`
	Phones              []string `json:"phones" bson:"phones"`
	Region              string   `json:"region" bson:"region"`
	CallerQueueId       string   `json:"caller_queue_id" bson:"caller_queue_id"`
	EnableSilenceAsDown bool     `json:"enable_silence_as_down" bson:"enable_silence_as_down"`
	Timeout             int      `json:"timeout" bson:"timeout"`
	Trunk               string   `json:"trunk" bson:"trunk"`
	SecondsForSuccess   int      `json:"seconds_for_success" bson:"seconds_for_success"`
	AttemptNumber       int      `json:"attempt_number" bson:"attempt_number"`
	ExternalId          string   `json:"external_id" bson:"external_id"`
}

type CallResultContextData struct {
	Fields []ContextDataField `bson:"fields"`
}

type ContextDataField struct {
	Name     string      `bson:"name"`
	Value    interface{} `bson:"value"`
	Approved bool        `bson:"approved"`
}

type PostResult struct {
	Status         CallStatus       `json:"status"`
	Phone          string           `json:"phone"`
	DialogueName   string           `json:"dialogueName"`
	ExternalId     string           `json:"external_id"`
	StartDate      string           `json:"start_date"`
	EndDate        string           `json:"end_date"`
	NextCallDate   string           `json:"next_call_date"`
	Duration       string           `json:"duration"`
	DurationBefore string           `json:"duration_before"`
	NextStatus     TaskStatus       `json:"next_status"`
	CallId         CallId           `json:"call_id"`
	DataBaseId     CallResultId     `json:"mongo_id"`
	CampaignId     int              `json:"company_id"`
	RegistryId     RegistryId       `json:"registry_id"`
	DateCreated    int64            `json:"date_created"`
	Tz             int              `json:"tz"`
	Priority       int              `json:"priority"`
	AttemptsCount  int              `json:"attempts_count"`
	IsTest         bool             `json:"is_test"`
	Service        DialerResultData `json:"service"`
	Project        string           `json:"-"`
}

type WrappedPostResult struct {
	Data struct {
		Report PostResult `json:"report"`
	} `json:"data"`
}

func (pr PostResult) Push(publisher RmqPublisher) error {
	wrap := WrappedPostResult{}
	wrap.Data.Report = pr
	bytes, err := json.Marshal(wrap)
	if err != nil {
		return err
	}
	gzipped, err := utils.GzipData(bytes)
	if err != nil {
		return err
	}
	queue := fmt.Sprintf("%s%s", PostResult_QueuePrefix, pr.Project)
	return publisher.Publish(
		gzipped,
		[]string{queue},
		rabbitmq.WithPublishOptionsContentEncoding("utf-8"),
		rabbitmq.WithPublishOptionsContentType("application/data"),
		rabbitmq.WithPublishOptionsCorrelationID(string(pr.CallId)),
		rabbitmq.WithPublishOptionsMandatory,
	)
}

type RmqCeleryPacket struct {
	Version       int    `json:"version,omitempty"`
	CorrelationId string `json:"correlation_id,omitempty"`
	Status        string `json:"status,omitempty"`
	Num           int    `json:"num,omitempty"`
	StartDate     string `json:"start_date,omitempty"`
	SendDate      string `json:"send_date,omitempty"`
	From          string `json:"from,omitempty"`
	To            string `json:"to,omitempty"`
	Ttl           int    `json:"ttl,omitempty"`
	//ProgressQueue string      `json:"progress_queue,omitempty"`
	AnswerQueue string `json:"answer_queue,omitempty"`
	//Log           string      `json:"log,omitempty"`
	//ErrorCode     string      `json:"error_code,omitempty"`
	//Traceback     string      `json:"traceback,omitempty"`
	Data interface{} `json:"data,omitempty"`
}

type DialerShowInfoRequest struct {
	Func string `json:"func"`
}

type DialerShowInfo struct {
	RequestError    string     `json:"requestError"`
	ContainerType   string     `json:"containerType"`
	Status          string     `json:"status"`
	StatusError     string     `json:"statusError"`
	TasksInWork     int        `json:"tasksInWork"`
	LinesLimit      int        `json:"linesLimit"`
	AppVersion      string     `json:"appVersion"`
	PlatformVersion string     `json:"platformVersion"`
	BuildId         BuildId    `json:"buildId"`
	CampaignId      CampaignId `json:"campaignId"`
	DeployedAt      time.Time  `json:"deployedAt"`
	StartedAt       time.Time  `json:"startedAt"`
	ContainerIp     string     `json:"ContainerIp"`
	ContainerName   string     `json:"containerName"`
}

type RmqDialerShowInfo struct {
	RmqCeleryPacket
	Data DialerShowInfo `json:"data"`
}

type MultiphonesPhone string

func (p *MultiphonesPhone) UnmarshalBSONValue(t bsontype.Type, value []byte) error {
	switch t {
	case bson.TypeString:
		s, _, ok := bsoncore.ReadString(value)
		if !ok {
			return fmt.Errorf("invalid value %s", value)
		}
		*p = MultiphonesPhone(s)

	case bson.TypeEmbeddedDocument:
		var phone struct {
			Phone string `bson:"phone"`
		}
		err := bson.UnmarshalValue(t, value, &phone)
		if err != nil {
			return err
		}
		*p = MultiphonesPhone(phone.Phone)

	default:
		return fmt.Errorf("invalid type %s", t)
	}
	return nil
}

type CallerMultiphoneId DataBaseId

func (id CallerMultiphoneId) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return DataBaseId(id).MarshalBSONValue()
}

type CallerMultiphone struct {
	Id     CallerMultiphoneId `bson:"_id"`
	Phones []MultiphonesPhone `bson:"phones"`
}

func (c CallerMultiphone) Equal(c2 CallerMultiphone) bool {
	if c.Id != c2.Id {
		return false
	}
	if len(c.Phones) != len(c2.Phones) {
		return false
	}
	for i := 0; i < len(c.Phones); i++ {
		if c.Phones[i] != c2.Phones[i] {
			return false
		}
	}
	return true
}

type TrunkRuleDialplan struct {
	Step   int    `json:"step" bson:"step"`
	Action string `json:"action" bson:"action"`
	Answer string `json:"answer" bson:"answer"`
	Params M      `json:"params" bson:"params"`
}

type TrunkRule struct {
	Id         string                `json:"_id,omitempty" bson:"_id,omitempty"` // not ObjectId!
	NumA       string                `json:"numA" bson:"numA"`
	NumB       string                `json:"numB" bson:"numB"`
	Server     []string              `json:"server" bson:"server"`
	ParentId   CampaignId            `json:"parent_id" bson:"parent_id"`
	Dialplan   []TrunkRuleDialplan   `json:"dialplan" bson:"dialplan"`
	ExtenGroup map[string]CampaignId `json:"exten_group" bson:"exten_group"`
}

type Trunk struct {
	Name  string      `json:"name" bson:"name"`
	Rules []TrunkRule `json:"rules" bson:"rules"`
	NumA  string      `json:"numA" bson:"numA"`
	NumB  string      `json:"numB" bson:"numB"`
}

type SipLogDialplan struct {
}

type SipLogStatusLog struct {
}

type SipLogLog struct {
	DialplanId string `json:"dialplan_id" bson:"dialplan_id"`
	Step       int    `json:"step" bson:"step"`
	Action     string `json:"action" bson:"action"`
	Datetime   string `json:"datetime" bson:"datetime"`
}

type SipLogId DataBaseId

func (id SipLogId) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return DataBaseId(id).MarshalBSONValue()
}

type SipLog struct {
	Id                SipLogId          `json:"_id,omitempty" bson:"_id,omitempty"`
	CallId            CallId            `json:"call_id" bson:"call_id"`
	TrunkName         string            `json:"trunk_name" bson:"trunk_name"`
	CompanyId         CampaignId        `json:"company_id" bson:"company_id"`
	NumA              string            `json:"num_a" bson:"num_a"`
	NumB              string            `json:"num_b" bson:"num_b"`
	Diversion         string            `json:"diversion" bson:"diversion"`
	Server            string            `json:"server" bson:"server"`
	ContainerServer   string            `json:"container_server" bson:"container_server"`
	Container         string            `json:"container" bson:"container"`
	CurrentDialplanId string            `json:"current_dialplan_id" bson:"current_dialplan_id"`
	SipCallId         string            `json:"sipcallid" bson:"sipcallid"`
	Dialplans         []SipLogDialplan  `json:"dialplans" bson:"dialplans"`
	StatusLogs        []SipLogStatusLog `json:"status_logs" bson:"status_logs"`
	Logs              []SipLogLog       `json:"logs" bson:"logs"`
}

type AssistantTechnicalStatus string // в твилио для проверки баланса

const (
	AssistantTechnicalStatus_Active       AssistantTechnicalStatus = "ACTIVE"
	AssistantTechnicalStatus_BalanceBlock AssistantTechnicalStatus = "BALANCE_BLOCK"
)

type AgentDataAssistantSettings struct {
	AssistantTechnicalStatus AssistantTechnicalStatus `json:"assistant_technical_status"`
	Status                   string                   `json:"status"`
	MinutesQuantity          int                      `json:"minutes_quantity"`
}

type AgentData struct {
	AssistantSettings AgentDataAssistantSettings `json:"assistant_settings"`
}

type AgentId struct {
	AssistantId int `json:"assistantId"`
}

var CallTasksSortMap = map[string]bson.D{
	CampaignSortType_default: {
		bson.E{
			Key:   "priority",
			Value: -1,
		},
		bson.E{
			Key:   "nextTz",
			Value: -1,
		},
		bson.E{
			Key:   "nextCallAt",
			Value: 1,
		},
		bson.E{
			Key:   "_id",
			Value: 1,
		},
	},
	CampaignSortType_zeroAttemptsFirst: {
		bson.E{
			Key:   "attemptNumber",
			Value: 1,
		},
		bson.E{
			Key:   "priority",
			Value: -1,
		},
		bson.E{
			Key:   "nextTz",
			Value: -1,
		},
		bson.E{
			Key:   "nextCallAt",
			Value: 1,
		},
		bson.E{
			Key:   "_id",
			Value: 1,
		},
	},
	CampaignSortType_farTzFirst: {
		bson.E{
			Key:   "nextTz",
			Value: -1,
		},
		bson.E{
			Key:   "priority",
			Value: -1,
		},
		bson.E{
			Key:   "nextCallAt",
			Value: 1,
		},
		bson.E{
			Key:   "_id",
			Value: 1,
		},
	},
}
