module gitlab.prod.kvint.io/kvint-core/kvint-caller-go

go 1.21.5

require (
	github.com/caarlos0/env/v6 v6.10.1
	github.com/go-echarts/go-echarts/v2 v2.4.2
	github.com/go-telegram-bot-api/telegram-bot-api/v5 v5.5.1
	github.com/google/uuid v1.6.0
	github.com/gorilla/mux v1.8.1
	github.com/minio/minio-go/v7 v7.0.69
	github.com/pkg/errors v0.9.1
	github.com/rs/zerolog v1.31.0
	github.com/wagslane/go-rabbitmq v0.12.4
	go.mongodb.org/mongo-driver v1.13.1
	go.uber.org/automaxprocs v1.5.3
	go.uber.org/goleak v1.2.0
	golang.org/x/crypto v0.19.0
	golang.org/x/time v0.10.0
)

require (
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/golang/snappy v0.0.1 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/compress v1.17.6 // indirect
	github.com/klauspost/cpuid/v2 v2.2.6 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.19 // indirect
	github.com/minio/md5-simd v1.1.2 // indirect
	github.com/minio/sha256-simd v1.0.1 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/montanaflynn/stats v0.0.0-20171201202039-1bf9dbcd8cbe // indirect
	github.com/rabbitmq/amqp091-go v1.7.0 // indirect
	github.com/rs/xid v1.5.0 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/youmark/pkcs8 v0.0.0-20181117223130-1be2e3e5546d // indirect
	golang.org/x/net v0.21.0 // indirect
	golang.org/x/sync v0.0.0-20220722155255-886fb9371eb4 // indirect
	golang.org/x/sys v0.17.0 // indirect
	golang.org/x/text v0.14.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
)
