package logger

import (
	"github.com/rs/zerolog/pkgerrors"
	"io"
	"net"
	"os"
	"path/filepath"
	"runtime"
	"strconv"

	"github.com/rs/zerolog"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
)

var (
	DefaultLogger   = New(config.Cfg.LogLevel)
	_, b, _, _      = runtime.Caller(0)
	basepath        = filepath.Join(filepath.Dir(b), "..")
	timeFieldFormat = "2006-01-02 15:04:05,000"
)

type Logger struct {
	zeroLogger zerolog.Logger
}

func New(level string) Logger {
	var l zerolog.Level
	err := l.UnmarshalText([]byte(level))
	if err != nil {
		l = zerolog.InfoLevel
	}
	zerolog.SetGlobalLevel(l)

	// format
	zerolog.TimeFieldFormat = timeFieldFormat
	zerolog.CallerMarshalFunc = func(pc uintptr, file string, line int) string {
		return file[len(basepath)+1:] + ":" + strconv.Itoa(line)
	}

	// field names
	zerolog.TimestampFieldName = "asctime"
	zerolog.LevelFieldName = "level"
	zerolog.MessageFieldName = "message"
	zerolog.CallerFieldName = "path"
	zerolog.ErrorFieldName = "error"
	zerolog.ErrorStackMarshaler = pkgerrors.MarshalStack

	var consoleWriter io.Writer
	if l < zerolog.InfoLevel {
		consoleWriter = zerolog.ConsoleWriter{
			Out:        os.Stderr,
			TimeFormat: timeFieldFormat,
		}
	} else {
		consoleWriter = os.Stderr
	}
	var writers io.Writer
	if url := config.Cfg.ELK.URL.String(); url != "" {
		elkConn, err := getElkConn(url)
		if err != nil {
			panic(err)
		}
		writers = io.MultiWriter(consoleWriter, elkConn) // Дублируем лог в elk
	} else {
		writers = io.MultiWriter(consoleWriter)
	}

	logger := zerolog.New(writers).With().
		Timestamp().
		CallerWithSkipFrameCount(zerolog.CallerSkipFrameCount + 1).
		Logger()

	return Logger{zeroLogger: logger}
}

func (l Logger) Debug(msg string, args ...interface{}) {
	l.zeroLogger.Debug().Msgf(msg, args...)
}

func (l Logger) Info(msg string, args ...interface{}) {
	l.zeroLogger.Info().Msgf(msg, args...)
}

func (l Logger) Warn(msg string, args ...interface{}) {
	l.zeroLogger.Warn().Msgf(msg, args...)
}

func (l Logger) Error(msg string, args ...interface{}) {
	l.zeroLogger.Error().Msgf(msg, args...)
}

func (l Logger) TraceError(err error) {
	l.zeroLogger.Error().Stack().Err(err).Msgf("")
}

func (l Logger) Fatal(msg string, args ...interface{}) {
	l.zeroLogger.Fatal().Msgf(msg, args...)
}

func (l Logger) Panic(msg string, args ...interface{}) {
	l.zeroLogger.Panic().Msgf(msg, args...)
}

func (l Logger) With(k, v string) Logger {
	l.zeroLogger = l.zeroLogger.With().Str(k, v).Logger()
	return l
}

func getElkConn(addrVal string) (*net.UDPConn, error) {
	addr, err := net.ResolveUDPAddr("udp", addrVal)
	if err != nil {
		return nil, err
	}
	conn, err := net.DialUDP("udp", nil, addr)
	if err != nil {
		return nil, err
	}
	err = conn.SetWriteBuffer(16 * 1024 * 1024) // 16mb
	if err != nil {
		return nil, err
	}
	return conn, nil
}
