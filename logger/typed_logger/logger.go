package logger

import (
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/logger"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
)

var DefaultLogger = New(config.Cfg.LogLevel)

type Logger struct {
	logger.Logger
}

func New(level string) Logger {
	return Logger{
		Logger: logger.New(level),
	}
}

func (l Logger) CampaignId(id types.CampaignId) types.ITypedLogger {
	l.Logger = l.With("campaignId", string(id))
	return l
}

func (l Logger) TaskId(id types.CallTaskId) types.ITypedLogger {
	l.Logger = l.With("taskId", string(id))
	return l
}

func (l Logger) CallId(id types.CallId) types.ITypedLogger {
	l.<PERSON>gger = l.With("callId", string(id))
	return l
}

func (l Logger) QueryId(id string) types.ITypedLogger {
	l.<PERSON>gger = l.With("queryId", id)
	return l
}
