# Caller

<details open>
<summary></summary>

- Выборка и сортировка заданий для диалогов согласно их состоянию и состоянию их кампаний.
- Обновление состояния заданий для диалогов и их кампаний согласно результатам диалогов.
- Определение общих алертов и отправка уведомлений о них.
- Отправка интеграционным сервисам данных о результатах диалогов.
- Ведение журнала с информацией о загруженности линий.
- Балансировка диалогов по серверам и транкам.

</details>

___

## Разделы

<details open>
<summary></summary>

- [Структура проекта](#структура-проекта) 
- [Деплой и запуск](#деплой-и-запуск) 
- [Тестирование](#тестирование) 
- [API](#api) 
- [Связанные сервисы](#связанные-сервисы) 
- [Техническое описание](#техническое-описание) 

</details>

___

## Структура проекта

<details>
<summary></summary>

- [clients](/clients/) - адаптеры для взаимодействия с внешними зависимостями
- [cmd](/cmd/) - исполняемые файлы, точки входа в приложения, файлы с переменными среды
    - [outdoing](/cmd/outdoing/) - исходящий коллер
    - [incoming](/cmd/incoming/) - входящий коллер
    - [twilio](/cmd/twilio/) - twilio коллер
    - [web](/cmd/web/) - агентский web коллер
    - [jivo](/cmd/jivo/) - jivo коллер
    - [tg_caller](/cmd/tg_caller/) - telegram коллер
    - [perf_test](/cmd/jivo/) - утилита для нагрузочного тестирования
- [config](/config/) - структуры с настройками приложения
- [callers](/callers/) - разные типы коллеров
    - [outdoing](/callers/outdoing/) - исходящий коллер
    - [incoming](/callers/incoming/) - входящий коллер
    - [twilio](/callers/twilio/) - twilio коллер
    - [web](/callers/web/) - агентский web коллер
- [types](/types/) - общие структуры и константы
- [utils](/utils/) - общие утилиты
- [logger](/logger/) - логгер
- [jivo](/jivo/) - jivo коллер
- [perf_test](/perf_test/) - утилита для нагрузочного тестирования

</details>

___

## Деплой и запуск

<details>
<summary></summary>

- <h3>Docker</h3>

    - <h4>Исходящий коллер</h4>

        - <h5>Стэйдж</h5>

        [cmd/outdoing/updateStaging.sh](cmd/outdoing/updateStaging.sh) - запуск и обновление ветки develop
        [cmd/outdoing/updateCustomStaging.sh \<version\>](cmd/outdoing/updateCustomStaging.sh) - любой тэг

        - <h5>Агентский стэйдж</h5>

        [cmd/outdoing/updateAgentStaging.sh](cmd/outdoing/updateAgentStaging.sh)  - запуск и обновление

        - <h5>Препрод</h5>

        [cmd/outdoing/updatePreprod.sh](cmd/outdoing/updatePreprod.sh) - запуск и обновление ветки master
        [cmd/outdoing/updateCustomPreprod.sh \<version\>](cmd/outdoing/updateCustomPreprod.sh) - любой тэг

        - <h5>Прод</h5>

        [cmd/outdoing/updateProd.sh \<version\>](cmd/outdoing/updateProd.sh) - запуск и обновление, где `<version>` - версия релиза

        - <h5>Агентский прод</h5>
        
        [cmd/outdoing/updateAgentProd.sh \<version\>](cmd/outdoing/updateAgentProd.sh) - запуск и обновление, где `<version>` - версия релиза

    - <h4>Входящий коллер</h4>

        Для работы требуется запущенный исходящий коллер  

        - <h5>Стэйдж</h5>

        [cmd/incoming/updateStaging.sh](/cmd/incoming/updateStaging.sh) - запуск и обновление

        - <h5>Препрод</h5>

        [cmd/incoming/updatePreprod.sh](/cmd/incoming/updatePreprod.sh) - запуск и обновление

        - <h5>Прод</h5>

        [cmd/incoming/updateProd.sh \<version\>](/cmd/incoming/updateProd.sh) - запуск и обновление, где `<version>` - версия релиза

    - <h4>Twilio коллер</h4>

        Для работы требуется запущенный исходящий коллер  

        - <h5>Стэйдж</h5>

        [cmd/twilio/updateStaging.sh](/cmd/twilio/updateStaging.sh) - запуск и обновление

        - <h5>Прод</h5>

        [cmd/twilio/updateProd.sh \<version\>](/cmd/twilio/updateProd.sh) - запуск и обновление, где `<version>` - версия релиза

    - <h4>Агентский web коллер</h4>

        Для работы требуется запущенный исходящий коллер  

        - <h5>Стэйдж</h5>

        [cmd/web/updateStaging.sh](/cmd/twilio/web/updateStaging.sh) - запуск и обновление

        - <h5>Прод</h5>

        [cmd/web/updateProd.sh \<version\>](/cmd/web/updatetProd.sh) - запуск и обновление, где `<version>` - версия релиза

    - <h4>Jivo коллер</h4>

        Для работы требуется запущенный исходящий коллер  

        - <h5>Стэйдж</h5>

        [cmd/jivo/updateStaging.sh](/cmd/jivo/updateStaging.sh) - запуск и обновление

        - <h5>Прод</h5>

        [cmd/jivo/updateProd.sh \<version\>](/cmd/jivo/updatetProd.sh) - запуск и обновление, где `<version>` - версия релиза

- <h3>Процесс</h3>

    - <h4>Исходящий коллер</h4>

        - <h5>Стэйдж</h5>

        `/bin/bash -c 'source cmd/staging.env && go run cmd/outdoing/main.go'`

        - <h5>Агентский стэйдж</h5>

        `/bin/bash -c 'source cmd/agentOutgoingStaging.env && go run cmd/outdoing/main.go'`

        - <h5>Препрод</h5>

        `/bin/bash -c 'source cmd/preprod.env && go run cmd/outdoing/main.go'`

        - <h5>Прод</h5>

        `/bin/bash -c 'source cmd/prod.env && go run cmd/outdoing/main.go'`

        - <h5>Агентский прод</h5>

        `/bin/bash -c 'source cmd/agentOutgoingProd.env && go run cmd/outdoing/main.go'`

    - <h4>Входящий коллер</h4>

        Для работы требуется запущенный исходящий коллер  

        - <h5>Стэйдж</h5>

        `/bin/bash -c 'source cmd/staging.env && go run cmd/incoming/main.go'`

        - <h5>Препрод</h5>

        `/bin/bash -c 'source cmd/preprod.env && go run cmd/incoming/main.go'`

        - <h5>Прод</h5>

        `/bin/bash -c 'source cmd/prod.env && go run cmd/incoming/main.go'`

    - <h4>Twilio коллер</h4>

        Для работы требуется запущенный исходящий коллер  

        - <h5>Стэйдж</h5>

        `/bin/bash -c 'source cmd/agentIncomingStaging.env && go run cmd/twilio/main.go'`

        - <h5>Прод</h5>

        `/bin/bash -c 'source cmd/agentIncomingProd.env && go run cmd/twilio/main.go'`

    - <h4>Агентский web коллер</h4>

        Для работы требуется запущенный исходящий коллер  

        - <h5>Стэйдж</h5>

        `/bin/bash -c 'source cmd/web/staging.env && go run cmd/web/main.go'`

        - <h5>Прод</h5>

        `/bin/bash -c 'source cmd/web/prod.env && go run cmd/web.go'`

    - <h4>Jivo коллер</h4>

        Для работы требуется запущенный исходящий коллер  

        - <h5>Стэйдж</h5>

        `/bin/bash -c 'source cmd/jivo/staging.env && go run cmd/jivo/main.go'`

        - <h5>Прод</h5>

        `/bin/bash -c 'source cmd/jivo/prod.env && go run cmd/jivo/main.go'`

</details>

___

## Тестирование

<details>
<summary></summary>

### Unit тесты

`go test -race --timeout 5m ./...`

### Нагрузочное тестирование

[cmd/perf_test/README.MD](/cmd/perf_test/README.MD)

</details>

___

## API

<details>
<summary></summary>

Сервис принимает сообщения через RabbitMQ из очереди с названием, указанной в переменной окружения `RMQ_QUEUE_DIALER_RESULTS` (далее префикс) и очередей вида префикс.N, где N - порядковый целочисленный положительный номер очереди, и максимальное его значение указано в переменной окружения `DIALER_CONSUME_CONCURRENCY`.

Например, при `RMQ_QUEUE_DIALER_RESULTS=kv.caller.dialer_response` и `DIALER_CONSUME_CONCURRENCY=2` сервис будет принимать сообщения из очередей:

```
kv.caller.dialer_response.0
kv.caller.dialer_response.1
kv.caller.dialer_response.2
```

На каждой из этих очередей есть по одному консьюмеру со своим пуллом заданий на звонок (CALL_STARTED, см. далее) ожидающих ответа по RPC протоколу.

Принимаются сообщения следующего вида, где body – json в байтах сжатых в gzip формате:
1. Добавить в пулл данные о начале звонка. Используется преимущественно входящими коллерами. Исходящий коллер делает это сам.

    ```
    headers: {
        "task": "CALL_STARTED"
    }
    body: {
        "CallId": string,
        "Campaign": types.Campaign,
        "CallTaskState": services.CallTaskState
    }
    ```

2. Задать промежуточный статус звонка

    ```
    headers: {
        "task": "CALL_RESULT"
    }
    body: {
        "status": "Progress",
        "data": types.DialerStatus
    }
    ```

3. Сообщить о завершении звонка

    ```
    headers: {
        "task": "CALL_RESULT"
    }
    body: {
        "status": "Complete",
        "data": types.DialerResult
    }
    ```

</details>

___

## Связанные сервисы

<details>
<summary></summary>

![](/services_integration.png)

</details>

___

## Техническое описание

<details>
<summary></summary>

Это statefull-сервис, и он не масштабируется горизонтально: допустим лишь один его процесс в системе

</details>

___

## Путь обработки тасок

<details>
<summary></summary>

### Исходящие

- 

</details>

___