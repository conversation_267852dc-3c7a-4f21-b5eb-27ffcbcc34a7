package utils

import (
	"encoding/json"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/bsontype"
	"go.mongodb.org/mongo-driver/x/bsonx/bsoncore"
)

func BsonUnmarshalStringFromMap[T comparable](val *T, m map[T]string, t bsontype.Type, value []byte) error {
	if t != bson.TypeString {
		return fmt.Errorf("invalid type '%v'", t)
	}
	str, _, ok := bsoncore.ReadString(value)
	if !ok {
		return fmt.Errorf("invalid value '%s'", value)
	}
	*val, ok = ReverseMapSearch(str, m)
	if !ok {
		return fmt.Errorf("invalid value '%s'", str)
	}
	return nil
}

func UnmarshalJsonStringFromMap[T comparable](val *T, m map[T]string, value []byte) error {
	var (
		str string
		ok  bool
	)
	if err := json.Unmarshal(value, &str); err != nil {
		return err
	}
	*val, ok = ReverseMapSearch(str, m)
	if !ok {
		return fmt.Errorf("invalid value '%s'", str)
	}
	return nil
}

func BsonMarshalStringFromMap[T comparable](val T, m map[T]string) (bsontype.Type, []byte, error) {
	if res, ok := m[val]; ok {
		return bson.MarshalValue(res)
	}
	return 0, nil, fmt.Errorf("invalid value '%+v'", val)
}

func MarshalJsonStringFromMap[T comparable](val T, m map[T]string) ([]byte, error) {
	if res, ok := m[val]; ok {
		return json.Marshal(res)
	}
	return nil, fmt.Errorf("invalid value '%+v'", val)
}

func ReverseMapSearch[T comparable](val string, m map[T]string) (T, bool) {
	for k, v := range m {
		if v == val {
			return k, true
		}
	}
	var t T
	return t, false
}
