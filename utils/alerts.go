package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"net/http"

	"github.com/google/uuid"
	"github.com/wagslane/go-rabbitmq"
)

type Publisher interface {
	Publish(data []byte, routingKeys []string, optionFuncs ...func(*rabbitmq.PublishOptions)) error
}

type TgDirectSender struct {
	chatId string
	token  string
}

func MakeTgDirectSender(chatId, token string) TgDirectSender {
	return TgDirectSender{
		chatId: chatId,
		token:  token,
	}
}

func (s TgDirectSender) SendAlert(msg interface{}) error {
	str, ok := msg.(string)
	if !ok {
		return fmt.Errorf("invalid message %s", msg)
	}
	return SendTgMessage(s.chatId, s.token, str)
}

type TgKvintServiceSender struct {
	endpoint  string
	channelId string
	apiKey    string
}

func MakeTgKvintServiceSender(endpoint, channelId, apiKey string) TgKvintServiceSender {
	return TgKvintServiceSender{
		endpoint:  endpoint,
		channelId: channelId,
		apiKey:    apiKey,
	}
}

func (s TgKvintServiceSender) SendAlert(msg interface{}) error {
	str, ok := msg.(string)
	if !ok {
		return fmt.Errorf("invalid message %s", msg)
	}
	payload := map[string]string{
		"text":    str,
		"channel": s.channelId,
	}
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return err
	}
	req, err := http.NewRequest(http.MethodPost, s.endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("xi-api-key", s.apiKey)

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()
	return nil
}

type IntegrationsSender struct {
	publisher Publisher
	queues    []string
}

func MakeIntegrationsSender(publisher Publisher) IntegrationsSender {
	return IntegrationsSender{publisher: publisher}
}

func (s IntegrationsSender) InQueue(queues []string) IntegrationsSender {
	s.queues = queues
	return s
}

func (s IntegrationsSender) SendAlert(msg interface{}) error {
	if len(s.queues) < 1 {
		return errors.New("empty queues")
	}
	b, err := json.Marshal(msg)
	if err != nil {
		return err
	}
	gzipped, err := GzipData(b)
	if err != nil {
		return err
	}
	headers := rabbitmq.Table{"task": "alert"}
	return s.publisher.Publish(
		gzipped,
		s.queues,
		rabbitmq.WithPublishOptionsHeaders(headers),
		rabbitmq.WithPublishOptionsCorrelationID(uuid.New().String()),
		rabbitmq.WithPublishOptionsMandatory,
	)
}
