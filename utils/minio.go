package utils

import (
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

func NewMinioClient(host, port, accessKey, secretKey string, secure bool) (*minio.Client, error) {
	minioClient, err := minio.New(fmt.Sprintf("%s:%s", host, port), &minio.Options{
		Creds:  credentials.NewStaticV4(accessKey, secretKey, ""),
		Secure: secure,
		Transport: &http.Transport{
			Dial: func(network, addr string) (net.Conn, error) {
				return net.DialTimeout(network, addr, 1*time.Minute)
			},
		},
	})
	if err != nil {
		fmt.Printf("[ERROR] NewMinioClient: %s", err)
	}
	return minioClient, err
}

type MinioClientConf struct {
	Host      string
	Port      string
	AccessKey string
	SecretKey string
	Secure    bool
}

func SaveTwilioAudio(callSid, bucket string, reader io.ReadCloser, objectSize int64, minioClientConf MinioClientConf, rndGen RandomGenerator) (string, error) {
	minioClient, err := NewMinioClient(minioClientConf.Host, minioClientConf.Port,
		minioClientConf.AccessKey, minioClientConf.SecretKey, minioClientConf.Secure)
	if err != nil {
		return "", err
	}
	fName := fmt.Sprintf("public_/%s/%s/%s.wav", RandStringRunes(2, rndGen), RandStringRunes(2, rndGen), callSid)
	info, err := minioClient.PutObject(context.Background(), bucket, fName,
		reader, objectSize, minio.PutObjectOptions{})
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("s3://fast|%s/%s", bucket, info.Key), nil
}
