package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

func SendTgMessage(chatId, token, msg string) error {
	apiUrl := fmt.Sprintf("https://api.telegram.org/bot%s/sendMessage", token)
	params := map[string]string{
		"chat_id": chatId,
		"text":    msg,
	}

	payload, err := json.Marshal(params)
	if err != nil {
		return err
	}
	req, err := http.NewRequest("POST", apiUrl, bytes.NewBuffer(payload))
	if err != nil {
		return err
	}
	req.Header.Add("Content-Type", "application/json")
	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()
	return nil
}
