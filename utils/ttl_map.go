package utils

import (
	"sync"
	"time"
)

type TTLValue[T any] struct {
	Value T
	ExpAt time.Time
}

// TTLMap - simple struct to store values in runtime, and clean them on get try (for simplicity, it's not cleaning data automatically)
type TTLMap[K comparable, V any] struct {
	mu           sync.RWMutex
	ttl          time.Duration
	values       map[K]TTLValue[V]
	lastUpdateAt time.Time
}

func MakeTTLMap[K comparable, V any](ttl time.Duration) *TTLMap[K, V] {
	return &TTLMap[K, V]{
		ttl:          ttl,
		values:       make(map[K]TTLValue[V]),
		lastUpdateAt: time.Now(),
	}
}

func (m *TTLMap[K, V]) Set(key K, value V) {
	m.mu.Lock()
	defer m.mu.Unlock()

	// drop all values if map wasn't used for 1 hour
	if time.Since(m.lastUpdateAt) > time.Hour {
		m.values = make(map[K]TTLValue[V])
	}

	m.lastUpdateAt = time.Now()
	m.values[key] = TTLValue[V]{
		Value: value,
		ExpAt: time.Now().Add(m.ttl),
	}
}

func (m *TTLMap[K, V]) Reset(key K) {
	m.mu.Lock()
	defer m.mu.Unlock()
	delete(m.values, key)
}

func (m *TTLMap[K, V]) Get(key K) (V, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	v, ok := m.values[key]
	if !ok {
		return v.Value, ok
	}
	if time.Now().After(v.ExpAt) {
		// no delete here, to not block map with mutex. It's lazy cache, as soon as trunks/campaigns needed not many memory to store
		return v.Value, false
	}
	return v.Value, ok
}
