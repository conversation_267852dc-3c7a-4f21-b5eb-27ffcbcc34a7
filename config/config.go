package config

import (
	"net/url"

	"github.com/caarlos0/env/v6"
)

var Cfg = getConfig()

type (
	Config struct {
		Mongo                    `envPrefix:"MONGO_"`
		RMQ                      `envPrefix:"RMQ_"`
		Minio                    `envPrefix:"MINIO_"`
		ELK                      `envPrefix:"ELK_"`
		KvintAPI                 `envPrefix:"KVINT_API_"`
		Telegram                 `envPrefix:"TELEGRAM_"`
		Twilio                   `envPrefix:"TWILIO_"`
		TeleIncoming             `envPrefix:"TELE_INCOMING_"`
		Jivo                     `envPrefix:"JIVO_"`
		Web                      `envPrefix:"WEB_"`
		LogLevel                 string `env:"LOG_LEVEL" envDefault:"info"`
		DialerConsumeConcurrency int    `env:"DIALER_CONSUME_CONCURRENCY" envDefault:"1" json:"DIALER_CONSUME_CONCURRENCY"`
		SyncDBInterval           int    `env:"SYNC_DB_INTERVAL" envDefault:"1" json:"SYNC_DB_INTERVAL"`
		IntegrationAlert         bool   `env:"INTEGRATIONS_ALERT"`
		TgCaller
	}
	Mongo struct {
		MongoUri                          url.URL `env:"URI" envDefault:"mongodb://localhost:27017/"`
		MongoDbName                       string  `env:"DB_NAME" envDefault:"caller"`
		MongoCampaignCollection           string  `env:"CAMPAIGN_COLLECTION" envDefault:"campaigns"`
		MongoCallTaskCollection           string  `env:"CALL_TASK_COLLECTION" envDefault:"call_tasks"`
		MongoCallResultCollection         string  `env:"CALL_RESULT_COLLECTION" envDefault:"records"`
		MongoCampaignLoadPointsCollection string  `env:"CAMPAIGN_LOAD_POINTS" envDefault:"campaign_load_points"`
		MongoMultiphoneDatabase           string  `env:"MULTIPHONE_DATABASE" envDefault:"heroku_ts1pfnh0"`
		MongoMultiphoneCollection         string  `env:"MULTIPHONE_COLLECTION" envDefault:"campaign.multiphones"`
		MongoTrunksDatabase               string  `env:"TRUNKS_DATABASE" envDefault:"heroku_ts1pfnh0"`
		MongoTrunksCollection             string  `env:"TRUNKS_COLLECTION" envDefault:"sip_trunks"`
		MongoSipLogsCollection            string  `env:"SIP_LOGS_COLLECTION" envDefault:"sip_logs"`
	}
	RMQ struct {
		RmqUri            url.URL `env:"URI" envDefault:"amqp://admin:admin@localhost:5672/"`
		DialerResults     string  `env:"QUEUE_DIALER_RESULTS" envDefault:"kv.caller.dialer_response"`
		DialerQueueSuffix string  `env:"DIALER_QUEUE_SUFFIX" envDefault:"kv.dialer"`
	}
	Minio struct {
		Host             string `env:"HOST" envDefault:"host"`
		Port             string `env:"PORT" envDefault:"9000"`
		AccessKey        string `env:"ACCESS_KEY" envDefault:"key"`
		SecretKey        string `env:"SECRET_KEY" envDefault:"key"`
		Secure           bool   `env:"SECURE" envDefault:"true"`
		MinioAudioBucket string `env:"AUDIO_BUCKET" envDefault:"audio-fulls"`
	}
	ELK struct {
		URL url.URL `env:"URL"`
	}
	KvintAPI struct {
		Url      url.URL `env:"URL"`
		Login    string  `env:"LOGIN"`
		Password string  `env:"PASSWORD"`
	}
	Telegram struct {
		ErrorToken            string `env:"ERROR_TOKEN"`
		ErrorChatId           string `env:"ERROR_CHAT_ID"`
		InfoToken             string `env:"INFO_TOKEN"`
		InfoChatId            string `env:"INFO_CHAT_ID"`
		Direct                bool   `env:"SEND_DIRECT_ALERT"`
		KvintService          bool   `env:"KVINT_SEND_SERVICE_ALERT"`
		KvintServiceEndpoint  string `env:"KVINT_SERVICE_KVINT_ENDPOINT"`
		KvintServiceChannelId string `env:"KVINT_SERVICE_CHANNEL_ID"`
		KvintServiceApiKey    string `env:"KVINT_SERVICE_API_KEY"`
	}
	Twilio struct {
		AccountSid      string  `env:"ACCOUNT_SID" envDefault:"**********************************"`
		TokenAuth       string  `env:"TOKEN_AUTH" envDefault:"d1ab7f0d2ce80a63319c6be240903dd6"`
		AgentPromptsUrl url.URL `env:"AGENT_PROMPTS_URL"`
		MainWsUrl       url.URL `env:"MAIN_WS_URL"`
	}
	TeleIncoming struct {
		Port       string `env:"PORT" envDefault:"5004"`
		SecretKey  string `env:"SECRET_KEY" envDefault:"SF7Bbtsa28"`
		InstanceId string `env:"INSTANCE_ID" envDefault:"1"`
	}
	Jivo struct {
		CampaignId        string  `env:"CAMPAIGN_ID"`
		BotId             string  `env:"BOT_ID"`      // TODO: get from campaign settings
		ProviderId        string  `env:"PROVIDER_ID"` // TODO: get from campaign settings
		Port              string  `env:"PORT"`
		AgentId           string  `env:"AGENT_ID"`
		DialerQueuePrefix string  `env:"DIALER_QUEUE_PREFIX" envDefault:"kv.caller.jivo."`
		BotApiUri         url.URL `env:"BOT_API_URI"`
	}
	Web struct {
		Queue        string  `env:"QUEUE"`
		WebMainWsUrl url.URL `env:"MAIN_WS_URL"`
	}
	TgCaller struct {
		// Входящие сообщения из телеграма не будут пересылаться в дайлер чаще чем раз в N секунд
		LimitSecondsPerTgMessageIn int64 `env:"LIMIT_SECONDS_PER_TG_MESSAGE_IN" envDefault:"5"`
		// Скорость отправки сообщений в секунду одним тг-ботом, в беслатной телеге ограничение 30
		LimitSendMsgsPerSecond int64 `env:"LIMIT_SEND_MSGS_PER_SECOND" envDefault:"29"`
		// Количество попыток отправки сообщения в телеграм
		TgSendAttempts int64 `env:"TG_SEND_ATTEMPTS" envDefault:"3"`
		// Максимальная длина сообщения для отправки в дайлер
		MaxTgMsgLength int `env:"MAX_TG_MSG_LENGTH" envDefault:"2048"`
		// Таймаут создания сессии на дайлере в секундах
		CreateSessionTimeout int64 `env:"CREATE_SESSION_TIMEOUT" envDefault:"30"`
	}
)

func getConfig() (cfg Config) {
	err := env.Parse(&cfg)
	if err != nil {
		panic(err)
	}
	return cfg
}
