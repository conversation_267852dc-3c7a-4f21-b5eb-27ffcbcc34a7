package incoming

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"

	"github.com/wagslane/go-rabbitmq"
	incoming_utils "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/incoming/utils"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/outdoing"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

type DialerOnTaskReceived struct {
	Error     error
	Server    string // SERVER_IDENT (like `1`)
	Container string // CONTAINER_IDENT (like `06`)
}

// ActiveTasksRequests map, to catch responses from dialer and send to correct ongoing request on caller
type ActiveTasksRequests struct {
	requests map[types.CallTaskId]chan types.DialerServerInfo // key = task_id
	mu       sync.Mutex
}

func (a *ActiveTasksRequests) SetRequest(taskId types.CallTaskId, chResult chan types.DialerServerInfo) error {
	a.mu.Lock()
	defer a.mu.Unlock()
	if _, prs := a.requests[taskId]; prs {
		return errors.New("task_id already registered in requests")
	}
	a.requests[taskId] = chResult
	return nil
}

func (a *ActiveTasksRequests) ResetRequest(taskId types.CallTaskId) {
	a.mu.Lock()
	defer a.mu.Unlock()
	delete(a.requests, taskId)
}

func (a *ActiveTasksRequests) ApplyResponse(dialerInfo types.DialerServerInfo) error {
	a.mu.Lock()
	defer a.mu.Unlock()
	if ch, ok := a.requests[dialerInfo.TaskId]; ok {
		ch <- dialerInfo
		return nil
	}
	return errors.New("not found task id to response")
}

type IServiceActiveTasks interface {
	ConsumeDialerServerInfo(ctx context.Context, conn *rabbitmq.Conn, queue string)
	SendTask(campaign types.Campaign, callId types.CallId, replyTo string, taskId types.CallTaskId, callDate time.Time, params InboundCallParams) (server, container string, err error)
}

// ServiceActiveTasks service to send tasks to dialers and get their server info to make correct response for asterisk
type ServiceActiveTasks struct {
	log                   types.ITypedLogger
	databaseProvider      incoming_utils.IDatabaseProvider
	publisher             types.RmqPublisher
	chResendDialerResults chan interface{}
	queueForDialerInfo    string
	requests              ActiveTasksRequests
}

func (s *ServiceActiveTasks) SendTask(
	campaign types.Campaign,
	callId types.CallId,
	replyTo string,
	taskId types.CallTaskId,
	callDate time.Time,
	params InboundCallParams,
) (server, container string, err error) {
	result := make(chan types.DialerServerInfo, 1)
	campaignData, err := json.Marshal(campaign.FieldData)
	if err != nil {
		return server, container, err
	}
	err = s.requests.SetRequest(taskId, result)
	if err != nil {
		return server, container, err
	}
	defer s.requests.ResetRequest(taskId)

	err = outdoing.NewCallTaskState(campaign, types.CallTask{
		Id:         taskId,
		CampaignId: campaign.Id,
		Phones: []types.TaskPhone{{
			Phone:           params.Phone,
			NormalizedPhone: params.Phone,
		}},
		IsTest:       campaign.IsTest,
		CreatedAt:    callDate,
		NextCallAt:   callDate,
		LastCallId:   callId,
		LastCallerId: params.PhoneTo,
		LastTrunk:    types.TrunkContext{Trunk: params.TrunkName, Server: params.Server},
	}).Push(s.publisher, replyTo)
	if err != nil {
		return server, container, err
	}

	var queue string
	if campaign.CallerSettings.PreventTransitLines {
		serverIdent := strings.Split(params.Server, "-")
		if len(serverIdent) < 1 {
			return server, container, errors.New("failed get server ident")
		}
		queue = campaign.QueueNameByServer(serverIdent[0], config.Cfg.RMQ.DialerQueueSuffix)
	} else {
		queue = campaign.QueueName(config.Cfg.RMQ.DialerQueueSuffix)
	}
	err = types.DialerTask{
		Func: "incoming_task",
		Data: types.DialerTaskData{
			Id:           callId,
			TaskId:       taskId,
			Phone:        params.Phone,
			CompanyId:    campaign.Id,
			BotData:      "{}",
			CampaignData: string(campaignData),
			Timezone:     3,
			CallTtl:      utils.TernarOp(campaign.TrunkSettings.CallTTL == 0, 900, campaign.TrunkSettings.CallTTL),
			IsTest:       campaign.IsTest,
			CallData: types.DialerCallData{
				CallerId:          campaign.TrunkSettings.CallerId,
				Trunk:             "default",
				SecondsForSuccess: campaign.CallerSettings.ShortCallTimeout,
				CallTaskId:        taskId,
				Dest:              fmt.Sprintf("Local/%s@%s", params.Phone, "default"),
				Timeout:           40,
				Phone:             params.Phone,
				ExternalId:        "",
				Region:            "",
				CallDate:          callDate.Unix(),
				AttemptNumber:     1,
				DialerCallDataIn: &types.DialerCallDataIn{
					PhoneTo:      params.PhoneTo,
					ExtensionNum: params.ExtensionNum,
				},
			},
			DialerTaskDataIn: &types.DialerTaskDataIn{
				CallerQueueOnTaskReceived: s.queueForDialerInfo,
			},
		},
	}.Push(s.publisher, queue, replyTo)
	if err != nil {
		return server, container, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	select {
	case <-ctx.Done():
		return server, container, errors.New("timeout on waiting server info from dialer")
	case v := <-result:
		return v.Server, v.Container, nil
	}
}

func (s *ServiceActiveTasks) ConsumeDialerServerInfo(ctx context.Context, conn *rabbitmq.Conn, queue string) {
	s.log.Info("consumeDialerReplies, start")
	consumer, err := rabbitmq.NewConsumer(
		conn,
		func(d rabbitmq.Delivery) rabbitmq.Action {
			raw, err := utils.UnzipData(d.Body)
			if err != nil {
				s.log.TraceError(err)
				return rabbitmq.NackDiscard
			}
			var res types.DialerResponseAny
			err = json.Unmarshal(raw, &res)
			if err != nil {
				s.log.TraceError(err)
				return rabbitmq.NackDiscard
			}
			var data types.DialerServerInfo
			err = json.Unmarshal(res.Data, &data)
			if err != nil {
				s.log.TraceError(err)
				return rabbitmq.NackDiscard
			}
			if res.Status != types.CELERY_STATUS_COMPLETE.String() {
				s.log.Error("unknown status - %s", res.Status)
				return rabbitmq.NackDiscard
			}

			if err := s.requests.ApplyResponse(data); err != nil {
				s.log.TraceError(err)
			}
			return rabbitmq.Ack
		},
		queue,
		rabbitmq.WithConsumerOptionsQueueDurable,
		func(options *rabbitmq.ConsumerOptions) {
			args := make(rabbitmq.Table)
			args["x-expires"] = 86400000 // 1day in milliseconds
			options.QueueOptions.Args = args
		},
	)
	if err != nil {
		s.log.Panic("serviceActiveTasks, failed run consumer - %s", err)
	}
	<-ctx.Done()
	consumer.Close()
}

func RunServiceActiveTasks(ctx context.Context, log types.ITypedLogger, conn *rabbitmq.Conn, databaseProvider incoming_utils.IDatabaseProvider) (IServiceActiveTasks, chan struct{}, error) {
	service := &ServiceActiveTasks{
		log:                   log,
		databaseProvider:      databaseProvider,
		chResendDialerResults: make(chan interface{}),
		queueForDialerInfo: fmt.Sprintf("%s.incoming.dialer-info.%s",
			config.Cfg.RMQ.DialerResults, config.Cfg.TeleIncoming.InstanceId),
		requests: ActiveTasksRequests{
			requests: make(map[types.CallTaskId]chan types.DialerServerInfo),
			mu:       sync.Mutex{},
		},
	}

	chDone := make(chan struct{}, 1)
	publisher, err := rabbitmq.NewPublisher(conn)
	if err != nil {
		close(chDone)
		return nil, chDone, fmt.Errorf("failed to run publisher - %w", err)
	}
	publisher.NotifyReturn(func(r rabbitmq.Return) {
		log.Error("queue not exist %s", r.RoutingKey)
	})
	service.publisher = publisher

	// dialer reply about server/container
	chDSIDone := make(chan struct{}, 1)
	go func() {
		defer close(chDSIDone)
		service.ConsumeDialerServerInfo(ctx, conn, service.queueForDialerInfo)
	}()

	go func() {
		defer close(chDone)
		defer publisher.Close()
		<-chDSIDone
	}()

	return service, chDone, nil
}
