package incoming

import (
	"strings"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
)

const (
	FailType_NotFoundExtensionNum = "not found extension num"
	FailType_NotFoundRules        = "not found rules"
	FailType_NotFoundCampaign     = "not found campaign"
	FailType_CampaignNotActive    = "campaign not active"
	FailType_NotFoundGeneralRules = "not found general rules"
	FailType_DialerNoResponse     = "dialer no response"
	FailType_EmptySipLog          = "empty sip_log"
	FailType_UncommonError        = "uncommon error"
)

func RecoveryPrefixNumber(phone string) string {
	if phone != "" && strings.HasPrefix(phone, " ") {
		phone = "+" + phone[1:]
	}
	return phone
}

func DropPrefixNumber(phone string) string {
	if phone != "" && phone[0] == '+' {
		phone = " " + phone[1:]
	}
	return phone
}

func contains(slice []string, item string) bool {
	for _, elem := range slice {
		if elem == item {
			return true
		}
	}
	return false
}

func CheckCondition(log types.ILogger, rule types.TrunkRule, server, phone, phoneTo, numA, numB, condName string) bool {
	log.Info("checkCondition, rule=%v, server=%s, phone=%s, phone_to=%s, num_a=%s, num_b=%s, cond_name=%s",
		rule, server, phone, phoneTo, numA, numB, condName)

	switch condName {
	case "server_and_B":
		if contains(rule.Server, server) && numB == phoneTo {
			log.Info("checkCondition, rule: %v, server_and_B\n", rule.Id)
			return true
		}

	case "server_and_AB_all":
		if contains(rule.Server, server) && rule.NumB == "*" && rule.NumA == "*" {
			log.Info("checkCondition, rule: %v, server_and_AB_all\n", rule.Id)
			return true
		}

	case "no_server_and_AB_all_or_AB":
		if len(rule.Server) == 0 &&
			(rule.NumB == "*" || numB == phoneTo) &&
			(rule.NumA == "*" || numA == phone) {
			log.Info("checkCondition, rule: %v, no_server_and_AB_all_or_AB\n", rule.Id)
			return true
		}
	}
	return false
}
