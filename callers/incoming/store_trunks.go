package incoming

import (
	"context"
	"github.com/pkg/errors"
	"sort"
	"time"

	incoming_utils "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/incoming/utils"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

type StoreTrunks struct {
	*utils.TTLMap[string, types.Trunk]
}

func MakeStoreTrunks(ttl time.Duration) StoreTrunks {
	return StoreTrunks{
		utils.MakeTTLMap[string, types.Trunk](ttl),
	}
}

func (s *StoreTrunks) GetTrunk(trunksProvider incoming_utils.ITrunksProvider, name string) (types.Trunk, error) {
	v, ok := s.Get(name)
	if ok {
		return v, nil
	}

	trunk, err := trunksProvider.GetTrunk(context.Background(), name)
	if err != nil {
		return v, err
	}

	// IMPORTANT: part of caller logic, running here to make sorting only once
	sort.SliceStable(trunk.Rules, func(i, j int) bool {
		aIsAny := trunk.Rules[i].NumA == "*" || trunk.Rules[i].NumB == "*"
		bIsAny := trunk.Rules[j].NumA == "*" || trunk.Rules[j].NumB == "*"
		return !aIsAny && bIsAny
	})

	sort.SliceStable(trunk.Rules, func(i, j int) bool {
		aIsAny := trunk.Rules[i].NumA == "*" && trunk.Rules[i].NumB == "*" && len(trunk.Rules[i].Server) == 0
		bIsAny := trunk.Rules[j].NumA == "*" && trunk.Rules[j].NumB == "*" && len(trunk.Rules[j].Server) == 0
		return !aIsAny && bIsAny
	})

	s.Set(name, trunk)
	return trunk, nil
}

func (s *StoreTrunks) GetTrunkRule(trunksProvider incoming_utils.ITrunksProvider, name, ruleId string) (types.TrunkRule, error) {
	var trunkRule types.TrunkRule
	trunk, err := s.GetTrunk(trunksProvider, name)
	if err != nil {
		return trunkRule, err
	}

	for _, v := range trunk.Rules {
		if v.Id == ruleId {
			return v, nil
		}
	}

	return trunkRule, errors.New("not found rule")
}
