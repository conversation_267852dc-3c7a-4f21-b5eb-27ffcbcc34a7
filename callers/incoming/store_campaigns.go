package incoming

import (
	"context"
	"github.com/pkg/errors"
	"time"

	incoming_utils "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/incoming/utils"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

type StoreCampaigns struct {
	*utils.TTLMap[types.CampaignId, types.Campaign]
}

func MakeStoreCampaigns(ttl time.Duration) StoreCampaigns {
	return StoreCampaigns{
		utils.MakeTTLMap[types.CampaignId, types.Campaign](ttl),
	}
}

func (s *StoreCampaigns) GetCampaign(ctx context.Context, databaseProvider incoming_utils.IDatabaseProvider, id types.CampaignId) (types.Campaign, error) {
	v, ok := s.Get(id)
	if ok {
		return v, nil
	}

	campaign, err := databaseProvider.GetCampaignById(ctx, id)
	if err != nil {
		return v, err
	}
	if campaign.CallDirection != types.CAMPAIGN_CALL_DIRECTION_IN {
		return v, errors.New("campaign is not incoming")
	}

	s.Set(id, campaign)
	return campaign, nil
}
