package incoming

import (
	"context"
	"time"

	incoming_utils "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/incoming/utils"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

type StoreTasks struct {
	*utils.TTLMap[types.CallTaskId, types.ExtendedCallTask]
}

func MakeStoreTasks(ttl time.Duration) StoreTasks {
	return StoreTasks{
		utils.MakeTTLMap[types.CallTaskId, types.ExtendedCallTask](ttl),
	}
}

func (s *StoreTasks) SetTask(id types.CallTaskId, task types.ExtendedCallTask) {
	s.Set(id, task)
}

func (s *StoreTasks) GetTask(ctx context.Context, databaseProvider incoming_utils.IDatabaseProvider, id types.CallTaskId) (types.ExtendedCallTask, error) {
	v, ok := s.Get(id)
	if ok {
		return v, nil
	}
	return databaseProvider.GetCallTaskById(ctx, id)
}

func (s *StoreTasks) ResetTask(ctx context.Context, databaseProvider incoming_utils.IDatabaseProvider, id types.CallTaskId) {
	s.Reset(id)
}
