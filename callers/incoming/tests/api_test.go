package incoming

import (
	"bytes"
	"context"
	"fmt"
	"github.com/pkg/errors"
	"io"
	"net/http"
	"net/url"
	"strings"
	"testing"
	"time"

	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/incoming"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

type MockLogger struct {
	wr io.Writer
}

func (l MockLogger) Debug(msg string, args ...interface{}) {
	l.wr.Write([]byte(fmt.Sprintf(msg, args...)))
}
func (l MockLogger) Info(msg string, args ...interface{}) {
	l.wr.Write([]byte(fmt.Sprintf(msg, args...)))
}
func (l MockLogger) Warn(msg string, args ...interface{}) {
	l.wr.Write([]byte(fmt.Sprintf(msg, args...)))
}
func (l MockLogger) Error(msg string, args ...interface{}) {
	l.wr.Write([]byte(fmt.Sprintf(msg, args...)))
}
func (l MockLogger) TraceError(err error) {
	l.wr.Write([]byte(err.Error()))
}
func (l MockLogger) Fatal(msg string, args ...interface{}) {
	l.wr.Write([]byte(fmt.Sprintf(msg, args...)))
}
func (l MockLogger) Panic(msg string, args ...interface{}) {
	l.wr.Write([]byte(fmt.Sprintf(msg, args...)))
}
func (l MockLogger) CampaignId(types.CampaignId) types.ITypedLogger {
	return l
}
func (l MockLogger) TaskId(types.CallTaskId) types.ITypedLogger {
	return l
}
func (l MockLogger) CallId(types.CallId) types.ITypedLogger {
	return l
}
func (l MockLogger) QueryId(string) types.ITypedLogger {
	return l
}

type MockDatabaseProvider struct {
	campaigns              []types.Campaign
	callsCountCreateRecord int
	callsCountCreateTask   int
}

func (mc *MockDatabaseProvider) GetCampaignById(ctx context.Context, id types.CampaignId) (campaign types.Campaign, err error) {
	for _, v := range mc.campaigns {
		if v.Id == id {
			return v, nil
		}
	}
	return types.Campaign{}, errors.New("mock campaign not found")
}

func (mc *MockDatabaseProvider) CreateRecord(ctx context.Context, callRecord types.CallResult) error {
	mc.callsCountCreateRecord++
	return nil
}

func (mc *MockDatabaseProvider) CreateIncomingTask(ctx context.Context, callTask types.ExtendedCallTask) (types.ExtendedCallTask, error) {
	mc.callsCountCreateTask++
	return types.ExtendedCallTask{}, nil
}

func (mc *MockDatabaseProvider) UpdateTask(ctx context.Context, taskId types.CallTaskId, update types.M) (int, error) {
	return 1, nil
}

func (mc *MockDatabaseProvider) GetCallTaskById(ctx context.Context, id types.CallTaskId) (callTask types.ExtendedCallTask, err error) {
	return types.ExtendedCallTask{}, nil
}

type MockTrunksProvider struct {
	trunks                 []types.Trunk
	sipLogs                []types.SipLog
	callsCountUpdateSipLog int
	callsCountCreateSipLog int
}

func (mt *MockTrunksProvider) GetTrunk(ctx context.Context, name string) (types.Trunk, error) {
	for _, v := range mt.trunks {
		if v.Name == name {
			return v, nil
		}
	}
	return types.Trunk{}, errors.New("mock trunk not found")
}

func (mt *MockTrunksProvider) GetSipLogByCallId(ctx context.Context, callId types.CallId) (types.SipLog, error) {
	for _, v := range mt.sipLogs {
		if v.CallId == callId {
			return v, nil
		}
	}
	return types.SipLog{}, errors.New("mock sip log not found")
}

func (mt *MockTrunksProvider) UpdateSipLog(ctx context.Context, sipLogId types.SipLogId, update types.M) error {
	mt.callsCountUpdateSipLog++
	return nil
}

func (mt *MockTrunksProvider) CreateSipLog(ctx context.Context, sipLog types.SipLog) error {
	mt.callsCountCreateSipLog++
	return nil
}

type MockActiveTasks struct {
	dialerReply incoming.DialerOnTaskReceived
}

func (ma *MockActiveTasks) ConsumeDialerServerInfo(ctx context.Context, conn *rabbitmq.Conn, queue string) {
}

func (ma *MockActiveTasks) SendTask(
	campaign types.Campaign,
	callId types.CallId,
	replyTo string,
	taskId types.CallTaskId,
	callDate time.Time,
	params incoming.InboundCallParams,
) (server, container string, err error) {
	return ma.dialerReply.Server, ma.dialerReply.Container, nil
}

func TestApiInboundCall(t *testing.T) {
	defaultCampaign := types.Campaign{
		Id:            "123",
		IsActive:      true,
		CallDirection: types.CAMPAIGN_CALL_DIRECTION_IN,
	}
	defaultNumA := "79148050700"
	defaultNumB := "79990559618"
	defaultCallId := types.CallId("call-id-default")
	defaultTrunkName := "trunk-default"
	defaultServer := "server-default"
	defaultSecretKey := config.Cfg.TeleIncoming.SecretKey

	// WARNING: campaigns and trunks will be cached, so in every test names should be unique
	tests := []struct {
		name                 string
		queryParams          url.Values
		expectedCode         int
		expectedBody         string
		expectedLog          string
		expectedCreateRecord int
		expectedCreateTask   int
		expectedUpdateSipLog int
		expectedCreateSipLog int
		campaigns            []types.Campaign
		trunks               []types.Trunk
		sipLogs              []types.SipLog
		dialerReply          incoming.DialerOnTaskReceived
	}{
		// getInCallByTrunk tests
		{
			name: "(getInCallByTrunk) wrong secret key",
			queryParams: url.Values{
				"call_uuid":  {""},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName},
				"server":     {defaultServer},
				"secret_key": {"mock-key"},
			},
			campaigns:    []types.Campaign{},
			trunks:       []types.Trunk{},
			sipLogs:      []types.SipLog{},
			expectedCode: http.StatusInternalServerError,
			expectedBody: "busy|",
			expectedLog:  "wrong secret key: mock-key",
		},
		{
			name: "(getInCallByTrunk) trunk not found",
			queryParams: url.Values{
				"call_uuid":  {""},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "1"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
			},
			campaigns:            []types.Campaign{},
			trunks:               []types.Trunk{},
			sipLogs:              []types.SipLog{},
			expectedCode:         http.StatusInternalServerError,
			expectedBody:         "busy|",
			expectedLog:          "failed get trunk",
			expectedCreateRecord: 1,
		},
		{
			name: "(getInCallByTrunk) trunk have no rules",
			queryParams: url.Values{
				"call_uuid":  {""},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "2"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
			},
			campaigns: []types.Campaign{},
			trunks: []types.Trunk{
				{Name: defaultTrunkName + "2", Rules: []types.TrunkRule{}},
			},
			sipLogs:              []types.SipLog{},
			expectedCode:         http.StatusInternalServerError,
			expectedBody:         "busy|",
			expectedLog:          "trunk have no rules",
			expectedCreateRecord: 1,
		},
		{
			name: "(getInCallByTrunk) trunk failed to find decent rule",
			queryParams: url.Values{
				"call_uuid":  {""},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "3"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
			},
			campaigns: []types.Campaign{},
			trunks: []types.Trunk{{
				Name: defaultTrunkName + "3",
				Rules: []types.TrunkRule{{
					NumA: "1",
					NumB: "2",
					Dialplan: []types.TrunkRuleDialplan{{
						Step:   1,
						Action: "dial",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}},
				}},
			}},
			sipLogs:              []types.SipLog{},
			expectedCode:         http.StatusInternalServerError,
			expectedBody:         "busy|",
			expectedLog:          "failed to find decent rule in trunk",
			expectedCreateRecord: 1,
		},
		{
			name: "(getInCallByTrunk) trunk found rule by wildcard numA and numB, but not found campaign",
			queryParams: url.Values{
				"call_uuid":  {""},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "4"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
			},
			campaigns: []types.Campaign{defaultCampaign},
			trunks: []types.Trunk{{
				Name: defaultTrunkName + "4",
				Rules: []types.TrunkRule{{
					NumA:     "*",
					NumB:     "*",
					ParentId: "000000000000000000000000",
					Dialplan: []types.TrunkRuleDialplan{{
						Step:   1,
						Action: "dial",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}},
				}},
			}},
			sipLogs:              []types.SipLog{},
			expectedCode:         http.StatusInternalServerError,
			expectedBody:         "busy|",
			expectedLog:          "failed to load campaign",
			expectedCreateRecord: 1,
		},
		{
			name: "(getInCallByTrunk) found trunk rule without exten_group for waitexten action",
			queryParams: url.Values{
				"call_uuid":  {""},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "5"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
			},
			campaigns: []types.Campaign{defaultCampaign},
			trunks: []types.Trunk{{
				Name: defaultTrunkName + "5",
				Rules: []types.TrunkRule{{
					NumA:       "*",
					NumB:       "*",
					ParentId:   defaultCampaign.Id,
					ExtenGroup: make(map[string]types.CampaignId),
					Dialplan: []types.TrunkRuleDialplan{{
						Step:   1,
						Action: "waitexten",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}},
				}},
			}},
			sipLogs:              []types.SipLog{},
			expectedCode:         http.StatusInternalServerError,
			expectedBody:         "busy|",
			expectedLog:          "empty exten_group for waitexten",
			expectedCreateRecord: 1,
		},
		{
			name: "(getInCallByTrunk) empty campaignId for action dial on first step",
			queryParams: url.Values{
				"call_uuid":  {""},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "6"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
			},
			campaigns: []types.Campaign{defaultCampaign},
			trunks: []types.Trunk{{
				Name: defaultTrunkName + "6",
				Rules: []types.TrunkRule{{
					NumA:     "*",
					NumB:     "*",
					ParentId: "",
					ExtenGroup: map[string]types.CampaignId{
						"23": "mock-id",
					},
					Dialplan: []types.TrunkRuleDialplan{{
						Step:   1,
						Action: "dial",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}},
				}},
			}},
			sipLogs:              []types.SipLog{},
			expectedCode:         http.StatusInternalServerError,
			expectedBody:         "busy|",
			expectedLog:          "empty campaignId on dial action",
			expectedCreateRecord: 1,
		},
		{
			name: "(getInCallByTrunk) correct waitexten step",
			queryParams: url.Values{
				"call_uuid":  {""},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "7"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
			},
			campaigns: []types.Campaign{defaultCampaign},
			trunks: []types.Trunk{{
				Name: defaultTrunkName + "7",
				Rules: []types.TrunkRule{{
					NumA:     "*",
					NumB:     "*",
					ParentId: "",
					ExtenGroup: map[string]types.CampaignId{
						"23": defaultCampaign.Id,
					},
					Dialplan: []types.TrunkRuleDialplan{{
						Step:   1,
						Action: "waitexten",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}},
				}},
			}},
			sipLogs:              []types.SipLog{},
			expectedCode:         http.StatusOK,
			expectedBody:         fmt.Sprintf("waitexten|null|null|docker|%s|%s|10|20|", defaultNumA, defaultNumB),
			expectedCreateRecord: 0,
			expectedCreateSipLog: 1,
		},
		{
			name: "(getInCallByTrunk) correct dial step, wildcard",
			queryParams: url.Values{
				"call_uuid":  {""},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "8"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
			},
			campaigns: []types.Campaign{defaultCampaign},
			trunks: []types.Trunk{{
				Name: defaultTrunkName + "8",
				Rules: []types.TrunkRule{{
					NumA:     "*",
					NumB:     "*",
					ParentId: defaultCampaign.Id,
					ExtenGroup: map[string]types.CampaignId{
						"23": defaultCampaign.Id,
					},
					Dialplan: []types.TrunkRuleDialplan{{
						Step:   1,
						Action: "dial",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}},
				}},
			}},
			sipLogs: []types.SipLog{},
			dialerReply: incoming.DialerOnTaskReceived{
				Server:    "1",
				Container: "06",
			},
			expectedCode:         http.StatusOK,
			expectedBody:         fmt.Sprintf("dial|1|06|docker|%s|%s|10|20|", defaultNumA, defaultNumB),
			expectedCreateTask:   1,
			expectedCreateSipLog: 1,
		},
		{
			name: "(getInCallByTrunk) correct dial step, not wildcard",
			queryParams: url.Values{
				"call_uuid":  {""},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "9"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
			},
			campaigns: []types.Campaign{defaultCampaign},
			trunks: []types.Trunk{{
				Name: defaultTrunkName + "9",
				Rules: []types.TrunkRule{{
					NumA:     "*",
					NumB:     "*",
					ParentId: defaultCampaign.Id,
					ExtenGroup: map[string]types.CampaignId{
						"23": defaultCampaign.Id,
					},
					Dialplan: []types.TrunkRuleDialplan{{
						Step:   1,
						Action: "dial",
						Answer: "{action}|1|05|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}},
				}, {
					NumA:     defaultNumA,
					NumB:     "*",
					ParentId: defaultCampaign.Id,
					ExtenGroup: map[string]types.CampaignId{
						"23": defaultCampaign.Id,
					},
					Dialplan: []types.TrunkRuleDialplan{{
						Step:   1,
						Action: "dial",
						Answer: "{action}|1|07|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}},
				}},
			}},
			sipLogs: []types.SipLog{},
			dialerReply: incoming.DialerOnTaskReceived{
				Server:    "1",
				Container: "06",
			},
			expectedCode:         http.StatusOK,
			expectedBody:         fmt.Sprintf("dial|1|07|docker|%s|%s|10|20|", defaultNumA, defaultNumB),
			expectedCreateTask:   1,
			expectedCreateSipLog: 1,
		},

		// getNonFirstInCall tests
		{
			name: "(getNonFirstInCall) not found sip log",
			queryParams: url.Values{
				"call_uuid":  {string(defaultCallId)},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "10"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
			},
			campaigns: []types.Campaign{defaultCampaign},
			trunks: []types.Trunk{{
				Name:  defaultTrunkName + "10",
				Rules: []types.TrunkRule{},
			}},
			sipLogs:              []types.SipLog{},
			expectedCode:         http.StatusInternalServerError,
			expectedBody:         fmt.Sprintf("busy|%s", defaultCallId),
			expectedLog:          "getNonFirstInCall, not found sip_log",
			expectedCreateRecord: 1,
		},
		{
			name: "(getNonFirstInCall) found sip log, but it's empty",
			queryParams: url.Values{
				"call_uuid":  {string(defaultCallId)},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "11"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
			},
			campaigns: []types.Campaign{defaultCampaign},
			trunks: []types.Trunk{{
				Name:  defaultTrunkName + "11",
				Rules: []types.TrunkRule{},
			}},
			sipLogs: []types.SipLog{{
				CallId: defaultCallId,
			}},
			expectedCode:         http.StatusInternalServerError,
			expectedBody:         fmt.Sprintf("busy|%s", defaultCallId),
			expectedLog:          "getNonFirstInCall, empty sip_log",
			expectedCreateRecord: 1,
		},
		{
			name: "(getNonFirstInCall) found sip log, but rules in trunk empty",
			queryParams: url.Values{
				"call_uuid":  {string(defaultCallId)},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "12"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
			},
			campaigns: []types.Campaign{defaultCampaign},
			trunks: []types.Trunk{{
				Name:  defaultTrunkName + "12",
				Rules: []types.TrunkRule{},
			}},
			sipLogs: []types.SipLog{{
				CallId: defaultCallId,
				Logs: []types.SipLogLog{{
					DialplanId: "dialplan-id",
				}},
			}},
			expectedCode:         http.StatusInternalServerError,
			expectedBody:         fmt.Sprintf("busy|%s", defaultCallId),
			expectedLog:          "getNonFirstInCall, no rules",
			expectedCreateRecord: 1,
		},
		{
			name: "(getNonFirstInCall) found sip log, but exten_num in query empty",
			queryParams: url.Values{
				"call_uuid":  {string(defaultCallId)},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "13"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
			},
			campaigns: []types.Campaign{defaultCampaign},
			trunks: []types.Trunk{{
				Name: defaultTrunkName + "13",
				Rules: []types.TrunkRule{{
					Id: "dialplan-id",
					Dialplan: []types.TrunkRuleDialplan{{
						Step:   1,
						Action: "waitexten",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}, {
						Step:   2,
						Action: "dial",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}},
				}},
			}},
			sipLogs: []types.SipLog{{
				CallId:            defaultCallId,
				CurrentDialplanId: "dialplan-id",
				Logs: []types.SipLogLog{{
					Step: 1,
				}},
			}},
			expectedCode:         http.StatusOK,
			expectedBody:         fmt.Sprintf("hangup|%s", defaultCallId),
			expectedLog:          "getNonFirstInCall, ext num is empty",
			expectedCreateRecord: 1,
		},
		{
			name: "(getNonFirstInCall) found sip log, but exten_group empty",
			queryParams: url.Values{
				"call_uuid":  {string(defaultCallId)},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "13"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
				"extension":  {"123"},
			},
			campaigns: []types.Campaign{defaultCampaign},
			trunks: []types.Trunk{{
				Name: defaultTrunkName + "13",
				Rules: []types.TrunkRule{{
					Id: "dialplan-id",
					Dialplan: []types.TrunkRuleDialplan{{
						Step:   1,
						Action: "waitexten",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}, {
						Step:   2,
						Action: "dial",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}},
				}},
			}},
			sipLogs: []types.SipLog{{
				CallId:            defaultCallId,
				CurrentDialplanId: "dialplan-id",
				Logs: []types.SipLogLog{{
					Step: 1,
				}},
			}},
			expectedCode:         http.StatusOK,
			expectedBody:         fmt.Sprintf("hangup|%s", defaultCallId),
			expectedLog:          "getNonFirstInCall, no campaign by ext_num: 123",
			expectedCreateRecord: 1,
		},
		{
			name: "(getNonFirstInCall) found sip log, but exten_group empty",
			queryParams: url.Values{
				"call_uuid":  {string(defaultCallId)},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "13"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
				"extension":  {"123"},
			},
			campaigns: []types.Campaign{defaultCampaign},
			trunks: []types.Trunk{{
				Name: defaultTrunkName + "13",
				Rules: []types.TrunkRule{{
					Id: "dialplan-id",
					Dialplan: []types.TrunkRuleDialplan{{
						Step:   1,
						Action: "waitexten",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}, {
						Step:   2,
						Action: "dial",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}},
				}},
			}},
			sipLogs: []types.SipLog{{
				CallId:            defaultCallId,
				CurrentDialplanId: "dialplan-id",
				Logs: []types.SipLogLog{{
					Step: 1,
				}},
			}},
			expectedCode:         http.StatusOK,
			expectedBody:         fmt.Sprintf("hangup|%s", defaultCallId),
			expectedLog:          "getNonFirstInCall, no campaign by ext_num: 123",
			expectedCreateRecord: 1,
		},
		{
			name: "(getNonFirstInCall) found sip log, but campaign not found",
			queryParams: url.Values{
				"call_uuid":  {string(defaultCallId)},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "14"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
				"extension":  {"123"},
			},
			campaigns: []types.Campaign{defaultCampaign},
			trunks: []types.Trunk{{
				Name: defaultTrunkName + "14",
				Rules: []types.TrunkRule{{
					Id: "dialplan-id",
					ExtenGroup: map[string]types.CampaignId{
						"123": "000000000000000000000000",
					},
					Dialplan: []types.TrunkRuleDialplan{{
						Step:   1,
						Action: "waitexten",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}, {
						Step:   2,
						Action: "dial",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}},
				}},
			}},
			sipLogs: []types.SipLog{{
				CallId:            defaultCallId,
				CurrentDialplanId: "dialplan-id",
				Logs: []types.SipLogLog{{
					Step: 1,
				}},
			}},
			expectedCode:         http.StatusInternalServerError,
			expectedBody:         fmt.Sprintf("busy|%s", defaultCallId),
			expectedLog:          "getNonFirstInCall, failed get campaign",
			expectedCreateRecord: 1,
		},
		{
			name: "(getNonFirstInCall) get correct answer and sent task to dialer",
			queryParams: url.Values{
				"call_uuid":  {string(defaultCallId)},
				"num_a":      {defaultNumA},
				"num_b":      {defaultNumB},
				"trunk_name": {defaultTrunkName + "15"},
				"server":     {defaultServer},
				"secret_key": {defaultSecretKey},
				"extension":  {"123"},
			},
			campaigns: []types.Campaign{defaultCampaign},
			trunks: []types.Trunk{{
				Name: defaultTrunkName + "15",
				Rules: []types.TrunkRule{{
					Id: "dialplan-id",
					ExtenGroup: map[string]types.CampaignId{
						"123": defaultCampaign.Id,
					},
					Dialplan: []types.TrunkRuleDialplan{{
						Step:   1,
						Action: "waitexten",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}, {
						Step:   2,
						Action: "dial",
						Answer: "{action}|{to_server}|{to_container}|{to_trunk}|{num_a}|{num_b}|{call_limit}|{timeout}|{uuid_call}",
						Params: types.M{
							"call_limit": 10,
							"timeout":    20,
						},
					}},
				}},
			}},
			sipLogs: []types.SipLog{{
				CallId:            defaultCallId,
				NumA:              defaultNumA,
				NumB:              defaultNumB,
				CurrentDialplanId: "dialplan-id",
				Logs: []types.SipLogLog{{
					Step: 1,
				}},
			}},
			dialerReply: incoming.DialerOnTaskReceived{
				Server:    "1",
				Container: "06",
			},
			expectedCode:         http.StatusOK,
			expectedBody:         fmt.Sprintf("dial|1|06|docker|%s|%s|10|20|%s", defaultNumA, defaultNumB, defaultCallId),
			expectedCreateTask:   1,
			expectedUpdateSipLog: 1,
		},
	}

	port := 5000
	ctx := context.Background()
	for _, tt := range tests {
		tt := tt
		port++
		t.Run(tt.name, func(t *testing.T) {

			config.Cfg.TeleIncoming.Port = fmt.Sprintf("%d", port)
			apiUrl := fmt.Sprintf("http://127.0.0.1:%s/inboundCall", config.Cfg.TeleIncoming.Port)

			// run incoming caller with mocked services
			logBuf := bytes.NewBuffer(make([]byte, 0, 5242880)) // 5MB
			mockLogger := MockLogger{wr: logBuf}
			trunksProvider := &MockTrunksProvider{trunks: tt.trunks, sipLogs: tt.sipLogs}
			databaseProvider := &MockDatabaseProvider{campaigns: tt.campaigns}
			activeTasks := &MockActiveTasks{dialerReply: tt.dialerReply}
			teleIncoming := incoming.TeleIncoming{
				Ctx:              ctx,
				Log:              mockLogger,
				TrunksProvider:   trunksProvider,
				DatabaseProvider: databaseProvider,
				ActiveTasks:      activeTasks,
				RndGen:           utils.NewRandomGenerator(ctx),
				QueueGen:         utils.NewSyncRoundRobin(ctx, 40),
			}
			go teleIncoming.Run(context.Background())
			time.Sleep(100 * time.Millisecond)

			// make request
			req, err := http.NewRequest("GET", apiUrl, nil)
			if err != nil {
				t.Error(err)
			}
			req.URL.RawQuery = tt.queryParams.Encode()

			res, err := http.DefaultClient.Do(req)
			if err != nil {
				t.Error(err)
			}
			defer res.Body.Close()

			body, err := io.ReadAll(res.Body)
			if err != nil {
				t.Error(err)
			}

			// Check status code
			if res.StatusCode != tt.expectedCode {
				t.Errorf("handler returned wrong status code: got - %v want - %v", res.StatusCode, tt.expectedCode)
			}

			// Check response body
			if !strings.Contains(string(body), tt.expectedBody) {
				t.Errorf("handler returned unexpected body: got - %v want - %v", string(body), tt.expectedBody)
			}

			// Check logs
			if tt.expectedLog != "" {
				time.Sleep(1 * time.Millisecond)
				logs := logBuf.String()
				if !strings.Contains(logs, tt.expectedLog) {
					t.Errorf("failed to find string in logs: got logs - %v want to find - %v", logs, tt.expectedLog)
					t.Logf("trunks = %+v\n", tt.trunks)
					t.Logf("all = %+v\n", tt)
					v, _ := trunksProvider.GetTrunk(context.Background(), defaultTrunkName)
					t.Logf("\n\nget trunk = %+v", v)
				}
			}

			// Check calls count to database
			if tt.expectedCreateRecord != databaseProvider.callsCountCreateRecord {
				t.Errorf("calls count create record, got - %v, expected - %v",
					tt.expectedCreateRecord, databaseProvider.callsCountCreateRecord)
			}
			if tt.expectedCreateTask != databaseProvider.callsCountCreateTask {
				t.Errorf("calls count create task, got - %v, expected - %v",
					tt.expectedCreateTask, databaseProvider.callsCountCreateTask)
			}
			if tt.expectedCreateSipLog != trunksProvider.callsCountCreateSipLog {
				t.Errorf("calls count create sip log, got - %v, expected - %v",
					tt.expectedCreateSipLog, trunksProvider.callsCountCreateSipLog)
			}
			if tt.expectedUpdateSipLog != trunksProvider.callsCountUpdateSipLog {
				t.Errorf("calls count update sip log, got - %v, expected - %v",
					tt.expectedUpdateSipLog, trunksProvider.callsCountUpdateSipLog)
			}
		})
	}
}
