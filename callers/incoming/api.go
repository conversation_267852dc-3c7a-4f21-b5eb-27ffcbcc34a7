package incoming

import (
	"context"
	"fmt"
	"net/http"
	"runtime/debug"
	"strings"
	"time"

	"github.com/pkg/errors"

	"github.com/gorilla/mux"
	incoming_utils "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/incoming/utils"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

var storeTrunks = MakeStoreTrunks(5 * time.Minute)
var storeCampaigns = MakeStoreCampaigns(5 * time.Minute)

type TeleIncoming struct {
	Ctx              context.Context
	Log              types.ITypedLogger
	TrunksProvider   incoming_utils.ITrunksProvider
	DatabaseProvider incoming_utils.IDatabaseProvider
	ActiveTasks      IServiceActiveTasks
	RndGen           utils.RandomGenerator
	QueueGen         utils.SyncRoundRobin
}

func (ti *TeleIncoming) Run(ctx context.Context) {
	router := mux.NewRouter()
	router.HandleFunc("/inboundCall", ti.ApiInboundCall).Methods("GET", "POST")
	router.HandleFunc("/addCallIn", ti.ApiAddCallIn).Methods("GET", "POST")

	server := http.Server{
		Addr:    fmt.Sprintf(":%s", config.Cfg.TeleIncoming.Port),
		Handler: router,
	}

	doneServe := make(chan struct{}, 1)
	go func() {
		defer close(doneServe)
		if err := server.ListenAndServe(); err != nil {
			if !errors.Is(err, http.ErrServerClosed) {
				ti.Log.TraceError(err)
			}
		}
	}()

	select {
	case <-ctx.Done():
		ctxShd, ctxShdCancel := context.WithTimeout(context.Background(), 10*time.Second)
		if err := server.Shutdown(ctxShd); err != nil {
			ti.Log.TraceError(err)
		}
		ctxShdCancel()
		return

	case <-doneServe:
		return
	}
}

func (ti *TeleIncoming) ApiHandleError(w http.ResponseWriter, errMsg string) {
	w.WriteHeader(http.StatusInternalServerError)
	if _, err := w.Write([]byte(errMsg)); err != nil {
		ti.Log.TraceError(err)
	}
}

type InboundCallParams struct {
	Phone        string
	PhoneTo      string
	Diversion    string
	TrunkName    string
	Server       string
	SecretKey    string
	ExtensionNum string
	CallId       types.CallId
	SipCallId    string
	ChannelId    string
}

func (ti *TeleIncoming) ApiInboundCall(w http.ResponseWriter, r *http.Request) {
	callLog := ti.Log.CallId(types.CallId(r.URL.Query().Get("call_uuid")))
	defer func() {
		if r := recover(); r != nil {
			ti.Log.Error("unexpected error: %+v", r)
			fmt.Println(string(debug.Stack()))
			ti.ApiHandleError(w, "unexpected error")
		}
	}()

	callLog.Info("inboundCall, full query: %+v", r.URL.Query())
	secretKey := r.URL.Query().Get("secret_key")
	if config.Cfg.TeleIncoming.SecretKey != secretKey {
		callLog.Error("inboundCall, wrong secret key: %s", secretKey)
		w.WriteHeader(http.StatusInternalServerError)
		if _, err := w.Write([]byte("busy|")); err != nil {
			ti.Log.TraceError(err)
		}
		return
	}

	params := InboundCallParams{
		Phone:        incoming_utils.RecoveryPrefixNumber(r.URL.Query().Get("num_a")),
		PhoneTo:      incoming_utils.RecoveryPrefixNumber(r.URL.Query().Get("num_b")),
		Diversion:    r.URL.Query().Get("diversion"),
		TrunkName:    r.URL.Query().Get("trunk_name"),
		Server:       r.URL.Query().Get("server"),
		SecretKey:    secretKey,
		ExtensionNum: r.URL.Query().Get("extension"),
		CallId:       types.CallId(r.URL.Query().Get("call_uuid")),
		SipCallId:    r.URL.Query().Get("sipcallid"),
		ChannelId:    r.URL.Query().Get("channel_id"),
	}
	callLog.Info("inboundCall, params: %+v", params)

	// if call_id exists, then try to find sip_log by call_id and make next step (usually works with extension numbers)
	if params.CallId != "" {
		res := ti.getNonFirstInCall(params)
		w.WriteHeader(res.Status)
		if _, err := w.Write([]byte(res.Content)); err != nil {
			callLog.TraceError(err)
		}
		return
	}

	// if asterisk send request without call_id, try to find server/container by trunk rules
	v := ti.getInCallByTrunk(r, params)
	w.WriteHeader(v.Status)
	if _, err := w.Write([]byte(v.Content)); err != nil {
		callLog.TraceError(err)
	}
}

// ApiAddCallIn legacy now? Task will be sent on InboundCall endpoint
func (ti *TeleIncoming) ApiAddCallIn(w http.ResponseWriter, r *http.Request) {
	defer func() {
		if r := recover(); r != nil {
			ti.Log.Error("unexpected error: %+v", r)
			fmt.Println(string(debug.Stack()))
			ti.ApiHandleError(w, "unexpected error")
		}
	}()

	ti.Log.Info("addCallIn, full query: %+v", r.URL.Query())
	w.WriteHeader(http.StatusOK)
	if _, err := w.Write([]byte("success")); err != nil {
		ti.Log.TraceError(err)
	}
}

func (ti *TeleIncoming) getInCallByTrunk(r *http.Request, params InboundCallParams) incoming_utils.ApiResponse {
	callId := types.CallId(ti.RndGen.GenerateId().String())
	callLog := ti.Log.CallId(callId)
	defer func() {
		if r := recover(); r != nil {
			callLog.Error("getInCallByTrunk, panic: %+v", r)
			fmt.Println(string(debug.Stack()))
			ti.PostFailedRecord(nil, nil, incoming_utils.FailType_UncommonError, callId, params)
		}
	}()

	callLog.Info("getInCallByTrunk, started GetInCallByTrunk: %s", params.SipCallId)
	trunk, err := storeTrunks.GetTrunk(ti.TrunksProvider, params.TrunkName)
	if err != nil {
		callLog.TraceError(fmt.Errorf("getInCallByTrunk, failed get trunk: %w", err))
		ti.PostFailedRecord(nil, nil, incoming_utils.FailType_UncommonError, callId, params)
		return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
			Content: "busy|",
		}
	}

	if len(trunk.Rules) == 0 {
		callLog.Error("getInCallByTrunk, trunk have no rules")
		ti.PostFailedRecord(nil, nil, incoming_utils.FailType_UncommonError, callId, params)
		return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
			Content: "busy|",
		}
	}

	numA_alias := utils.TernarOp(trunk.NumA == "", "num_a", trunk.NumA)
	numB_alias := utils.TernarOp(trunk.NumB == "", "num_b", trunk.NumB)
	numA_alias = utils.TernarOp(numA_alias == "*", "num_a", incoming_utils.DropPrefixNumber(numA_alias))
	numB_alias = utils.TernarOp(numB_alias == "*", "num_b", incoming_utils.DropPrefixNumber(numB_alias))
	params.Phone = incoming_utils.RecoveryPrefixNumber(r.URL.Query().Get(numA_alias))
	params.Phone = utils.TernarOp(params.Phone == "", numA_alias, params.Phone)
	params.PhoneTo = incoming_utils.RecoveryPrefixNumber(r.URL.Query().Get(numB_alias))
	params.PhoneTo = utils.TernarOp(params.PhoneTo == "", numB_alias, params.PhoneTo)

	conditions := []string{"server_and_B", "server_and_AB_all", "no_server_and_AB_all_or_AB"}
	for _, cond := range conditions {
		for _, rule := range trunk.Rules {
			if ok := incoming_utils.CheckCondition(callLog, rule, params.Server, params.Phone, params.PhoneTo, rule.NumA, rule.NumB, cond); !ok {
				continue
			}

			campaignId := rule.ParentId
			if campaignId == "" && len(rule.ExtenGroup) == 0 {
				continue
			}

			if len(rule.Dialplan) < 1 {
				continue
			}
			step := rule.Dialplan[0]

			var campaign types.Campaign
			if step.Action == "dial" {
				if campaignId == "" {
					callLog.Error("getInCallByTrunk, empty campaignId on dial action: %s", campaignId)
					ti.PostFailedRecord(nil, nil, incoming_utils.FailType_NotFoundCampaign, callId, params)
					return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
						Content: "busy|",
					}
				}

				campaign, err = storeCampaigns.GetCampaign(ti.Ctx, ti.DatabaseProvider, campaignId)
				if err != nil {
					callLog.CampaignId(campaignId).TraceError(fmt.Errorf("getInCallByTrunk, failed to load campaign: %w", err))
					ti.PostFailedRecord(nil, nil, incoming_utils.FailType_NotFoundCampaign, callId, params)
					return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
						Content: "busy|",
					}
				}

				if !campaign.IsActive {
					callLog.CampaignId(campaignId).Warn("campaign not active")
					ti.PostFailedRecord(&campaign, nil, incoming_utils.FailType_CampaignNotActive, callId, params)
					return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
						Content: "busy|",
					}
				}

				// костыль чтобы не шла тестовая нагрузка на прод, можно будет удалить когда придумаем как удалить эту кампанию
				if campaignId == "67e10e9b740e2de5ecddc763" {
					answer := parseDialPlan(step, "", "", callId, params)
					callLog.Info("getInCallByTrunk, answer: %s", answer)
					return incoming_utils.ApiResponse{Status: http.StatusOK,
						Content: answer,
					}
				}
			}

			if len(rule.ExtenGroup) == 0 && step.Action != "dial" {
				callLog.Error("getInCallByTrunk, empty exten_group for waitexten: %s", campaignId)
				ti.PostFailedRecord(nil, nil, incoming_utils.FailType_NotFoundCampaign, callId, params)
				return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
					Content: "busy|",
				}
			}

			sipLog := types.SipLog{
				CallId:    callId,
				TrunkName: params.TrunkName,
				// На этапе waitexten мы ещё не знаем кампанию, добавляем это поле в блоке (if step.Action == "dial")
				//CompanyId:         campaignId,
				NumA:              params.Phone,
				NumB:              params.PhoneTo,
				Diversion:         params.Diversion,
				Server:            params.Server,
				ContainerServer:   "",
				Container:         "",
				CurrentDialplanId: rule.Id,
				SipCallId:         params.SipCallId,
				Dialplans:         make([]types.SipLogDialplan, 0),
				StatusLogs:        make([]types.SipLogStatusLog, 0),
				Logs: []types.SipLogLog{
					{
						DialplanId: rule.Id,
						Step:       step.Step,
						Action:     step.Action,
						Datetime:   time.Now().Format("02.01.2006 15:04:05"),
					},
				},
			}

			var server string
			var container string
			var task types.ExtendedCallTask
			if step.Action == "dial" {
				task, err = ti.CreateIncomingTask(campaign, callId, params)
				if err != nil {
					callLog.CampaignId(campaignId).TraceError(err)
					ti.PostFailedRecord(&campaign, &task, incoming_utils.FailType_UncommonError, callId, params)
					return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
						Content: "busy|",
					}
				}

				// if asterisk had strict path to server/container, it's no sense to send task to dialer
				if strings.Contains(step.Answer, "{to_server}|{to_container}") {
					server, container, err = ti.ActiveTasks.SendTask(
						campaign,
						callId,
						fmt.Sprintf("%s.%d", config.Cfg.RMQ.DialerResults, ti.QueueGen.Next()),
						task.Id,
						task.CreatedAt,
						params,
					)
					if err != nil {
						callLog.CampaignId(campaignId).TraceError(err)
						ti.PostFailedRecord(&campaign, &task, incoming_utils.FailType_DialerNoResponse, callId, params)
						ti.StopFailedTask(task, callId)
						return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
							Content: "busy|",
						}
					}
					sipLog.ContainerServer = fmt.Sprintf("%s-%s", server, container)
					sipLog.Container = fmt.Sprintf("%s-%ssl", server, container)
				}
				sipLog.CompanyId = campaignId
			}

			if err := ti.TrunksProvider.CreateSipLog(ti.Ctx, sipLog); err != nil {
				callLog.CampaignId(campaignId).TraceError(err)
				ti.PostFailedRecord(&campaign, &task, incoming_utils.FailType_UncommonError, callId, params)
				ti.StopFailedTask(task, callId)
				return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
					Content: "busy|",
				}
			}

			answer := parseDialPlan(step, server, container, callId, params)
			callLog.Info("getInCallByTrunk, answer: %s", answer)
			return incoming_utils.ApiResponse{Status: http.StatusOK,
				Content: answer,
			}
		}
	}

	callLog.Info("inboundCall, failed to find decent rule in trunk")
	ti.PostFailedRecord(nil, nil, incoming_utils.FailType_NotFoundRules, callId, params)
	return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
		Content: "busy|",
	}
}

func (ti *TeleIncoming) getNonFirstInCall(params InboundCallParams) incoming_utils.ApiResponse {
	callId := params.CallId
	callLog := ti.Log.CallId(callId)
	defer func() {
		if r := recover(); r != nil {
			callLog.Error("getNonFirstInCall, panic: %+v", r)
			fmt.Println(string(debug.Stack()))
			ti.PostFailedRecord(nil, nil, incoming_utils.FailType_UncommonError, callId, params)
		}
	}()

	sipLog, err := ti.TrunksProvider.GetSipLogByCallId(ti.Ctx, callId)
	if err != nil {
		params.Phone = ""
		params.PhoneTo = ""
		callLog.TraceError(fmt.Errorf("getNonFirstInCall, not found sip_log, err: %w", err))
		ti.PostFailedRecord(nil, nil, incoming_utils.FailType_EmptySipLog, callId, params)
		return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
			Content: fmt.Sprintf("busy|%s", callId),
		}
	}

	params.Phone = sipLog.NumA
	params.PhoneTo = sipLog.NumB
	currentDialplanId := sipLog.CurrentDialplanId
	if len(sipLog.Logs) < 1 {
		callLog.Error("getNonFirstInCall, empty sip_log")
		ti.PostFailedRecord(nil, nil, incoming_utils.FailType_EmptySipLog, callId, params)
		return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
			Content: fmt.Sprintf("busy|%s", callId),
		}
	}
	currentStep := sipLog.Logs[len(sipLog.Logs)-1].Step

	currentRule, err := storeTrunks.GetTrunkRule(ti.TrunksProvider, params.TrunkName, currentDialplanId)
	if err != nil {
		callLog.TraceError(fmt.Errorf("getNonFirstInCall, no rules: %s %s, err: %w", currentDialplanId, params.TrunkName, err))
		ti.PostFailedRecord(nil, nil, incoming_utils.FailType_NotFoundRules, callId, params)
		return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
			Content: fmt.Sprintf("busy|%s", callId),
		}
	}

	if len(currentRule.Dialplan)-1 < currentStep {
		callLog.Error("getNonFirstInCall, no rules: %d %s %s", currentStep, currentDialplanId, params.TrunkName)
		ti.PostFailedRecord(nil, nil, incoming_utils.FailType_NotFoundRules, callId, params)
		return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
			Content: fmt.Sprintf("busy|%s", callId),
		}
	}
	step := currentRule.Dialplan[currentStep]

	if params.ExtensionNum == "" {
		callLog.Info("getNonFirstInCall, ext num is empty")
		ti.PostFailedRecord(nil, nil, incoming_utils.FailType_NotFoundExtensionNum, callId, params)
		return incoming_utils.ApiResponse{Status: http.StatusOK,
			Content: fmt.Sprintf("hangup|%s", callId),
		}
	}

	campaignId, ok := currentRule.ExtenGroup[params.ExtensionNum]
	if !ok {
		callLog.Info("getNonFirstInCall, no campaign by ext_num: %s", params.ExtensionNum)
		ti.PostFailedRecord(nil, nil, incoming_utils.FailType_NotFoundExtensionNum, callId, params)
		return incoming_utils.ApiResponse{Status: http.StatusOK,
			Content: fmt.Sprintf("hangup|%s", callId),
		}
	}
	campaign, err := storeCampaigns.GetCampaign(ti.Ctx, ti.DatabaseProvider, campaignId)
	if err != nil || !campaign.IsActive {
		callLog.CampaignId(campaignId).TraceError(fmt.Errorf("getNonFirstInCall, failed get campaign: %w", err))
		ti.PostFailedRecord(nil, nil, incoming_utils.FailType_NotFoundCampaign, callId, params)
		return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
			Content: fmt.Sprintf("busy|%s", callId),
		}
	}

	var server string
	var container string
	var task types.ExtendedCallTask
	if step.Action == "dial" {
		task, err = ti.CreateIncomingTask(campaign, callId, params)
		if err != nil {
			callLog.TraceError(err)
			ti.PostFailedRecord(&campaign, &task, incoming_utils.FailType_UncommonError, callId, params)
			return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
				Content: fmt.Sprintf("busy|%s", callId),
			}
		}

		server, container, err = ti.ActiveTasks.SendTask(
			campaign,
			callId,
			fmt.Sprintf("%s.%d", config.Cfg.RMQ.DialerResults, ti.QueueGen.Next()),
			task.Id,
			task.CreatedAt,
			params,
		)
		if err != nil {
			callLog.TraceError(err)
			ti.PostFailedRecord(&campaign, &task, incoming_utils.FailType_DialerNoResponse, callId, params)
			ti.StopFailedTask(task, callId)
			return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
				Content: fmt.Sprintf("busy|%s", callId),
			}
		}
	}

	answer := step.Answer
	toServer := utils.TernarOp(server == "", "null", server)
	toContainer := utils.TernarOp(container == "", "null", container)
	values := map[string]string{
		"action":       step.Action,
		"to_server":    toServer,
		"to_container": toContainer,
		"uuid_call":    string(callId),
		"to_trunk":     "docker",
		"num_a":        params.Phone,
		"num_b":        params.PhoneTo,
	}
	for k, v := range values {
		answer = strings.ReplaceAll(answer, fmt.Sprintf("{%s}", k), v)
	}
	for k, v := range step.Params {
		answer = strings.ReplaceAll(answer, fmt.Sprintf("{%s}", k), fmt.Sprintf("%v", v))
	}

	if err := ti.TrunksProvider.UpdateSipLog(ti.Ctx, sipLog.Id, types.M{
		"$set": types.M{
			"company_id":       campaignId,
			"container_server": utils.TernarOp(toServer == "null", "", fmt.Sprintf("%s-%s", server, container)),
			"container":        utils.TernarOp(toContainer == "null", "", fmt.Sprintf("%s-%ssl", server, container)),
		},
		"$push": types.M{
			"logs": types.M{
				"dialplan_id": answer,
				"step":        currentStep + 1,
				"exten":       params.ExtensionNum,
				"action":      step.Action,
				"sipcallid":   params.SipCallId,
				"datetime":    time.Now().Format("02.01.2006 15:04:05"),
			},
		},
	}); err != nil {
		callLog.TraceError(err)
		ti.PostFailedRecord(&campaign, &task, incoming_utils.FailType_UncommonError, callId, params)
		ti.StopFailedTask(task, callId)
		return incoming_utils.ApiResponse{Status: http.StatusInternalServerError,
			Content: fmt.Sprintf("busy|%s", callId),
		}
	}

	callLog.Info("getNonFirstInCall, answer: %s", answer)
	return incoming_utils.ApiResponse{Status: http.StatusOK,
		Content: answer,
	}
}

func (ti *TeleIncoming) PostFailedRecord(
	campaign *types.Campaign,
	task *types.ExtendedCallTask,
	failType string,
	callId types.CallId,
	params InboundCallParams,
) {
	now := time.Now()
	callResult := types.CallResult{
		StartTs:            int(now.UnixMilli()),
		EndTs:              int(now.UnixMilli()),
		StartDatabase:      now,
		EndDatabase:        now,
		TaskAddedDatabase:  now,
		DuplicateMessages:  map[string]string{},
		StructuredLogs:     []interface{}{},
		CallWithProblems:   true,
		ErrorMsg:           failType,
		Direction:          types.CALL_DIRECTION_IN,
		InterruptionStatus: 0,
		Source:             "caller",
		CallData:           types.CallResultCallData{},
		CategoryStatus:     "Отсутствие диалога",
		DialerId:           params.Server,
		CallResultIn: &types.CallResultIn{
			PhoneTo: params.PhoneTo,
		},
		Attempt: types.Attempt{
			CallId:     callId,
			Phone:      params.Phone,
			Status:     types.CALL_STATUS_ERROR_BEFORE_UP,
			Tz:         3,
			Trunk:      params.TrunkName,
			ResultCall: "Ошибка обработки звонка",
		},
	}
	if task != nil {
		callResult.CallTaskId = task.Id
	}
	if campaign != nil {
		callResult.CampaignId = campaign.Id
		callResult.BuildInfo = campaign.BuildInfo
	}
	callResult.BuildInfo.Project = "failed_calls"

	if err := ti.DatabaseProvider.CreateRecord(ti.Ctx, callResult); err != nil {
		ti.Log.CallId(callId).Error("postFailedRecord, error - %s", err)
	}
}

func (ti *TeleIncoming) CreateIncomingTask(campaign types.Campaign, callId types.CallId, params InboundCallParams) (types.ExtendedCallTask, error) {
	now := time.Now()
	return ti.DatabaseProvider.CreateIncomingTask(ti.Ctx, types.ExtendedCallTask{
		Project:               campaign.BuildInfo.Project,
		Status:                types.TASK_STATUS_PROGRESS,
		LastRequestToDialerAt: now,
		AttemptNumber:         1,
		UpdatedAt:             now,
		CallTask: types.CallTask{
			CampaignId: campaign.Id,
			LastCallId: callId,
			Phones: []types.TaskPhone{{
				Phone:           params.Phone,
				NormalizedPhone: params.Phone,
			}},
			CreatedAt:  now,
			NextCallAt: now,
		},
	})
}

func (ti *TeleIncoming) StopFailedTask(task types.ExtendedCallTask, callId types.CallId) {
	_, err := ti.DatabaseProvider.UpdateTask(
		ti.Ctx,
		task.Id,
		types.M{"$set": types.M{"status": types.TASK_STATUS_STOP}},
	)
	if err != nil {
		ti.Log.CallId(callId).Error("postFailedRecord, error - %s", err)
	}
}

func parseDialPlan(step types.TrunkRuleDialplan, server, container string, callId types.CallId, params InboundCallParams) string {
	answer := step.Answer
	toServer := utils.TernarOp(server == "", "null", server)
	toContainer := utils.TernarOp(container == "", "null", container)
	values := map[string]string{
		"action":       step.Action,
		"to_server":    toServer,
		"to_container": toContainer,
		"uuid_call":    string(callId),
		"to_trunk":     "docker",
		"num_a":        params.Phone,
		"num_b":        params.PhoneTo,
	}
	for k, v := range values {
		answer = strings.ReplaceAll(answer, fmt.Sprintf("{%s}", k), v)
	}
	for k, v := range step.Params {
		answer = strings.ReplaceAll(answer, fmt.Sprintf("{%s}", k), fmt.Sprintf("%v", v))
	}
	return answer
}
