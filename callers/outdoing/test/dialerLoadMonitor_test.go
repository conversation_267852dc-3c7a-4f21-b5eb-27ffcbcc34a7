package outdoing

import (
	"context"
	"testing"
	"time"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/outdoing"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
	"go.mongodb.org/mongo-driver/mongo"
)

type CreateCampaignLoadPointReturn struct {
	*mongo.InsertOneResult
	Err error
}

type SetCampaignLoadPointsExpireIndexArgs struct {
	ctx           context.Context
	expireField   string
	expireSeconds int32
}

type CreateCampaignLoadPointArgs struct {
	ctx   context.Context
	point types.CampaignLoadPoint
}

type MockMonitorDataStorage struct {
	SetCampaignLoadPointsExpireIndexReturn error
	SetCampaignLoadPointsExpireIndexArgs   []SetCampaignLoadPointsExpireIndexArgs
	CreateCampaignLoadPointReturn          CreateCampaignLoadPointReturn
	CreateCampaignLoadPointArgs            []CreateCampaignLoadPointArgs
}

func (ds *MockMonitorDataStorage) SetCampaignLoadPointsExpireIndex(
	ctx context.Context,
	expireField string,
	expireSeconds int32,
) error {
	ds.SetCampaignLoadPointsExpireIndexArgs = append(
		ds.SetCampaignLoadPointsExpireIndexArgs,
		SetCampaignLoadPointsExpireIndexArgs{
			ctx:           ctx,
			expireField:   expireField,
			expireSeconds: expireSeconds,
		},
	)
	return ds.SetCampaignLoadPointsExpireIndexReturn
}

type MockMonitorTime struct {
	NowReturn func() (time.Time, bool)
}

func (m *MockMonitorTime) Now() time.Time {
	now, _ := m.NowReturn()
	return now
}

func (ds *MockMonitorDataStorage) CreateCampaignLoadPoint(
	ctx context.Context,
	point types.CampaignLoadPoint,
) error {
	ds.CreateCampaignLoadPointArgs = append(
		ds.CreateCampaignLoadPointArgs,
		CreateCampaignLoadPointArgs{
			ctx:   ctx,
			point: point,
		},
	)
	return ds.CreateCampaignLoadPointReturn.Err
}

func TestDialerLoadMonitor(t *testing.T) {
	ctx, ctxCancel := context.WithCancel(context.Background())
	ch := make(chan interface{})
	ds := &MockMonitorDataStorage{
		SetCampaignLoadPointsExpireIndexReturn: nil,
		SetCampaignLoadPointsExpireIndexArgs:   []SetCampaignLoadPointsExpireIndexArgs{},
		CreateCampaignLoadPointReturn: CreateCampaignLoadPointReturn{
			InsertOneResult: nil,
			Err:             nil,
		},
		CreateCampaignLoadPointArgs: []CreateCampaignLoadPointArgs{},
	}
	start := time.Now().Add(-time.Hour)
	times := []time.Time{
		start,
		start.Add(time.Minute),
		start.Add(time.Minute * 2),
		start.Add(time.Minute * 2),

		start.Add(time.Minute * 4),
		start.Add(time.Minute * 5),
		start.Add(time.Minute * 5),
		start.Add(time.Minute * 5),
	}
	mt := &MockMonitorTime{NowReturn: utils.Generator(times)}
	var campaignId types.CampaignId = "123"
	rndGen := utils.NewRandomGenerator(ctx)
	callIds := [2]types.CallId{
		types.CallId(rndGen.GenerateId().String()),
		types.CallId(rndGen.GenerateId().String()),
	}
	go func() {
		ch <- outdoing.DialerLoadMonitorCampaignLines{Id: campaignId, Lines: 2}
		ch <- outdoing.DialerLoadMonitorTaskStarted{CampaignId: campaignId, CallId: callIds[0]}
		ch <- outdoing.DialerLoadMonitorTaskStopped{CampaignId: campaignId, CallId: callIds[0]}

		ch <- outdoing.DialerLoadMonitorCampaignLines{Id: campaignId, Lines: 1}
		ch <- outdoing.DialerLoadMonitorTaskStarted{CampaignId: campaignId, CallId: callIds[1]}
		ch <- outdoing.DialerLoadMonitorTaskStopped{CampaignId: campaignId, CallId: callIds[1]}

		ctxCancel()
	}()
	outdoing.StartDialerLoadMonitor(ctx, ch, ds, mt)

	if l := len(ds.SetCampaignLoadPointsExpireIndexArgs); l != 1 {
		t.Errorf("SetCampaignLoadPointsExpireIndex called %d times", l)
	}
	if l := len(ds.CreateCampaignLoadPointArgs); l != 5 {
		t.Errorf("CreateCampaignLoadPoint called %d times", l)
		return
	}

	point := types.CampaignLoadPoint{
		CampaignId:      campaignId,
		Lines:           2,
		SavedAt:         times[1],
		LinesPerSlotMin: map[string]int{},
		LinesPerSlotMax: map[string]int{},
		LinesPerSlotCur: map[string]int{},
		CallStatusesMin: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
		CallStatusesMax: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
		CallStatusesCur: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
	}
	if !point.Equal(ds.CreateCampaignLoadPointArgs[0].point) {
		t.Errorf("%+v != %+v", point, ds.CreateCampaignLoadPointArgs[0].point)
	}
	point = types.CampaignLoadPoint{
		CampaignId:      campaignId,
		Lines:           2,
		BusyLinesCur:    1,
		BusyLinesMax:    1,
		SavedAt:         times[2],
		LinesPerSlotMin: map[string]int{},
		LinesPerSlotMax: map[string]int{},
		LinesPerSlotCur: map[string]int{},
		CallStatusesMin: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
		CallStatusesMax: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
		CallStatusesCur: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
	}
	if !point.Equal(ds.CreateCampaignLoadPointArgs[1].point) {
		t.Errorf("%+v != %+v", point, ds.CreateCampaignLoadPointArgs[1].point)
	}
	point = types.CampaignLoadPoint{
		CampaignId:      campaignId,
		Lines:           2,
		BusyLinesMax:    1,
		SavedAt:         times[3],
		LinesPerSlotMin: map[string]int{},
		LinesPerSlotMax: map[string]int{},
		LinesPerSlotCur: map[string]int{},
		CallStatusesMin: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
		CallStatusesMax: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
		CallStatusesCur: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
	}
	if !point.Equal(ds.CreateCampaignLoadPointArgs[2].point) {
		t.Errorf("%+v != %+v", point, ds.CreateCampaignLoadPointArgs[2].point)
	}
	point = types.CampaignLoadPoint{
		CampaignId:      campaignId,
		Lines:           1,
		BusyLinesCur:    0,
		BusyLinesMax:    0,
		SavedAt:         times[5],
		LinesPerSlotMin: map[string]int{},
		LinesPerSlotMax: map[string]int{},
		LinesPerSlotCur: map[string]int{},
		CallStatusesMin: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
		CallStatusesMax: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
		CallStatusesCur: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
	}
	if !point.Equal(ds.CreateCampaignLoadPointArgs[3].point) {
		t.Errorf("%+v != %+v", point, ds.CreateCampaignLoadPointArgs[3].point)
	}
	point = types.CampaignLoadPoint{
		CampaignId:      campaignId,
		Lines:           1,
		BusyLinesCur:    0,
		BusyLinesMax:    1,
		SavedAt:         times[7],
		LinesPerSlotMin: map[string]int{},
		LinesPerSlotMax: map[string]int{},
		LinesPerSlotCur: map[string]int{},
		CallStatusesMin: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
		CallStatusesMax: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
		CallStatusesCur: types.CallerCallStatusMetricsList{
			types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
			types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
			types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
			types.CALLER_CALL_STATUS_PREPARING:             0,
		},
	}
	if !point.Equal(ds.CreateCampaignLoadPointArgs[4].point) {
		t.Errorf("%+v != %+v", point, ds.CreateCampaignLoadPointArgs[4].point)
	}
}
