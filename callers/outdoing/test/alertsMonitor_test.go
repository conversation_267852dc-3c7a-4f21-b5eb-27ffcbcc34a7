package outdoing

import (
	"context"
	"fmt"
	"testing"
	"time"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/outdoing"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
)

type AlertSender interface {
	SendAlert(msg string) error
}

type MockAlertSender struct {
	Count   int
	LastMsg string
}

func (m *MockAlertSender) SendAlert(msg interface{}) error {
	str, ok := msg.(string)
	if !ok {
		return fmt.Errorf("invalid message %s", msg)
	}
	m.Count++
	m.LastMsg = str
	return nil
}

func TestAlertsMonitor(t *testing.T) {
	runAlerts := func(ctx context.Context) (chan interface{}, chan bool, *MockAlertSender) {
		ch := make(chan interface{})
		chServiceStopped := make(chan bool)
		alertSender := &MockAlertSender{}
		go func() {
			outdoing.StartAlertsMonitor(ctx, ch, []outdoing.AlertSender{alertSender})
			chServiceStopped <- true
		}()
		return ch, chServiceStopped, alertSender
	}

	t.Run("alert with series status", func(t *testing.T) {
		t.Parallel()
		ctx, ctxCancel := context.WithCancel(context.Background())
		ch, chServiceStopped, alertSender := runAlerts(ctx)

		campaignOut := types.Campaign{
			Id:            "123",
			CallDirection: types.CAMPAIGN_CALL_DIRECTION_OUT,
			AlertSettings: types.AlertSettings{Down: 5},
			Name:          "name",
			BuildInfo: types.BuildInfo{
				Project: "project",
			},
		}
		alertCh := make(chan struct{}, 1)
		ch <- outdoing.AlertsMonitorCampaignStarted{Campaign: campaignOut, AlertCh: alertCh}

		// make series of "down"
		for i := 0; i < 4; i++ {
			ch <- outdoing.AlertsMonitorCallStarted{Campaign: campaignOut}
			ch <- outdoing.AlertsMonitorCallResult{Campaign: campaignOut, Status: types.CALL_STATUS_DOWN}
		}
		time.Sleep(time.Second)
		select {
		case <-alertCh:
			t.Errorf("should not be stopped, series of down not completed")
		default:
		}

		// make series of "success"
		for i := 0; i < 4; i++ {
			ch <- outdoing.AlertsMonitorCallStarted{Campaign: campaignOut}
			ch <- outdoing.AlertsMonitorCallResult{Campaign: campaignOut, Status: types.CALL_STATUS_SUCCESS}
		}
		time.Sleep(time.Second)
		select {
		case <-alertCh:
			t.Errorf("should not be stopped, series of down not completed")
		default:
		}

		// make series of "down"
		for i := 0; i < 4; i++ {
			ch <- outdoing.AlertsMonitorCallStarted{Campaign: campaignOut}
			ch <- outdoing.AlertsMonitorCallResult{Campaign: campaignOut, Status: types.CALL_STATUS_DOWN}
		}
		time.Sleep(time.Second)
		select {
		case <-alertCh:
			t.Errorf("should not be stopped, series of down not completed")
		default:
		}

		// done series of "down"
		ch <- outdoing.AlertsMonitorCallStarted{Campaign: campaignOut}
		ch <- outdoing.AlertsMonitorCallResult{Campaign: campaignOut, Status: types.CALL_STATUS_DOWN}
		time.Sleep(time.Second)
		select {
		case <-alertCh:
		default:
			t.Errorf("should be stopped, series of down completed")
		}

		ctxCancel()
		<-chServiceStopped

		if alertSender.Count != 1 {
			t.Errorf("expected alert count - %d, got - %d", 1, alertSender.Count)
		}
		expectedMsg := "в прозвоне по исходящей кампании \"name\" (id=0, project=project) серия down из 5 подряд, я застопился, идите проверьте"
		if alertSender.LastMsg != expectedMsg {
			t.Errorf("expected alert msg - %s, got - %s", expectedMsg, alertSender.LastMsg)
		}
	})

	t.Run("alert with lines exceeded limit", func(t *testing.T) {
		t.Parallel()
		ctx, ctxCancel := context.WithCancel(context.Background())
		ch, chServiceStopped, alertSender := runAlerts(ctx)

		campaignIn := types.Campaign{
			Id:            "123",
			Lines:         15,
			CallDirection: types.CAMPAIGN_CALL_DIRECTION_IN,
			Name:          "name",
			BuildInfo: types.BuildInfo{
				Project: "project",
			},
		}

		// make series of calls
		for i := 0; i < 10; i++ {
			ch <- outdoing.AlertsMonitorCallStarted{Campaign: campaignIn}
			ch <- outdoing.AlertsMonitorCallResult{Campaign: campaignIn, Status: types.CALL_STATUS_DOWN}
			if alertSender.Count != 0 {
				t.Errorf("expected alert count - %d, got - %d", 0, alertSender.Count)
			}
		}

		// increase lines equal to campaign capacity
		for i := 0; i < campaignIn.Lines; i++ {
			ch <- outdoing.AlertsMonitorCallStarted{Campaign: campaignIn}
			if alertSender.Count != 0 {
				t.Errorf("expected alert count - %d, got - %d", 0, alertSender.Count)
			}
		}

		// exceed lines limit
		ch <- outdoing.AlertsMonitorCallStarted{Campaign: campaignIn}

		ctxCancel()
		<-chServiceStopped

		if alertSender.Count != 1 {
			t.Errorf("expected alert count - %d, got - %d", 1, alertSender.Count)
		}
		expectedMsg := "в прозвоне по входящей кампании \"name\" (id=0, project=project) превышено кол-во линий - 16 (лимит - 15)"
		if alertSender.LastMsg != expectedMsg {
			t.Errorf("expected alert msg - %s, got - %s", expectedMsg, alertSender.LastMsg)
		}
	})
}
