package outdoing

import (
	"context"
	"testing"
	"time"

	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/outdoing"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

func TestRecieveCallEvents_StopFromDB(t *testing.T) {
	t.Parallel()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	campaign := campaignTemplate
	task := taskTemplate

	dialerMonitorCh := make(chan interface{})
	alertsMonitorCh := make(chan interface{})

	ts := MockTrunksState{}
	ds := MockDataStorage{
		campaigns: map[types.CampaignId]*types.Campaign{
			campaign.Id: &campaign,
		},
		tasks: map[types.CallTaskId]*types.ExtendedCallTask{
			task.Id: &task,
		},
		callResults: map[types.CallResultId]*types.CallResult{},
	}
	rmq := MockRmq{}
	publisher := MockPublisher{Rmq: &rmq}
	loadMonitor := MockLoadMonitor{Ch: dialerMonitorCh}
	alertsMonitor := MockAlertMonitor{Ch: alertsMonitorCh}

	loadMonitor.Listen(ctx)
	alertsMonitor.Listen(ctx)

	callResultsCh := make(chan interface{})
	go outdoing.StartRecieveCallEvents(ctx, callResultsCh, dialerMonitorCh, alertsMonitorCh, &ds, &ts, &publisher)

	rndGen := utils.NewRandomGenerator(ctx)
	if attempts, err := ds.GetAttemptsByTaskId(ctx, []types.CallTaskId{task.Id}); err != nil {
		t.Error(err)
	} else {
		task.CallResults = make([]types.Attempt, 0, len(attempts))
		for attempt := range attempts {
			task.CallResults = append(task.CallResults, attempt)
		}
	}
	taskState := outdoing.NewCallTaskState(campaign, task.CallTask)
	callId := types.CallId(rndGen.GenerateId().String())
	taskState = taskState.SetCallId(callId)
	taskState.CallFinishCh = make(chan struct{})
	callResultsCh <- taskState

	ds.UpdateTask(ctx, task.Id, types.M{"$set": types.M{"status": types.TASK_STATUS_STOP}})

	callResult := outdoing.CallResultData{
		DialerResult: types.DialerResult{
			Id:         callId,
			CallTaskId: task.Id,
			CampaignId: campaign.Id,
			BotResult:  "down",
			Status:     types.CALL_STATUS_DOWN,
			DataBaseId: "1",
		},
		ResponseCh: make(chan rabbitmq.Action),
	}
	ds.CreateRecord(ctx, taskState.CreateCallResult(
		outdoing.CallState{
			Attempt: &types.Attempt{
				CallId:     callId,
				Status:     callResult.DialerResult.Status,
				ResultCall: callResult.DialerResult.BotResult,
			},
		},
		&callResult.DialerResult,
	))
	callResultsCh <- callResult
	<-callResult.ResponseCh
	<-taskState.CallFinishCh
	if taskFromDB, err := ds.GetCallTaskById(ctx, task.Id); err != nil {
		t.Error(err)
	} else if taskFromDB.Status != types.TASK_STATUS_STOP {
		t.Errorf("wrong status %s", taskFromDB.Status)
	} else if taskFromDB.AttemptNumber != 1 {
		t.Errorf("wrong attempt number %d", taskFromDB.AttemptNumber)
	}
}

func TestRecieveCallEvents_RecallTypeByOne(t *testing.T) {
	t.Parallel()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	campaign := campaignTemplate
	campaign.CallerSettings.PhonesAttemptCounterType = types.COUNTER_TYPE_BY_ONE
	task := taskTemplate

	dialerMonitorCh := make(chan interface{})
	alertsMonitorCh := make(chan interface{})

	ts := MockTrunksState{}
	ds := MockDataStorage{
		campaigns: map[types.CampaignId]*types.Campaign{
			campaign.Id: &campaign,
		},
		tasks: map[types.CallTaskId]*types.ExtendedCallTask{
			task.Id: &task,
		},
		callResults: map[types.CallResultId]*types.CallResult{},
	}
	rmq := MockRmq{}
	publisher := MockPublisher{Rmq: &rmq}
	loadMonitor := MockLoadMonitor{Ch: dialerMonitorCh}
	alertsMonitor := MockAlertMonitor{Ch: alertsMonitorCh}

	loadMonitor.Listen(ctx)
	alertsMonitor.Listen(ctx)

	callResultsCh := make(chan interface{})
	go outdoing.StartRecieveCallEvents(ctx, callResultsCh, dialerMonitorCh, alertsMonitorCh, &ds, &ts, &publisher)

	rndGen := utils.NewRandomGenerator(ctx)
	for i := 0; i < 4; i++ {
		if attempts, err := ds.GetAttemptsByTaskId(ctx, []types.CallTaskId{task.Id}); err != nil {
			t.Error(err)
		} else {
			task.CallResults = make([]types.Attempt, 0, len(attempts))
			for attempt := range attempts {
				task.CallResults = append(task.CallResults, attempt)
			}
		}
		taskState := outdoing.NewCallTaskState(campaign, task.CallTask)
		callId := types.CallId(rndGen.GenerateId().String())
		taskState = taskState.SetCallId(callId)
		taskState.CallFinishCh = make(chan struct{})
		callResultsCh <- taskState
		callResult := outdoing.CallResultData{
			DialerResult: types.DialerResult{
				Id:         callId,
				CallTaskId: task.Id,
				CampaignId: campaign.Id,
				BotResult:  "down",
				Status:     types.CALL_STATUS_DOWN,
				DataBaseId: "1",
			},
			ResponseCh: make(chan rabbitmq.Action),
		}
		ds.CreateRecord(ctx, taskState.CreateCallResult(
			outdoing.CallState{
				Attempt: &types.Attempt{
					CallId:     callId,
					Status:     callResult.DialerResult.Status,
					ResultCall: callResult.DialerResult.BotResult,
				},
			},
			&callResult.DialerResult,
		))
		callResultsCh <- callResult
		<-callResult.ResponseCh
		<-taskState.CallFinishCh
		if taskFromDB, err := ds.GetCallTaskById(ctx, task.Id); err != nil {
			t.Error(err)
		} else if i < 3 && taskFromDB.Status != types.TASK_STATUS_DELAY {
			t.Errorf("wrong status %s", taskFromDB.Status)
		} else if i == 3 && taskFromDB.Status != types.TASK_STATUS_STOP {
			t.Errorf("wrong status %s", taskFromDB.Status)
		} else if i > 3 {
			t.Error("exceeded attempts")
		} else if i+1 != taskFromDB.AttemptNumber {
			t.Errorf("wrong attempt number %d", taskFromDB.AttemptNumber)
		}
	}
}

func TestRecieveCallEvents_NextCallAt(t *testing.T) {
	t.Parallel()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	campaign := campaignTemplate
	campaign.RecallSettings.DefaultAttempts = 4
	campaign.RecallSettings.AttemptsByStatus = types.AttemptByStatus{}
	task := taskTemplate
	task.Phones = task.Phones[:1]

	dialerMonitorCh := make(chan interface{})
	alertsMonitorCh := make(chan interface{})

	ts := MockTrunksState{}
	ds := MockDataStorage{
		campaigns: map[types.CampaignId]*types.Campaign{
			campaign.Id: &campaign,
		},
		tasks: map[types.CallTaskId]*types.ExtendedCallTask{
			task.Id: &task,
		},
		callResults: map[types.CallResultId]*types.CallResult{},
	}
	rmq := MockRmq{}
	publisher := MockPublisher{Rmq: &rmq}
	loadMonitor := MockLoadMonitor{Ch: dialerMonitorCh}
	alertsMonitor := MockAlertMonitor{Ch: alertsMonitorCh}

	loadMonitor.Listen(ctx)
	alertsMonitor.Listen(ctx)

	callResultsCh := make(chan interface{})
	go outdoing.StartRecieveCallEvents(ctx, callResultsCh, dialerMonitorCh, alertsMonitorCh, &ds, &ts, &publisher)

	rndGen := utils.NewRandomGenerator(ctx)
	smartRecallDuration := 300
	callsResult := []outdoing.CallResultData{
		{
			DialerResult: types.DialerResult{
				Id:         types.CallId(rndGen.GenerateId().String()),
				CallTaskId: task.Id,
				CampaignId: campaign.Id,
				BotResult:  "down",
				Status:     types.CALL_STATUS_DOWN,
				DataBaseId: "1",
				EndDate:    float64(time.Now().Unix()),
			},
			ResponseCh: make(chan rabbitmq.Action),
		},
		{
			DialerResult: types.DialerResult{
				Id:             types.CallId(rndGen.GenerateId().String()),
				CallTaskId:     task.Id,
				CampaignId:     campaign.Id,
				BotResult:      "down",
				Status:         types.CALL_STATUS_DOWN,
				DataBaseId:     "2",
				RecallDatetime: smartRecallDuration,
				EndDate:        float64(time.Now().Unix()),
			},
			ResponseCh: make(chan rabbitmq.Action),
		},
		{
			DialerResult: types.DialerResult{
				Id:             types.CallId(rndGen.GenerateId().String()),
				CallTaskId:     task.Id,
				CampaignId:     campaign.Id,
				BotResult:      "request_recall",
				Status:         types.CALL_STATUS_REQUEST_RECALL,
				DataBaseId:     "3",
				RecallDatetime: smartRecallDuration,
				EndDate:        float64(time.Now().Unix()),
			},
			ResponseCh: make(chan rabbitmq.Action),
		},
	}

	edge := time.Duration(time.Second)
	for i, callResult := range callsResult {
		if attempts, err := ds.GetAttemptsByTaskId(ctx, []types.CallTaskId{task.Id}); err != nil {
			t.Error(err)
		} else {
			task.CallResults = make([]types.Attempt, 0, len(attempts))
			for attempt := range attempts {
				task.CallResults = append(task.CallResults, attempt)
			}
		}
		taskState := outdoing.NewCallTaskState(campaign, task.CallTask)
		callId := callResult.DialerResult.Id
		taskState = taskState.SetCallId(callId)
		taskState.CallFinishCh = make(chan struct{})
		callResultsCh <- taskState
		ds.CreateRecord(ctx, taskState.CreateCallResult(
			outdoing.CallState{
				Attempt: &types.Attempt{
					CallId:     callId,
					Status:     callResult.DialerResult.Status,
					ResultCall: callResult.DialerResult.BotResult,
				},
			},
			&callResult.DialerResult,
		))
		callResultsCh <- callResult
		<-callResult.ResponseCh
		<-taskState.CallFinishCh
		if taskFromDB, err := ds.GetCallTaskById(ctx, task.Id); err != nil {
			t.Error(err)
		} else {
			until := time.Until(taskFromDB.NextCallAt)
			var diff time.Duration
			switch i {
			case 0, 1:
				diff = (until - time.Duration(time.Second*30)).Abs()
			case 2:
				diff = (until - time.Duration(time.Second*time.Duration(smartRecallDuration))).Abs()
			}
			if diff > edge {
				t.Errorf("wrong delay for recall %d %s", i+1, until)
			}
		}
	}
}
