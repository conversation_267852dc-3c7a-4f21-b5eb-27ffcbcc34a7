package outdoing

import (
	"context"
	"encoding/json"
	"sync"
	"testing"
	"time"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/outdoing"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

func TestSyncDB_Simple(t *testing.T) {
	t.<PERSON>()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	campaign := campaignTemplate
	task := taskTemplate

	dialerMonitorCh := make(chan interface{})
	alertsMonitorCh := make(chan interface{})
	recieveCallsCh := make(chan interface{})

	trunksState := MockTrunksState{}
	rmq := MockRmq{}
	publisher := MockPublisher{Rmq: &rmq}
	loadMonitor := MockLoadMonitor{Ch: dialerMonitorCh}
	alertMonitor := MockAlertMonitor{Ch: alertsMonitorCh}
	ds := MockDataStorage{
		campaigns: map[types.CampaignId]*types.Campaign{
			campaign.Id: &campaign,
		},
		tasks: map[types.CallTaskId]*types.ExtendedCallTask{
			task.Id: &task,
		},
		callResults: map[types.CallResultId]*types.CallResult{},
	}
	syncDB := outdoing.SyncDB{
		TrunkManager:              &trunksState,
		Publisher:                 &publisher,
		DataStorage:               &ds,
		CampaignStates:            map[types.CampaignId]outdoing.CampaignState{},
		RecieveCallCh:             recieveCallsCh,
		DialerMonitorChIn:         dialerMonitorCh,
		AlertsMonitorChIn:         alertsMonitorCh,
		RndGen:                    utils.NewRandomGenerator(ctx),
		ReplyToGen:                utils.NewSyncRoundRobin(ctx, 40),
		DoubleLoopMonitoring:      make(map[types.CampaignId]int, 200),
		DLMmutex:                  sync.Mutex{},
		DoubleSendTasksMonitoring: make(map[types.CampaignId]int, 200),
		DSTMmutex:                 sync.Mutex{},
	}
	recieveCalls := MockRecieveCalls{
		Ch:      recieveCallsCh,
		OnEvent: func(interface{}) {},
	}

	loadMonitorDone := loadMonitor.Listen(ctx)
	doneAlertMonitor := alertMonitor.Listen(ctx)
	syncDB.SyncCampaigns(ctx)
	doneRecieveCalls := recieveCalls.Listen(ctx)
	<-loadMonitorDone
	<-doneAlertMonitor
	<-doneRecieveCalls

	if len(loadMonitor.History) < 1 {
		t.Errorf("load monitor events recieved %d times", len(loadMonitor.History))
	} else if e, ok := loadMonitor.History[0].(outdoing.DialerLoadMonitorCampaignLines); !ok {
		t.Errorf("unexpected event %v", e)
	} else if e.Id != campaign.Id {
		t.Errorf("%s != %s", e.Id, campaign.Id)
	} else if e.Lines != campaign.Lines {
		t.Errorf("%d != %d", e.Lines, campaign.Lines)
	}
	var publishCall types.DialerTask
	if len(rmq.PublishCalls) != 1 {
		t.Errorf("published calls %d", len(rmq.PublishCalls))
	} else if ungzipped, err := utils.UnzipData(rmq.PublishCalls[0].Data); err != nil {
		t.Errorf("failed to ungzip published call")
	} else if json.Unmarshal(ungzipped, &publishCall) != nil {
		t.Error("failed to unmarshal published call")
	} else if publishCall.Data.CallData.AttemptNumber != 1 {
		t.Errorf("wrong attempt number")
	}
	if len(recieveCalls.History) != 1 {
		t.Errorf("recieved calls %d", len(recieveCalls.History))
	}
}

func TestSyncDB_StartDate_EndDate(t *testing.T) {
	t.Parallel()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	now := time.Now()

	campaign := campaignTemplate
	campaign.Lines = 4

	task1 := taskTemplate
	task1.Id = "1"

	task2 := taskTemplate
	task2.Id = "2"
	task2.StartDate = now.Add(-time.Hour)
	task2.EndDate = now.Add(time.Hour)

	task3 := taskTemplate
	task3.Id = "3"
	task3.StartDate = now.Add(time.Hour)

	task4 := taskTemplate
	task4.Id = "4"
	task4.EndDate = now.Add(-time.Hour)

	t.Logf("%+v", map[string]types.CallTaskId{
		"task1": task1.Id,
		"task2": task2.Id,
		"task3": task3.Id,
		"task4": task4.Id,
	})

	dialerMonitorCh := make(chan interface{})
	alertsMonitorCh := make(chan interface{})
	recieveCallsCh := make(chan interface{})

	trunksState := MockTrunksState{}
	rmq := MockRmq{}
	publisher := MockPublisher{Rmq: &rmq}
	loadMonitor := MockLoadMonitor{Ch: dialerMonitorCh}
	alertMonitor := MockAlertMonitor{Ch: alertsMonitorCh}
	ds := MockDataStorage{
		campaigns: map[types.CampaignId]*types.Campaign{
			campaign.Id: &campaign,
		},
		tasks: map[types.CallTaskId]*types.ExtendedCallTask{
			task1.Id: &task1,
			task2.Id: &task2,
			task3.Id: &task3,
			task4.Id: &task4,
		},
		callResults: map[types.CallResultId]*types.CallResult{},
	}
	syncDB := outdoing.SyncDB{
		TrunkManager:              &trunksState,
		Publisher:                 &publisher,
		DataStorage:               &ds,
		CampaignStates:            map[types.CampaignId]outdoing.CampaignState{},
		RecieveCallCh:             recieveCallsCh,
		DialerMonitorChIn:         dialerMonitorCh,
		AlertsMonitorChIn:         alertsMonitorCh,
		RndGen:                    utils.NewRandomGenerator(ctx),
		ReplyToGen:                utils.NewSyncRoundRobin(ctx, 40),
		DoubleLoopMonitoring:      make(map[types.CampaignId]int, 200),
		DLMmutex:                  sync.Mutex{},
		DoubleSendTasksMonitoring: make(map[types.CampaignId]int, 200),
		DSTMmutex:                 sync.Mutex{},
	}
	recieveCalls := MockRecieveCalls{
		Ch:      recieveCallsCh,
		OnEvent: func(interface{}) {},
	}

	loadMonitorDone := loadMonitor.Listen(ctx)
	alertMonitorDone := alertMonitor.Listen(ctx)
	syncDB.SyncCampaigns(ctx)
	recieveCallsDone := recieveCalls.Listen(ctx)
	<-loadMonitorDone
	<-alertMonitorDone
	<-recieveCallsDone

	if task1.Status != types.TASK_STATUS_PROGRESS {
		t.Errorf("task 1 status %q", task1.Status)
	}
	if task2.Status != types.TASK_STATUS_PROGRESS {
		t.Errorf("task 2 status %q", task2.Status)
	}
	if task3.Status != types.TASK_STATUS_DELAY {
		t.Errorf("task 3 status %q", task3.Status)
	}
	if task4.Status != types.TASK_STATUS_STOP {
		t.Errorf("task 4 status %q", task4.Status)
	}
}

func TestSyncDB_StopTaskByResult(t *testing.T) {
	t.Parallel()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	campaign := campaignTemplate
	task := taskTemplate

	dialerMonitorCh := make(chan interface{})
	alertsMonitorCh := make(chan interface{})
	recieveCallsCh := make(chan interface{})

	trunksState := MockTrunksState{}
	rmq := MockRmq{}
	publisher := MockPublisher{Rmq: &rmq}
	loadMonitor := MockLoadMonitor{Ch: dialerMonitorCh}
	alertMonitor := MockAlertMonitor{Ch: alertsMonitorCh}
	ds := MockDataStorage{
		campaigns: map[types.CampaignId]*types.Campaign{
			campaign.Id: &campaign,
		},
		tasks: map[types.CallTaskId]*types.ExtendedCallTask{
			task.Id: &task,
		},
		callResults: map[types.CallResultId]*types.CallResult{},
	}
	syncDB := outdoing.SyncDB{
		TrunkManager:              &trunksState,
		Publisher:                 &publisher,
		DataStorage:               &ds,
		CampaignStates:            map[types.CampaignId]outdoing.CampaignState{},
		RecieveCallCh:             recieveCallsCh,
		DialerMonitorChIn:         dialerMonitorCh,
		AlertsMonitorChIn:         alertsMonitorCh,
		RndGen:                    utils.NewRandomGenerator(ctx),
		ReplyToGen:                utils.NewSyncRoundRobin(ctx, 40),
		DoubleLoopMonitoring:      make(map[types.CampaignId]int, 200),
		DLMmutex:                  sync.Mutex{},
		DoubleSendTasksMonitoring: make(map[types.CampaignId]int, 200),
		DSTMmutex:                 sync.Mutex{},
	}
	recieveCalls := MockRecieveCalls{
		Ch: recieveCallsCh,
		OnEvent: func(e interface{}) {
			callTaskState, ok := e.(outdoing.CallTaskState)
			if !ok {
				t.Errorf("unexpected type %v", callTaskState)
				return
			}
			ds.CreateRecord(ctx, types.CallResult{
				CampaignId: campaign.Id,
				CallTaskId: task.Id,
				Attempt:    types.Attempt{CallId: callTaskState.Data.LastCallId},
			})
			ds.UpdateTask(ctx, task.Id, types.M{
				"$set": types.M{"status": types.TASK_STATUS_STOP},
				"$inc": types.M{"attemptNumber": 1},
			})
			callTaskState.CallFinishCh <- struct{}{}
		},
	}

	loadMonitorDone := loadMonitor.Listen(ctx)
	alertMonitorDone := alertMonitor.Listen(ctx)
	syncDB.SyncCampaigns(ctx)
	recieveCallsDone := recieveCalls.Listen(ctx)
	<-loadMonitorDone
	<-alertMonitorDone
	<-recieveCallsDone

	if len(rmq.PublishCalls) != 1 {
		t.Errorf("published calls %d", len(rmq.PublishCalls))
	}
}

func TestSyncDB_TurnIsActive(t *testing.T) {
	t.Parallel()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	campaign := campaignTemplate
	task := taskTemplate

	dialerMonitorCh := make(chan interface{})
	alertsMonitorCh := make(chan interface{})
	recieveCallsCh := make(chan interface{})

	trunksState := MockTrunksState{}
	rmq := MockRmq{}
	publisher := MockPublisher{Rmq: &rmq}
	loadMonitor := MockLoadMonitor{Ch: dialerMonitorCh}
	alertMonitor := MockAlertMonitor{Ch: alertsMonitorCh}
	ds := MockDataStorage{
		campaigns: map[types.CampaignId]*types.Campaign{
			campaign.Id: &campaign,
		},
		tasks: map[types.CallTaskId]*types.ExtendedCallTask{
			task.Id: &task,
		},
		callResults: map[types.CallResultId]*types.CallResult{},
	}
	syncDB := outdoing.SyncDB{
		TrunkManager:              &trunksState,
		Publisher:                 &publisher,
		DataStorage:               &ds,
		CampaignStates:            map[types.CampaignId]outdoing.CampaignState{},
		RecieveCallCh:             recieveCallsCh,
		DialerMonitorChIn:         dialerMonitorCh,
		AlertsMonitorChIn:         alertsMonitorCh,
		RndGen:                    utils.NewRandomGenerator(ctx),
		ReplyToGen:                utils.NewSyncRoundRobin(ctx, 40),
		DoubleLoopMonitoring:      make(map[types.CampaignId]int, 200),
		DLMmutex:                  sync.Mutex{},
		DoubleSendTasksMonitoring: make(map[types.CampaignId]int, 200),
		DSTMmutex:                 sync.Mutex{},
	}
	recieveCalls := MockRecieveCalls{
		Ch: recieveCallsCh,
	}
	recieveCalls.OnEvent = func(e interface{}) {
		callTaskState, ok := e.(outdoing.CallTaskState)
		if !ok {
			t.Errorf("unexpected type %v", callTaskState)
			return
		}
		ds.CreateRecord(ctx, types.CallResult{
			CampaignId: campaign.Id,
			CallTaskId: task.Id,
			Attempt:    types.Attempt{CallId: callTaskState.Data.LastCallId},
		})
		switch len(recieveCalls.History) {
		case 1:
			ds.SetCampaignIsActive(campaign.Id, false)
			syncDB.SyncCampaigns(ctx)
			ds.UpdateTask(ctx, task.Id, types.M{
				"$set": types.M{"status": types.TASK_STATUS_DELAY},
				"$inc": types.M{"attemptNumber": 1},
			})
			callTaskState.CallFinishCh <- struct{}{}
			time.Sleep(time.Second * 2)
			select {
			case <-recieveCallsCh:
				t.Error("call in pause campaign time")
			default:
			}
			ds.SetCampaignIsActive(campaign.Id, true)
			syncDB.SyncCampaigns(ctx)
			time.Sleep(time.Second)
			syncDB.SyncCampaigns(ctx)

		case 2:
			ds.UpdateTask(ctx, task.Id, types.M{
				"$set": types.M{"status": types.TASK_STATUS_STOP},
				"$inc": types.M{"attemptNumber": 1},
			})
			callTaskState.CallFinishCh <- struct{}{}

		default:
			t.Error("recall after stop")
		}
	}

	loadMonitorDone := loadMonitor.Listen(ctx)
	alertMonitorDone := alertMonitor.Listen(ctx)
	syncDB.SyncCampaigns(ctx)
	recieveCallsDone := recieveCalls.Listen(ctx)
	<-loadMonitorDone
	<-alertMonitorDone
	<-recieveCallsDone

	if len(rmq.PublishCalls) != 2 {
		t.Errorf("published calls %d", len(rmq.PublishCalls))
	}
}

func TestSyncDB_StopCampaignByAlert(t *testing.T) {
	t.Parallel()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()

	campaign := campaignTemplate
	task := taskTemplate

	dialerMonitorCh := make(chan interface{})
	alertsMonitorCh := make(chan interface{})
	recieveCallsCh := make(chan interface{})

	trunksState := MockTrunksState{}
	rmq := MockRmq{}
	publisher := MockPublisher{Rmq: &rmq}
	loadMonitor := MockLoadMonitor{Ch: dialerMonitorCh}
	alertMonitor := MockAlertMonitor{Ch: alertsMonitorCh}
	ds := MockDataStorage{
		campaigns: map[types.CampaignId]*types.Campaign{
			campaign.Id: &campaign,
		},
		tasks: map[types.CallTaskId]*types.ExtendedCallTask{
			task.Id: &task,
		},
		callResults: map[types.CallResultId]*types.CallResult{},
	}
	syncDB := outdoing.SyncDB{
		TrunkManager:              &trunksState,
		Publisher:                 &publisher,
		DataStorage:               &ds,
		CampaignStates:            map[types.CampaignId]outdoing.CampaignState{},
		RecieveCallCh:             recieveCallsCh,
		DialerMonitorChIn:         dialerMonitorCh,
		AlertsMonitorChIn:         alertsMonitorCh,
		RndGen:                    utils.NewRandomGenerator(ctx),
		ReplyToGen:                utils.NewSyncRoundRobin(ctx, 40),
		DoubleLoopMonitoring:      make(map[types.CampaignId]int, 200),
		DLMmutex:                  sync.Mutex{},
		DoubleSendTasksMonitoring: make(map[types.CampaignId]int, 200),
		DSTMmutex:                 sync.Mutex{},
	}
	recieveCalls := MockRecieveCalls{
		Ch: recieveCallsCh,
	}
	recieveCalls.OnEvent = func(e interface{}) {
		callTaskState, ok := e.(outdoing.CallTaskState)
		if !ok {
			t.Errorf("unexpected type %v", callTaskState)
			return
		}
		ds.CreateRecord(ctx, types.CallResult{
			CampaignId: task.CampaignId,
			Attempt:    types.Attempt{CallId: callTaskState.Data.LastCallId},
		})
		updateMap := types.M{
			"$inc": types.M{"attemptNumber": 1},
			"$set": types.M{"status": types.TASK_STATUS_DELAY},
		}
		switch len(recieveCalls.History) {
		case 1:
			syncDB.CampaignStates[campaign.Id].AlertCh <- struct{}{}
			time.Sleep(time.Second * 2)
			syncDB.SyncCampaigns(ctx)
			time.Sleep(time.Second * 2)
			ds.SetCampaignStatus(campaign.Id, types.CAMPAIGN_STATUS_ACTIVE)
			time.Sleep(time.Second * 2)
			syncDB.SyncCampaigns(ctx)
			time.Sleep(time.Second * 2)
			syncDB.CampaignStates[campaign.Id].AlertCh <- struct{}{}
			time.Sleep(time.Second * 2)
			syncDB.SyncCampaigns(ctx)
			time.Sleep(time.Second * 2)
			ds.SetCampaignStatus(campaign.Id, types.CAMPAIGN_STATUS_ACTIVE)
			time.Sleep(time.Second * 2)
			syncDB.SyncCampaigns(ctx)
			updateMap["$set"] = types.M{"status": types.TASK_STATUS_DELAY}
			ds.SetCampaignStatus(campaign.Id, types.CAMPAIGN_STATUS_PAUSED_BY_ALERT)

		case 2:
			ds.SetCampaignStatus(campaign.Id, types.CAMPAIGN_STATUS_PAUSED_BY_ALERT)
			syncDB.SyncCampaigns(ctx)
			time.Sleep(time.Second * 2)
		}
		ds.UpdateTask(ctx, task.Id, updateMap)
		callTaskState.CallFinishCh <- struct{}{}
		time.Sleep(time.Second * 2)
	}

	loadMonitorDone := loadMonitor.Listen(ctx)
	alertMonitorDone := alertMonitor.Listen(ctx)
	syncDB.SyncCampaigns(ctx)
	recieveCallsDone := recieveCalls.Listen(ctx)
	<-loadMonitorDone
	<-alertMonitorDone
	<-recieveCallsDone

	if len(recieveCalls.History) != 2 {
		t.Errorf("recieved calls %d", len(recieveCalls.History))
	}
}

func TestSyncDB_StartAfterNoTasks(t *testing.T) {
	t.Parallel()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	campaign := campaignTemplate
	campaign.Status = types.CAMPAIGN_STATUS_NO_TASKS
	task := taskTemplate

	dialerMonitorCh := make(chan interface{})
	alertsMonitorCh := make(chan interface{})
	recieveCallsCh := make(chan interface{})

	trunksState := MockTrunksState{}
	rmq := MockRmq{}
	publisher := MockPublisher{Rmq: &rmq}
	loadMonitor := MockLoadMonitor{Ch: dialerMonitorCh}
	alertMonitor := MockAlertMonitor{Ch: alertsMonitorCh}
	ds := MockDataStorage{
		campaigns: map[types.CampaignId]*types.Campaign{
			campaign.Id: &campaign,
		},
		tasks:       map[types.CallTaskId]*types.ExtendedCallTask{},
		callResults: map[types.CallResultId]*types.CallResult{},
	}
	syncDB := outdoing.SyncDB{
		TrunkManager:              &trunksState,
		Publisher:                 &publisher,
		DataStorage:               &ds,
		CampaignStates:            map[types.CampaignId]outdoing.CampaignState{},
		RecieveCallCh:             recieveCallsCh,
		DialerMonitorChIn:         dialerMonitorCh,
		AlertsMonitorChIn:         alertsMonitorCh,
		RndGen:                    utils.NewRandomGenerator(ctx),
		ReplyToGen:                utils.NewSyncRoundRobin(ctx, 40),
		DoubleLoopMonitoring:      make(map[types.CampaignId]int, 200),
		DLMmutex:                  sync.Mutex{},
		DoubleSendTasksMonitoring: make(map[types.CampaignId]int, 200),
		DSTMmutex:                 sync.Mutex{},
	}
	recieveCalls := MockRecieveCalls{
		Ch:      recieveCallsCh,
		OnEvent: func(interface{}) {},
	}

	loadMonitorDone := loadMonitor.Listen(ctx)
	alertMonitorDone := alertMonitor.Listen(ctx)
	syncDB.SyncCampaigns(ctx)
	recieveCallsDone := recieveCalls.Listen(ctx)
	time.Sleep(time.Second * 2)
	if _, err := ds.CreateIncomingTask(ctx, task); err != nil {
		t.Error(err)
		return
	}

	<-loadMonitorDone
	<-alertMonitorDone
	<-recieveCallsDone

	if len(rmq.PublishCalls) != 1 {
		t.Errorf("published calls %d", len(rmq.PublishCalls))
	}
}

func TestSyncDB_Recall(t *testing.T) {
	t.Parallel()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*20)
	defer cancel()

	campaign := campaignTemplate
	task := taskTemplate

	dialerMonitorCh := make(chan interface{})
	alertsMonitorCh := make(chan interface{})
	recieveCallsCh := make(chan interface{})

	trunksState := MockTrunksState{}
	rmq := MockRmq{}
	publisher := MockPublisher{Rmq: &rmq}
	loadMonitor := MockLoadMonitor{Ch: dialerMonitorCh}
	alertMonitor := MockAlertMonitor{Ch: alertsMonitorCh}
	ds := MockDataStorage{
		campaigns: map[types.CampaignId]*types.Campaign{
			campaign.Id: &campaign,
		},
		tasks: map[types.CallTaskId]*types.ExtendedCallTask{
			task.Id: &task,
		},
		callResults: map[types.CallResultId]*types.CallResult{},
	}
	syncDB := outdoing.SyncDB{
		TrunkManager:              &trunksState,
		Publisher:                 &publisher,
		DataStorage:               &ds,
		CampaignStates:            map[types.CampaignId]outdoing.CampaignState{},
		RecieveCallCh:             recieveCallsCh,
		DialerMonitorChIn:         dialerMonitorCh,
		AlertsMonitorChIn:         alertsMonitorCh,
		RndGen:                    utils.NewRandomGenerator(ctx),
		ReplyToGen:                utils.NewSyncRoundRobin(ctx, 40),
		DoubleLoopMonitoring:      make(map[types.CampaignId]int, 200),
		DLMmutex:                  sync.Mutex{},
		DoubleSendTasksMonitoring: make(map[types.CampaignId]int, 200),
		DSTMmutex:                 sync.Mutex{},
	}
	recieveCalls := MockRecieveCalls{
		Ch: recieveCallsCh,
	}
	recieveCalls.OnEvent = func(e interface{}) {
		callTaskState, ok := e.(outdoing.CallTaskState)
		if !ok {
			t.Errorf("unexpected type %v", callTaskState)
			return
		}
		ds.CreateRecord(ctx, types.CallResult{
			CampaignId: task.CampaignId,
			Attempt:    types.Attempt{CallId: callTaskState.Data.LastCallId},
		})
		updateMap := types.M{"$inc": types.M{"attemptNumber": 1}}

		switch len(recieveCalls.History) {
		case 1:
			updateMap["$set"] = types.M{"status": types.TASK_STATUS_DELAY}

		case 2:
			updateMap["$set"] = types.M{"status": types.TASK_STATUS_DELAY}
			time.Sleep(time.Second * 2)

		case 3:
			updateMap["$set"] = types.M{"status": types.TASK_STATUS_STOP}
		}
		ds.UpdateTask(ctx, task.Id, updateMap)
		callTaskState.CallFinishCh <- struct{}{}
		time.Sleep(time.Second * 2)
	}

	loadMonitorDone := loadMonitor.Listen(ctx)
	alertMonitorDone := alertMonitor.Listen(ctx)
	syncDB.SyncCampaigns(ctx)
	recieveCallsDone := recieveCalls.Listen(ctx)
	<-loadMonitorDone
	<-alertMonitorDone
	<-recieveCallsDone

	if len(recieveCalls.History) != 3 {
		t.Errorf("recieved calls %d", len(recieveCalls.History))
	}
}

func TestSyncDB_CleanEveryDay(t *testing.T) {
	t.Parallel()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	campaign := campaignTemplate

	campaignClean := campaignTemplate
	campaignClean.Id = "1"
	campaignClean.TaskSettings.CleanEveryDay = true
	campaignClean.IsActive = false

	campaignCleanActive := campaignTemplate
	campaignCleanActive.Id = "2"
	campaignCleanActive.TaskSettings.CleanEveryDay = true

	task := taskTemplate
	task.Id = "1"

	task2 := taskTemplate
	task2.Id = "2"
	task2.NextCallAt = time.Now().Add(time.Second * 6)

	taskClean := taskTemplate
	taskClean.Id = "3"
	taskClean.CampaignId = campaignClean.Id

	taskClean2 := taskTemplate
	taskClean2.Id = "4"
	taskClean2.CampaignId = campaignClean.Id
	taskClean2.NextCallAt = time.Now().Add(time.Hour)

	taskCleanActive := taskTemplate
	taskCleanActive.Id = "5"
	taskCleanActive.CampaignId = campaignCleanActive.Id
	taskCleanActive2 := taskTemplate
	taskCleanActive2.Id = "6"
	taskCleanActive2.CampaignId = campaignCleanActive.Id
	taskCleanActive2.NextCallAt = time.Now().Add(time.Hour)

	dialerMonitorCh := make(chan interface{})
	alertsMonitorCh := make(chan interface{})
	recieveCallsCh := make(chan interface{})

	trunksState := MockTrunksState{}
	rmq := MockRmq{}
	publisher := MockPublisher{Rmq: &rmq}
	loadMonitor := MockLoadMonitor{Ch: dialerMonitorCh}
	alertMonitor := MockAlertMonitor{Ch: alertsMonitorCh}
	ds := MockDataStorage{
		campaigns: map[types.CampaignId]*types.Campaign{
			campaign.Id:            &campaign,
			campaignClean.Id:       &campaignClean,
			campaignCleanActive.Id: &campaignCleanActive,
		},
		tasks: map[types.CallTaskId]*types.ExtendedCallTask{
			task.Id:             &task,
			task2.Id:            &task2,
			taskClean.Id:        &taskClean,
			taskCleanActive.Id:  &taskCleanActive,
			taskCleanActive2.Id: &taskCleanActive2,
		},
		callResults: map[types.CallResultId]*types.CallResult{},
	}
	syncDB := outdoing.SyncDB{
		TrunkManager:      &trunksState,
		Publisher:         &publisher,
		DataStorage:       &ds,
		CampaignStates:    map[types.CampaignId]outdoing.CampaignState{},
		RecieveCallCh:     recieveCallsCh,
		DialerMonitorChIn: dialerMonitorCh,
		AlertsMonitorChIn: alertsMonitorCh,
		DurationToClean: func() time.Duration {
			return time.Second * 3
		},
		RndGen:                    utils.NewRandomGenerator(ctx),
		ReplyToGen:                utils.NewSyncRoundRobin(ctx, 40),
		DoubleLoopMonitoring:      make(map[types.CampaignId]int, 200),
		DLMmutex:                  sync.Mutex{},
		DoubleSendTasksMonitoring: make(map[types.CampaignId]int, 200),
		DSTMmutex:                 sync.Mutex{},
	}
	recieveCalls := MockRecieveCalls{
		Ch: recieveCallsCh,
		OnEvent: func(e interface{}) {
			callTaskState, ok := e.(outdoing.CallTaskState)
			if !ok {
				t.Errorf("unexpected type %v", callTaskState)
				return
			}
			ds.CreateRecord(ctx, types.CallResult{
				CampaignId: callTaskState.Campaign.Id,
				CallTaskId: callTaskState.CallTask.Id,
				Attempt:    types.Attempt{CallId: callTaskState.Data.LastCallId},
			})
			ds.UpdateTask(ctx, callTaskState.CallTask.Id, types.M{
				"$set": types.M{"status": types.TASK_STATUS_STOP},
				"$inc": types.M{"attemptNumber": 1},
			})
			callTaskState.CallFinishCh <- struct{}{}
		},
	}

	startDone := make(chan struct{}, 1)
	go func() {
		syncDB.Start(ctx, time.Second)
		close(startDone)
	}()
	loadMonitorDone := loadMonitor.Listen(ctx)
	alertMonitorDone := alertMonitor.Listen(ctx)
	recieveCallsDone := recieveCalls.Listen(ctx)

	time.Sleep(time.Second * 6)
	ds.CreateIncomingTask(ctx, taskClean2)

	<-loadMonitorDone
	<-alertMonitorDone
	<-recieveCallsDone
	<-startDone

	cntCallsCampaign := 0
	cntCallsCampaignClean := 0
	cntCallsCampaignCleanActive := 0
	for _, result := range ds.callResults {
		switch result.CampaignId {
		case campaign.Id:
			cntCallsCampaign++
		case campaignClean.Id:
			cntCallsCampaignClean++
		case campaignCleanActive.Id:
			cntCallsCampaignCleanActive++
		}
	}
	if cntCallsCampaign != 2 {
		t.Errorf("calls in simple campaign %d", cntCallsCampaign)
	}
	if cntCallsCampaignClean != 0 {
		t.Errorf("calls in clean campaign %d", cntCallsCampaignClean)
	}
	if cntCallsCampaignCleanActive != 1 {
		t.Errorf("calls in clean and active campaign %d", cntCallsCampaignCleanActive)
	}

	cntDelayCampaign := 0
	cntDelayCampaignClean := 0
	cntDelayCampaignCleanActive := 0
	for _, task := range ds.tasks {
		if task.Status != types.TASK_STATUS_DELAY {
			continue
		}
		switch task.CampaignId {
		case campaign.Id:
			cntDelayCampaign++
		case campaignClean.Id:
			cntDelayCampaignClean++
		case campaignCleanActive.Id:
			cntDelayCampaignCleanActive++
		}
	}
	if cntDelayCampaign != 0 {
		t.Errorf("delay tasks in simple campaign %d", cntDelayCampaign)
	}
	if cntDelayCampaignClean != 1 {
		t.Errorf("delay tasks in clean campaign %d", cntDelayCampaignClean)
	}
	if cntDelayCampaignCleanActive != 0 {
		t.Errorf("delay tasks in clean and active campaign %d", cntDelayCampaignCleanActive)
	}
}
