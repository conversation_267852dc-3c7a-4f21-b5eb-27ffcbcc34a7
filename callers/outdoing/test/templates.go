package outdoing

import (
	"context"
	"github.com/pkg/errors"
	"sort"
	"sync"
	"time"

	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/mongodb"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type MockPublisherPublishArgs struct {
	Data        []byte
	RoutingKeys []string
	OptionFuncs []func(*rabbitmq.PublishOptions)
}

type MockPublisher struct {
	Rmq *MockRmq
}

func (p *MockPublisher) Close() {}

func (p *MockPublisher) NotifyReturn(handler func(r rabbitmq.Return)) {}

func (p *MockPublisher) Publish(
	data []byte,
	routingKeys []string,
	optionFuncs ...func(*rabbitmq.PublishOptions),
) error {
	p.Rmq.Mu.Lock()
	defer p.Rmq.Mu.Unlock()
	p.Rmq.PublishCalls = append(
		p.Rmq.PublishCalls,
		MockPublisherPublishArgs{
			Data:        data,
			RoutingKeys: routingKeys,
			OptionFuncs: optionFuncs,
		},
	)
	return nil
}

type MockRmq struct {
	PublishCalls []MockPublisherPublishArgs
	Mu           sync.RWMutex
}

func (r *MockRmq) NewPublisher() (types.RmqPublisher, error) {
	return &MockPublisher{Rmq: r}, nil
}

type MockLoadMonitor struct {
	History []interface{}
	Ch      chan interface{}
}

func (m *MockLoadMonitor) Listen(ctx context.Context) (doneCh chan struct{}) {
	doneCh = make(chan struct{}, 1)
	go func() {
		defer close(doneCh)
		for {
			select {
			case <-ctx.Done():
				return
			case e := <-m.Ch:
				m.History = append(m.History, e)
			}
		}
	}()
	return doneCh
}

type MockAlertMonitor struct {
	History []interface{}
	Ch      chan interface{}
}

func (m *MockAlertMonitor) Listen(ctx context.Context) (doneCh chan struct{}) {
	doneCh = make(chan struct{}, 1)
	go func() {
		defer close(doneCh)
		for {
			select {
			case <-ctx.Done():
				return
			case e := <-m.Ch:
				m.History = append(m.History, e)
			}
		}
	}()
	return doneCh
}

type MockRecieveCalls struct {
	History []interface{}
	Ch      chan interface{}
	OnEvent func(interface{})
}

func (m *MockRecieveCalls) Listen(ctx context.Context) (doneCh chan struct{}) {
	doneCh = make(chan struct{}, 1)
	go func() {
		defer close(doneCh)
		for {
			select {
			case <-ctx.Done():
				return
			case e := <-m.Ch:
				m.History = append(m.History, e)
				m.OnEvent(e)
			}
		}
	}()
	return doneCh
}

type MockTrunksState struct{}

func (ts *MockTrunksState) Incr(tc types.TrunkContext) {}

func (ts *MockTrunksState) Decr(tc types.TrunkContext) {}

func (ts *MockTrunksState) ChooseContext(tc []types.TrunkContext) *types.TrunkContext {
	return &types.TrunkContext{}
}
func (ts *MockTrunksState) SetContexts(tc map[types.TrunkContext]struct{}) {}

func (ts *MockTrunksState) Info() (trunksInfo, serversInfo map[string]string) {
	return nil, nil
}

type MockDataStorage struct {
	campaigns   map[types.CampaignId]*types.Campaign
	tasks       map[types.CallTaskId]*types.ExtendedCallTask
	callResults map[types.CallResultId]*types.CallResult
	mu          sync.RWMutex
}

func (s *MockDataStorage) GetProgressTasks(ctx context.Context) (<-chan types.CallTask, error) {
	resCh := make(chan types.CallTask)
	go func() {
		close(resCh)
	}()
	return resCh, nil
}

func (s *MockDataStorage) GetCleanEveryDayCampaignsId(ctx context.Context) (result []types.CampaignId, err error) {
	result = []types.CampaignId{}
	s.mu.RLock()
	defer s.mu.RUnlock()
	for _, campaign := range s.campaigns {
		if campaign.CallDirection == types.CAMPAIGN_CALL_DIRECTION_OUT && campaign.TaskSettings.CleanEveryDay {
			result = append(result, campaign.Id)
		}
	}
	return result, nil
}
func (s *MockDataStorage) StopCampaignTasks(ctx context.Context, campaignId []types.CampaignId) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	for _, task := range s.tasks {
		for _, id := range campaignId {
			if task.CampaignId == id && task.Status == types.TASK_STATUS_DELAY {
				task.Status = types.TASK_STATUS_STOP
			}
		}
	}
	return nil
}

func (s *MockDataStorage) SetCampaignIsActive(id types.CampaignId, v bool) {
	s.campaigns[id].IsActive = v
}

func (s *MockDataStorage) SetCampaignStatus(id types.CampaignId, status types.CampaignStatus) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	campaign, ok := s.campaigns[id]
	if !ok {
		return nil
	}
	campaign.Status = status
	return nil
}

func (s *MockDataStorage) CreateIncomingTask(ctx context.Context, callTask types.ExtendedCallTask) (types.ExtendedCallTask, error) {
	if callTask.Id == "" {
		callTask.Id = types.CallTaskId(primitive.NewObjectID().Hex())
	}
	s.mu.Lock()
	defer s.mu.Unlock()
	if _, ok := s.tasks[callTask.Id]; ok {
		return callTask, errors.New("not unique id")
	}
	s.tasks[callTask.Id] = &callTask
	return callTask, nil
}

func (s *MockDataStorage) CreateRecord(ctx context.Context, callRecord types.CallResult) error {
	if callRecord.Id == "" {
		callRecord.Id = types.CallResultId(primitive.NewObjectID().Hex())
	}
	s.mu.Lock()
	defer s.mu.Unlock()
	if _, ok := s.callResults[callRecord.Id]; ok {
		return errors.New("not unique id")
	}
	s.callResults[callRecord.Id] = &callRecord
	return nil
}

func (s *MockDataStorage) SetIndexes(ctx context.Context, requiredIndexes []mongodb.Idx, collectionName string) error {
	return nil
}

func (s *MockDataStorage) GetCallTaskById(ctx context.Context, id types.CallTaskId) (callTask types.ExtendedCallTask, err error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	callTask = *s.tasks[id]
	return callTask, nil
}

func (s *MockDataStorage) GetCampaignById(ctx context.Context, id types.CampaignId) (campaign types.Campaign, err error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	campaignP, ok := s.campaigns[id]
	if !ok {
		return campaign, nil
	}
	return *campaignP, nil
}

func (s *MockDataStorage) GetActiveCampaigns(ctx context.Context) (<-chan types.Campaign, error) {
	s.mu.RLock()
	ch := make(chan types.Campaign, 1)
	go func() {
		defer func() {
			s.mu.RUnlock()
			close(ch)
		}()
		for _, campaign := range s.campaigns {
			campaign := *campaign
			for _, status := range mongodb.CampaignStatusesForLoad {
				if status, ok := status.(types.CampaignStatus); ok && status == campaign.Status && campaign.IsActive {
					ch <- campaign
				}
			}
		}
	}()
	return ch, nil
}

func (s *MockDataStorage) UpdateTask(ctx context.Context, taskId types.CallTaskId, update types.M) (int, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	task, ok := s.tasks[taskId]
	if !ok {
		return 0, nil
	}
	if set, ok := update["$set"].(types.M); ok {
		if status, ok := set["status"].(types.TaskStatus); ok {
			if task.Status != types.TASK_STATUS_DELAY &&
				task.Status != types.TASK_STATUS_PROGRESS {
				return 0, nil
			}
			task.Status = status
		}
		if lastCallId, ok := set["lastCallId"].(types.CallId); ok {
			task.LastCallId = lastCallId
		}
		if lastCallerId, ok := set["lastCallerId"].(string); ok {
			task.LastCallerId = lastCallerId
		}
		if lastCallStatus, ok := set["lastCallStatus"].(types.CallStatus); ok {
			task.LastCallStatus = lastCallStatus
		}
		if nextPhoneIndex, ok := set["nextPhoneIndex"].(int); ok {
			task.NextPhoneIndex = nextPhoneIndex
		}
		if nextTz, ok := set["nextTz"].(int); ok {
			task.NextTz = nextTz
		}
		if lastTrunk, ok := set["lastTrunk"].(types.TrunkContext); ok {
			task.LastTrunk = lastTrunk
		}
		if attemptNumber, ok := set["attemptNumber"].(int); ok {
			task.AttemptNumber = attemptNumber
		}
		if lastRequestToDialerAt, ok := set["lastRequestToDialerAt"].(time.Time); ok {
			task.LastRequestToDialerAt = lastRequestToDialerAt
		}
		if nextCallAt, ok := set["nextCallAt"].(time.Time); ok {
			task.NextCallAt = nextCallAt
		}
	}
	return 1, nil
}

func (s *MockDataStorage) GetMultiphonesId(ctx context.Context, ids []types.CallerMultiphoneId) (multiphonesId map[types.CallerMultiphoneId]types.CallerMultiphone, err error) {
	return map[types.CallerMultiphoneId]types.CallerMultiphone{}, nil
}

func (s *MockDataStorage) GetAttemptsByTaskId(ctx context.Context, tasksId []types.CallTaskId) (<-chan types.Attempt, error) {
	ch := make(chan types.Attempt, 1)
	s.mu.RLock()
	go func() {
		defer func() {
			s.mu.RUnlock()
			close(ch)
		}()
		for _, callResult := range s.callResults {
			for _, taskId := range tasksId {
				if callResult.CallTaskId == taskId {
					ch <- callResult.Attempt
				}
			}
		}
	}()
	return ch, nil
}

func (s *MockDataStorage) HasActualTasks(
	ctx context.Context,
	campaignId types.CampaignId,
	isTest bool,
) (bool, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	for _, task := range s.tasks {
		if task.CampaignId == campaignId && task.IsTest == isTest &&
			(task.Status == types.TASK_STATUS_DELAY || task.Status == types.TASK_STATUS_PROGRESS) {
			return true, nil
		}
	}
	return false, nil
}

func (s *MockDataStorage) GetCampaignActualTasks(ctx context.Context, campaign types.Campaign, t time.Time) <-chan types.CallTask {
	taskCh := make(chan types.CallTask)
	go func() {
		s.mu.RLock()
		defer close(taskCh)
		tasks := []types.ExtendedCallTask{}
		for _, task := range s.tasks {
			task := *task
			if task.CampaignId != campaign.Id {
				continue
			}
			if task.Status != types.TASK_STATUS_DELAY {
				continue
			}
			if task.IsTest != campaign.IsTest {
				continue
			}
			if task.NextCallAt.Unix() == 0 || task.NextCallAt.IsZero() || task.NextCallAt.After(t) {
				continue
			}
			if task.StartDate.Unix() != 0 && !task.StartDate.IsZero() && !task.StartDate.Before(t) {
				continue
			}
			for _, callResult := range s.callResults {
				if callResult.CallTaskId == task.Id {
					task.CallResults = append(task.CallResults, callResult.Attempt)
				}
			}
			tasks = append(tasks, task)
		}
		sort.Slice(tasks, func(i int, j int) bool {
			if tasks[i].LastCallStatus != tasks[j].LastCallStatus {
				if tasks[i].LastCallStatus == types.CALL_STATUS_REQUEST_RECALL {
					return true
				} else if tasks[j].LastCallStatus == types.CALL_STATUS_REQUEST_RECALL {
					return false
				}
			}
			if iTimesPriority, jTimesPriority :=
				campaign.TimePriority(t.In(utils.LocationByOffset(tasks[i].NextTz))),
				campaign.TimePriority(t.In(utils.LocationByOffset(tasks[j].NextTz))); iTimesPriority != jTimesPriority {
				return iTimesPriority < jTimesPriority
			}
			if tasks[i].Priority != tasks[j].Priority {
				if tasks[i].Priority == 0 {
					return false
				}
				return tasks[i].Priority > tasks[j].Priority
			}
			if tasks[i].NextTz != tasks[j].NextTz {
				if tasks[i].NextTz == 0 {
					return false
				}
				return tasks[i].NextTz > tasks[j].NextTz
			}
			if tasks[i].NextCallAt != tasks[j].NextCallAt {
				return tasks[i].NextCallAt.Before(tasks[j].NextCallAt)
			}
			return tasks[i].CreatedAt.Before(tasks[j].CreatedAt)
		})
		s.mu.RUnlock()
		for _, task := range tasks {
			taskCh <- task.CallTask
		}
	}()
	return taskCh
}

var now = time.Now()
var campaignTemplate = types.Campaign{
	Id:            "123",
	Name:          "simple_campaign",
	PublicId:      1,
	Lines:         2,
	CallDirection: types.CAMPAIGN_CALL_DIRECTION_OUT,
	BuildId:       "123",
	Status:        types.CAMPAIGN_STATUS_ACTIVE,
	IsActive:      true,
	IsTest:        false,
	CallerSettings: types.CallerSettings{
		ShortCallTimeout:         5,
		SilenceAsDown:            false,
		PhonesAttemptCounterType: types.COUNTER_TYPE_BY_ALL,
		PostResult:               true,
	},
	TrunkSettings: types.TrunkSettings{
		CallerId:     "+79999999999",
		Timeout:      360,
		CallTTL:      360,
		MultiphoneId: "",
		Context: []types.TrunkContext{
			{
				Trunk:  "prod:1",
				Server: "1",
			},
			{
				Trunk:  "prod:2",
				Server: "2",
			},
			{
				Trunk:  "prod:3",
				Server: "3",
			},
		},
	},
	RecallSettings: types.RecallSettings{
		DefaultAttempts: 3,
		DefaultTimeout:  30,
		AttemptsByStatus: types.AttemptByStatus{
			"down": {
				Number:  2,
				Timeout: 20,
			},
		},
		RecallsByCallResult: types.AttemptByStatus{
			"silence": {
				Number:  1,
				Timeout: 10,
			},
		},
	},
	AlertSettings: types.AlertSettings{
		Error:              10,
		TrunkError:         10,
		TimeoutError:       10,
		Down:               10,
		AnsweringMachine:   10,
		TimeoutKill:        10,
		ErrorBeforeUp:      10,
		ErrorAfterUp:       10,
		CallTransferHangUp: 10,
	},
	ScheduleSettings: types.ScheduleSettings{
		CampaignTime: types.Interval{
			Start: 0,
			End:   86400,
		},
		LocalUserTime: types.Interval{
			Start: 0,
			End:   86400,
		},
		OptimalTimes: []types.Interval{},
		AvoidTimes:   []types.Interval{},
	},
	BuildInfo: types.BuildInfo{
		Description: "some_build",
		Project:     "some_project",
		Dialogue: types.Dialogue{
			Id:      "123",
			Name:    "some_dialogue",
			Version: 1,
		},
	},
	FieldData: types.M{},
}
var taskTemplate = types.ExtendedCallTask{
	Project:   campaignTemplate.BuildInfo.Project,
	CallData:  types.CallData{},
	Status:    types.TASK_STATUS_DELAY,
	UpdatedAt: now,
	CallTask: types.CallTask{
		Id:         "123",
		CampaignId: campaignTemplate.Id,
		ExternalId: "1",
		IsTest:     false,
		Phones: []types.TaskPhone{
			{
				Phone:           "+79998888888",
				NormalizedPhone: "+79998888888",
				Timezone:        3,
				Region:          "MSK",
			},
			{
				Phone:           "+79997777777",
				NormalizedPhone: "+79997777777",
				Timezone:        5,
				Region:          "EKB",
			},
		},
		NextCallAt: now,
		CreatedAt:  now,
	},
}
