package outdoing

import (
	"fmt"
	"sort"
	"sync"
	"sync/atomic"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
)

type counter struct {
	busy  atomic.Int32
	limit int
}

type TrunkManager struct {
	contexts map[types.TrunkContext]struct{}
	trunks   map[string]*counter
	servers  map[string]*counter
	mutex    sync.RWMutex
}

func (tm *TrunkManager) SetContexts(tc map[types.TrunkContext]struct{}) {
	tm.mutex.Lock()
	for trunkContext := range tc {
		if trunkContext.Trunk != "" && tm.trunks[trunkContext.Trunk] == nil {
			tm.trunks[trunkContext.Trunk] = &counter{limit: 0}
		}
		if trunkContext.Server != "" && trunkContext.Server != types.Trunk_CurrentServer && tm.servers[trunkContext.Server] == nil {
			tm.servers[trunkContext.Server] = &counter{limit: 0}
		}
	}
	tm.contexts = tc
	tm.mutex.Unlock()
}

func (tm *TrunkManager) Incr(tc types.TrunkContext) {
	tm.mutex.RLock()
	if ctr := tm.trunks[tc.Trunk]; tc.Trunk != "" && ctr != nil {
		ctr.busy.Add(1)
	}
	if ctr := tm.servers[tc.Server]; tc.Server != "" &&
		tc.Server != types.Trunk_CurrentServer &&
		ctr != nil {
		ctr.busy.Add(1)
	}
	tm.mutex.RUnlock()
}

func (tm *TrunkManager) Decr(tc types.TrunkContext) {
	tm.mutex.RLock()
	if ctr := tm.trunks[tc.Trunk]; tc.Trunk != "" && ctr != nil {
		ctr.busy.Add(-1)
	}
	if ctr := tm.servers[tc.Server]; tc.Server != "" && tc.Server != types.Trunk_CurrentServer && ctr != nil {
		ctr.busy.Add(-1)
	}
	tm.mutex.RUnlock()
}

func (tm *TrunkManager) ChooseContext(tc []types.TrunkContext) *types.TrunkContext {
	freeContexts := make([]types.TrunkContext, 0, len(tc))
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	for _, trunkContext := range tc {
		if _, ok := tm.contexts[trunkContext]; ok {
			if ctr := tm.trunks[trunkContext.Trunk]; ctr == nil || ctr.limit == 0 || int(ctr.busy.Load()) < ctr.limit {
				if ctr := tm.servers[trunkContext.Server]; ctr == nil || ctr.limit == 0 || int(ctr.busy.Load()) < ctr.limit {
					freeContexts = append(freeContexts, trunkContext)
				}
			}
		}
	}
	if len(freeContexts) == 0 {
		return nil
	}
	sort.Slice(freeContexts, func(i, j int) bool {
		trunkCounter_i := tm.trunks[freeContexts[i].Trunk]
		trunkCounter_j := tm.trunks[freeContexts[j].Trunk]
		if trunkCounter_i == nil {
			return false
		} else if trunkCounter_j == nil {
			return true
		} else {
			return trunkCounter_i.busy.Load() < trunkCounter_j.busy.Load()
		}
	})
	resultContext := freeContexts[0]
	if ctr := tm.trunks[resultContext.Trunk]; resultContext.Trunk != "" && ctr != nil {
		ctr.busy.Add(1)
	}
	if ctr := tm.servers[resultContext.Server]; resultContext.Server != "" &&
		resultContext.Server != types.Trunk_CurrentServer &&
		ctr != nil {
		ctr.busy.Add(1)
	}
	return &resultContext
}

func (tm *TrunkManager) Info() (trunksInfo, serversInfo map[string]string) {
	tm.mutex.RLock()
	trunksInfo = make(map[string]string, len(tm.trunks))
	serversInfo = make(map[string]string, len(tm.servers))
	for tr, ctr := range tm.trunks {
		if ctr == nil || ctr.limit == 0 {
			trunksInfo[tr] = "unlimited"
		} else {
			trunksInfo[tr] = fmt.Sprintf("use %d of %d", ctr.busy.Load(), ctr.limit)
		}
	}
	for sv, ctr := range tm.servers {
		if ctr == nil || ctr.limit == 0 {
			serversInfo[sv] = "unlimited"
		} else {
			serversInfo[sv] = fmt.Sprintf("use %d of %d", ctr.busy.Load(), ctr.limit)
		}
	}
	tm.mutex.RUnlock()
	return trunksInfo, serversInfo
}

func NewTrunkManager() *TrunkManager {
	return &TrunkManager{
		contexts: map[types.TrunkContext]struct{}{},
		trunks:   map[string]*counter{},
		servers:  map[string]*counter{},
	}
}
