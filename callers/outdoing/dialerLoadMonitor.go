package outdoing

import (
	"context"
	"fmt"
	"time"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
)

var DialerLoadMonitorTimeLayout = "2006-01-02 15:04"
var expireSeconds int32 = 2592000
var expireField string = "savedAt"

type MonitorDataStorage interface {
	CreateCampaignLoadPoint(ctx context.Context, point types.CampaignLoadPoint) error
	SetCampaignLoadPointsExpireIndex(ctx context.Context, expireField string, expireSeconds int32) error
}

type MonitorTimeHandler struct{}

func (m MonitorTimeHandler) Now() time.Time {
	return time.Now()
}

type MonitorTime interface {
	Now() time.Time
}

type MinMaxCounter struct {
	Min     int
	Max     int
	Current int
}

func (m MinMaxCounter) Incr() MinMaxCounter {
	m.Current++
	if m.Max == 0 || m.Max < m.Current {
		m.Max = m.Current
	}
	return m
}

func (m MinMaxCounter) Decr() MinMaxCounter {
	m.Current--
	if m.Current < 0 {
		m.Current = 0
	}
	if m.Min > m.Current {
		m.Min = m.Current
	}
	return m
}

func (m MinMaxCounter) Reset(val int) MinMaxCounter {
	m.Min = val
	m.Max = val
	m.Current = val
	return m
}

func MakeCallStatuses() types.CallerCallStatusMetricsList {
	return types.CallerCallStatusMetricsList{
		types.CALLER_CALL_STATUS_EMPTY:                 0,
		types.CALLER_CALL_STATUS_PREPARING:             0,
		types.CALLER_CALL_STATUS_CALLING_TO_USER:       0,
		types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     0,
		types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   0,
		types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: 0,
	}
}

func MakeCallStatusesCounter() [6]MinMaxCounter {
	return [6]MinMaxCounter{
		types.CALLER_CALL_STATUS_EMPTY:                 {},
		types.CALLER_CALL_STATUS_PREPARING:             {},
		types.CALLER_CALL_STATUS_CALLING_TO_USER:       {},
		types.CALLER_CALL_STATUS_IN_CALL_WITH_USER:     {},
		types.CALLER_CALL_STATUS_CALLING_TO_OPERATOR:   {},
		types.CALLER_CALL_STATUS_IN_CALL_WITH_OPERATOR: {},
	}
}

type DialerLoadMonitorActiveCall struct {
	Status   types.CallerCallStatus
	DialerId string
}

type DialerLoadMonitorCampaign struct {
	Id              types.CampaignId
	LastSentAt      time.Time // time value
	LastSentAtFmt   string    // time value, formatted without seconds
	Lines           int
	BusyLines       MinMaxCounter
	LinesPerSlot    map[string]MinMaxCounter // key = slot
	LinesPerSlotMin map[string]int
	LinesPerSlotMax map[string]int
	LinesPerSlotCur map[string]int
	CallStatuses    [6]MinMaxCounter // key = status
	CallStatusesMin types.CallerCallStatusMetricsList
	CallStatusesMax types.CallerCallStatusMetricsList
	CallStatusesCur types.CallerCallStatusMetricsList
	ActiveCallIds   map[types.CallId]DialerLoadMonitorActiveCall // key = call id
}

func MakeDialerLoadMonitorCampaign(campaignId types.CampaignId, monitorTime MonitorTime) DialerLoadMonitorCampaign {
	currTime := monitorTime.Now()
	return DialerLoadMonitorCampaign{
		Id:              campaignId,
		LastSentAt:      currTime,
		LastSentAtFmt:   currTime.Format(DialerLoadMonitorTimeLayout),
		Lines:           0,
		LinesPerSlot:    make(map[string]MinMaxCounter),
		LinesPerSlotMin: make(map[string]int),
		LinesPerSlotMax: make(map[string]int),
		LinesPerSlotCur: make(map[string]int),
		CallStatuses:    MakeCallStatusesCounter(),
		CallStatusesMin: MakeCallStatuses(),
		CallStatusesMax: MakeCallStatuses(),
		CallStatusesCur: MakeCallStatuses(),
		ActiveCallIds:   make(map[types.CallId]DialerLoadMonitorActiveCall),
	}
}

// if one minute passed, new values will be saved
func (c DialerLoadMonitorCampaign) DialerLoadMonitorCheckTime(
	ctx context.Context,
	dataStorage MonitorDataStorage,
	monitorTime MonitorTime,
) DialerLoadMonitorCampaign {
	log := log.CampaignId(c.Id)
	currTime := monitorTime.Now()
	currTimeFmt := currTime.Format(DialerLoadMonitorTimeLayout)
	if c.Lines == 0 || c.BusyLines.Current != 0 && currTimeFmt == c.LastSentAtFmt {
		return c
	}
	c.LastSentAtFmt = currTimeFmt
	c.LastSentAt = currTime

	for status, counter := range c.CallStatuses {
		c.CallStatusesMin[status] = counter.Min
		c.CallStatusesMax[status] = counter.Max
		c.CallStatusesCur[status] = counter.Current
		c.CallStatuses[status] = counter.Reset(counter.Current)
	}
	for slot, counter := range c.LinesPerSlot {
		// при отправке таски слот ещё не известен, поэтому в мониторинге проскакивают пустые значения
		if slot == "" {
			c.LinesPerSlot[slot] = counter.Reset(counter.Current)
			continue
		}
		c.LinesPerSlotMin[slot] = counter.Min
		c.LinesPerSlotMax[slot] = counter.Max
		c.LinesPerSlotCur[slot] = counter.Current
		c.LinesPerSlot[slot] = counter.Reset(counter.Current)
	}

	point := types.CampaignLoadPoint{
		CampaignId:      c.Id,
		Lines:           c.Lines,
		LinesPerSlotMin: c.LinesPerSlotMin,
		LinesPerSlotMax: c.LinesPerSlotMax,
		LinesPerSlotCur: c.LinesPerSlotCur,
		BusyLinesMin:    c.BusyLines.Min,
		BusyLinesMax:    c.BusyLines.Max,
		BusyLinesCur:    c.BusyLines.Current,
		CallStatusesMin: c.CallStatusesMin,
		CallStatusesMax: c.CallStatusesMax,
		CallStatusesCur: c.CallStatusesCur,
		SavedAt:         c.LastSentAt,
	}
	ctxTimeout, calncelFunc := context.WithTimeout(ctx, time.Minute)
	defer calncelFunc()
	err := dataStorage.CreateCampaignLoadPoint(ctxTimeout, point)
	if err != nil {
		log.TraceError(err)
	}
	return c
}

type DialerLoadMonitorCampaignLines struct {
	Id    types.CampaignId
	Lines int
}

type DialerLoadMonitorTaskStarted struct {
	CampaignId types.CampaignId
	CallId     types.CallId
}

type DialerLoadMonitorTaskStopped struct {
	CampaignId types.CampaignId
	CallId     types.CallId
}

type DialerLoadMonitorTaskCallStatus struct {
	CallId     types.CallId
	DialerId   string
	CampaignId types.CampaignId
	Status     types.CallerCallStatus
}

// StartDialerLoadMonitor - save campaign load stats to database (once at every minute, if it was some event)
func StartDialerLoadMonitor(ctx context.Context, chEvents chan interface{}, dataStorage MonitorDataStorage, monitorTime MonitorTime) error {
	err := dataStorage.SetCampaignLoadPointsExpireIndex(ctx, expireField, expireSeconds)
	if err != nil {
		return fmt.Errorf("dataStorage.SetCampaignLoadPointsExpireIndex -> %w", err)
	}
	campaigns := make(map[types.CampaignId]DialerLoadMonitorCampaign)
	log.Info("Start StartDialerLoadMonitor")
	for {
		select {
		case <-ctx.Done():
			return nil
		case v := <-chEvents:
			switch v := v.(type) {
			case DialerLoadMonitorCampaignLines:
				campaign, ok := campaigns[v.Id]
				if !ok {
					campaign = MakeDialerLoadMonitorCampaign(v.Id, monitorTime)
				}
				linesBefore := campaign.Lines
				campaign.Lines = v.Lines
				if !ok || linesBefore != v.Lines {
					campaign = campaign.DialerLoadMonitorCheckTime(ctx, dataStorage, monitorTime)
				}
				campaigns[v.Id] = campaign

			case DialerLoadMonitorTaskStarted:
				campaign, ok := campaigns[v.CampaignId]
				if !ok {
					campaign = MakeDialerLoadMonitorCampaign(v.CampaignId, monitorTime)
				}

				campaign.BusyLines = campaign.BusyLines.Incr()
				campaign.ActiveCallIds[v.CallId] = DialerLoadMonitorActiveCall{
					Status:   types.CALLER_CALL_STATUS_EMPTY,
					DialerId: "",
				}
				campaigns[v.CampaignId] = campaign.DialerLoadMonitorCheckTime(ctx, dataStorage, monitorTime)

			case DialerLoadMonitorTaskStopped:
				campaign, ok := campaigns[v.CampaignId]
				if !ok {
					continue
				}

				campaign.BusyLines = campaign.BusyLines.Decr()
				if call, ok := campaign.ActiveCallIds[v.CallId]; ok && call.Status != types.CALLER_CALL_STATUS_EMPTY {
					cnt := campaign.CallStatuses[call.Status]
					campaign.CallStatuses[call.Status] = cnt.Decr()
					cnt = campaign.LinesPerSlot[call.DialerId]
					campaign.LinesPerSlot[call.DialerId] = cnt.Decr()
				}
				delete(campaign.ActiveCallIds, v.CallId)
				campaign = campaign.DialerLoadMonitorCheckTime(ctx, dataStorage, monitorTime)
				if campaign.BusyLines.Current == 0 {
					delete(campaigns, v.CampaignId)
				} else {
					campaigns[v.CampaignId] = campaign
				}

			case DialerLoadMonitorTaskCallStatus:
				campaign, ok := campaigns[v.CampaignId]
				if !ok {
					continue
				}

				call, ok := campaign.ActiveCallIds[v.CallId]
				if !ok {
					continue
				}
				cnt := campaign.CallStatuses[call.Status]
				if call.Status != types.CALLER_CALL_STATUS_EMPTY {
					campaign.CallStatuses[call.Status] = cnt.Decr()
				}
				call.Status = v.Status
				cnt = campaign.CallStatuses[call.Status]
				campaign.CallStatuses[call.Status] = cnt.Incr()

				if call.Status == types.CALLER_CALL_STATUS_PREPARING && v.DialerId != "" {
					call.DialerId = v.DialerId
					if counter, ok := campaign.LinesPerSlot[call.DialerId]; ok {
						campaign.LinesPerSlot[call.DialerId] = counter.Incr()
					} else {
						campaign.LinesPerSlot[v.DialerId] = MinMaxCounter{}.Incr()
					}
				}
				campaign.ActiveCallIds[v.CallId] = call
				campaigns[v.CampaignId] = campaign.DialerLoadMonitorCheckTime(ctx, dataStorage, monitorTime)
			}
		}
	}
}
