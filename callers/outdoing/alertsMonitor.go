package outdoing

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

const (
	alertType_linesExceeded = "lines_exceeded"
	alertType_resultSeries  = "result_series"
)

const (
	ALERT_TYPE_LINES_EXCEEDED AlertType = iota
	ALERT_TYPE_RESULT_SERIES
)

type AlertType uint8

var (
	alertTypes = [2]string{
		ALERT_TYPE_LINES_EXCEEDED: alertType_linesExceeded,
		ALERT_TYPE_RESULT_SERIES:  alertType_resultSeries,
	}
	alertTypeMap = map[string]AlertType{
		alertType_linesExceeded: ALERT_TYPE_LINES_EXCEEDED,
		alertType_resultSeries:  ALERT_TYPE_RESULT_SERIES,
	}
)

func (at AlertType) String() string {
	return alertTypes[at]
}

func (at AlertType) MarshalJSON() ([]byte, error) {
	if int(at) >= len(alertTypes) {
		return nil, fmt.Errorf("invalid value: %d", at)
	}
	return json.Marshal(alertTypes[at])
}

func (at *AlertType) UnmarshalJSON(data []byte) error {
	var str string
	if err := json.Unmarshal(data, &str); err != nil {
		return err
	}
	val, ok := alertTypeMap[str]
	if !ok {
		return fmt.Errorf("invalid value: %s", str)
	}
	*at = val
	return nil
}

type CampaignAlerts struct {
	statusesRowValue  types.CallStatus
	statusesRowCount  int
	activeLines       int
	alertSeriesSentAt time.Time
	alertLinesSentAt  time.Time
	isActive          bool
	alertCh           chan struct{}
}

var mapMessageDetails = [2][2]string{
	types.CAMPAIGN_CALL_DIRECTION_IN:  {"входящей", "я НЕ застопился, идите проверьте"},
	types.CAMPAIGN_CALL_DIRECTION_OUT: {"исходящей", "я застопился, идите проверьте"},
}

func (ca CampaignAlerts) mapAlertSeries(status types.CallStatus, count int, campaign types.Campaign) string {
	details := mapMessageDetails[campaign.CallDirection]
	return fmt.Sprintf(
		"в прозвоне по %s кампании %q (id=%d, project=%s) серия %s из %d подряд, %s",
		details[0],
		campaign.Name,
		campaign.PublicId,
		campaign.BuildInfo.Project,
		status,
		count,
		details[1],
	)
}

func (ca CampaignAlerts) mapAlertLines(campaign types.Campaign) string {
	details := mapMessageDetails[campaign.CallDirection]
	return fmt.Sprintf(
		"в прозвоне по %s кампании %q (id=%d, project=%s) превышено кол-во линий - %d (лимит - %d)",
		details[0],
		campaign.Name,
		campaign.PublicId,
		campaign.BuildInfo.Project,
		ca.activeLines,
		campaign.Lines,
	)
}

type AlertsMonitorCallStarted struct {
	Campaign types.Campaign
}

type AlertsMonitorCallResult struct {
	Status   types.CallStatus
	Campaign types.Campaign
}

type AlertsMonitorCampaignStarted struct {
	Campaign types.Campaign
	AlertCh  chan struct{}
}

type AlertsMonitorCampaignStopped struct {
	Id types.CampaignId
}

type AlertSender interface {
	SendAlert(msg interface{}) error
}

type AlertData struct {
	CampaignId types.CampaignId `json:"campaign_id"`
	AlertType  AlertType        `json:"alert_type"`
	LinesLimit int              `json:"lines_limit"`
	LinesUsed  int              `json:"lines_used"`
}

func MakeAlertSenders(publisher types.RmqPublisher) []AlertSender {
	alertSenders := []AlertSender{}
	if config.Cfg.Telegram.KvintService {
		alertSenders = append(
			alertSenders,
			utils.MakeTgKvintServiceSender(
				config.Cfg.Telegram.KvintServiceEndpoint,
				config.Cfg.Telegram.KvintServiceChannelId,
				config.Cfg.Telegram.KvintServiceApiKey,
			),
		)
	}
	if config.Cfg.Telegram.Direct {
		alertSenders = append(
			alertSenders,
			utils.MakeTgDirectSender(
				config.Cfg.Telegram.ErrorChatId,
				config.Cfg.Telegram.ErrorToken,
			),
		)
	}
	if config.Cfg.IntegrationAlert {
		alertSenders = append(
			alertSenders,
			utils.MakeIntegrationsSender(publisher),
		)
	}
	return alertSenders
}

func StartAlertsMonitor(ctx context.Context, chEvents chan interface{}, alertSenders []AlertSender) {
	log.Info("Start StartAlertsMonitor")
	values := make(map[types.CampaignId]CampaignAlerts)
	for {
		select {
		case <-ctx.Done():
			return

		case v := <-chEvents:
			switch v := v.(type) {
			case AlertsMonitorCampaignStarted:
				ca := values[v.Campaign.Id]
				ca.alertCh = v.AlertCh
				ca.isActive = true
				values[v.Campaign.Id] = ca

			case AlertsMonitorCampaignStopped:
				ca, ok := values[v.Id]
				if !ok {
					continue
				}
				if ca.alertCh != nil {
					close(ca.alertCh)
					ca.alertCh = nil
				}
				if ca.activeLines < 1 {
					delete(values, v.Id)
					continue
				}
				ca.isActive = false
				values[v.Id] = ca

			case AlertsMonitorCallStarted:
				ca := values[v.Campaign.Id]
				ca.activeLines++
				// check lines exceeded alert
				if v.Campaign.Lines > 0 && ca.activeLines > v.Campaign.Lines && time.Since(ca.alertLinesSentAt) >= time.Minute {
					ca.alertLinesSentAt = time.Now()
					if errMsg := ca.mapAlertLines(v.Campaign); errMsg != "" {
						ca.sendAlerts(ALERT_TYPE_LINES_EXCEEDED, errMsg, v.Campaign, alertSenders)
					}
				}
				values[v.Campaign.Id] = ca

			case AlertsMonitorCallResult:
				log := log.CampaignId(v.Campaign.Id)
				ca, ok := values[v.Campaign.Id]
				if !ok {
					continue
				}
				ca.activeLines--
				if v.Status == ca.statusesRowValue {
					ca.statusesRowCount++
				} else {
					ca.statusesRowValue = v.Status
					ca.statusesRowCount = 1
				}
				// check calls with status like `down`/`answering_machine` and send alert, if it was upper the limit in a row
				if limitToAlert := v.Campaign.AlertSettings.StatusMap()[v.Status]; limitToAlert > 0 &&
					ca.statusesRowCount >= limitToAlert &&
					time.Since(ca.alertSeriesSentAt) >= time.Minute {
					if errMsg := ca.mapAlertSeries(ca.statusesRowValue, ca.statusesRowCount, v.Campaign); errMsg != "" {
						ca.alertSeriesSentAt = time.Now()
						ca.sendAlerts(ALERT_TYPE_RESULT_SERIES, errMsg, v.Campaign, alertSenders)
						if ca.alertCh != nil {
							ca.alertCh <- struct{}{}
						}
					} else {
						log.Error("campaignAlerts, failed to map error msg for status - %s", v.Status)
					}
					ca.statusesRowValue = types.CALL_STATUS_UNKNOWN
					ca.statusesRowCount = 0
				}
				if ca.activeLines < 1 && !ca.isActive {
					if ca.alertCh != nil {
						close(ca.alertCh)
					}
					delete(values, v.Campaign.Id)
					continue
				}
				values[v.Campaign.Id] = ca
			}
		}
	}
}

func (ca CampaignAlerts) sendAlerts(alertType AlertType, msg string, campaign types.Campaign, alertSenders []AlertSender) {
	log := log.CampaignId(campaign.Id)
	var wg sync.WaitGroup
	for _, sender := range alertSenders {
		var anyMessage interface{}
		if integrationsSender, ok := sender.(utils.IntegrationsSender); ok {
			if !campaign.CallerSettings.PostResult {
				continue
			}
			anyMessage = AlertData{
				CampaignId: campaign.Id,
				AlertType:  alertType,
				LinesLimit: campaign.Lines,
				LinesUsed:  ca.activeLines,
			}
			sender = integrationsSender.InQueue([]string{fmt.Sprintf("%s%s", types.PostResult_QueuePrefix, campaign.BuildInfo.Project)})
		} else {
			anyMessage = msg
		}
		wg.Add(1)
		go func(sender AlertSender, msg interface{}) {
			if err := sender.SendAlert(msg); err != nil {
				log.TraceError(err)
			}
			wg.Done()
		}(sender, anyMessage)
	}
	wg.Wait()
}
