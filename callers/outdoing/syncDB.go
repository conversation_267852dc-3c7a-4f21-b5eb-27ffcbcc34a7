package outdoing

import (
	"context"
	"encoding/json"
	"github.com/pkg/errors"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/mongodb"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	logger "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/logger/typed_logger"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

var log = logger.DefaultLogger

type CallState struct {
	Phone    types.TaskPhone
	CallerId string
	Trunk    types.TrunkContext
	States   []types.CallerCallStatus
	*types.Attempt
}

type CampaignSetDataEvent struct {
	Campaign   types.Campaign
	Multiphone types.CallerMultiphone
}

type CampaignChangeStatusStep uint8

const (
	Approved CampaignChangeStatusStep = iota
	WaitFirst
	WaitSecond
)

type CampaignState struct {
	IsActive  bool
	Direction types.CampaignCallDirection
	DataCh    chan CampaignSetDataEvent
	ActiveCh  chan bool
	AlertCh   chan struct{}
	DoneCh    chan struct{}
	// тип сортировки тасок
	TaskPriority string
}

func NewCampaignState(direction types.CampaignCallDirection, taskPriority string) CampaignState {
	return CampaignState{
		IsActive:     true,
		Direction:    direction,
		DataCh:       make(chan CampaignSetDataEvent, 1),
		ActiveCh:     make(chan bool, 1),
		AlertCh:      make(chan struct{}, 1),
		DoneCh:       make(chan struct{}, 1),
		TaskPriority: taskPriority,
	}
}

func (c CampaignState) Loop(
	ctx context.Context,
	campaign types.Campaign,
	multiphone types.CallerMultiphone,
	syncDB *SyncDB,
	queueSize int,
	callFinishCh chan struct{},
) {
	campaignLog := log.CampaignId(campaign.Id)
	timer := time.NewTimer(0)
	inActiveTime := campaign.ScheduleSettings.InActiveTime()
	isActive := campaign.IsActive
	alert := false
	noTasks := false
	switch campaign.Status {
	case types.CAMPAIGN_STATUS_NO_TASKS:
		noTasks = true
	case types.CAMPAIGN_STATUS_PAUSED_BY_ALERT:
		alert = true
	}
	status := DefineCampaignStatus(campaign.Status, campaign.IsTest, alert, inActiveTime, noTasks)
	var statusStep CampaignChangeStatusStep = Approved
	// линтер ругается что эти переменные не используются и тд, но cancelTasks вызывается в defer,
	// а потом вместе с контекстом перезаписываются при очередном SendCalls
	//nolint:all
	ctxTasks, cancelTasks := context.WithCancel(ctx)
	noTasksCh := make(chan bool, 1)
	var statusDoneCh chan struct{}
	var doneSendCh <-chan DoneSendCalls
	var sendDataCh chan CampaignSetDataEvent
	campaignLog.Info("start campaign main loop")

	go func() {
		defer func() {
			campaignLog.Info("stop campaign main loop")
			cancelTasks()
			done := false
			dataOk := true
			activeOk := true
			alertOk := true
			dataCh := c.DataCh
			activeCh := c.ActiveCh
			alertCh := c.AlertCh
			timerOk := !timer.Stop()
			for dataOk || activeOk || alertOk || timerOk || !done {
				if !done && queueSize < 1 && doneSendCh == nil && statusDoneCh == nil {
					// @todo разобраться может ли тут незакрываться канал
					close(c.DoneCh)
					done = true
				}
				select {
				case v := <-doneSendCh:
					queueSize = v.QueueSize
					callFinishCh = v.CallFinishCh
					close(sendDataCh)
					doneSendCh = nil
				case <-statusDoneCh:
					statusDoneCh = nil
				case <-callFinishCh:
					queueSize--
				case _, dataOk = <-dataCh:
					if dataOk {
						continue
					}
					dataCh = nil
				case _, activeOk = <-activeCh:
					if activeOk {
						continue
					}
					activeCh = nil
				case _, alertOk = <-alertCh:
					if alertOk {
						continue
					}
					alertCh = nil
				case <-timer.C:
					timerOk = false
				}
				campaignLog.Debug("%+v", types.M{
					"dataOk":       dataOk,
					"activeOk":     activeOk,
					"alertOk":      alertOk,
					"timerOk":      timerOk,
					"done":         done,
					"doneSendCh":   doneSendCh,
					"statusDoneCh": statusDoneCh,
					"queueSize":    queueSize,
				})
			}
			syncDB.DecrLoop(campaign.Id)
		}()

		for {
			select {
			case <-ctx.Done():
				campaignLog.Debug("done campaign context")
				cancelTasks()
				return

			case <-statusDoneCh:
				campaignLog.Debug("done change status")
				statusDoneCh = nil
				statusStep = WaitFirst

			case <-c.AlertCh:
				campaignLog.Info("internal alert recieved")
				alert = true

			case isActive = <-c.ActiveCh:
				campaignLog.Info("campaign active event %t", isActive)
				if !isActive {
					cancelTasks()
				}

			case data := <-c.DataCh:
				campaignLog.Debug("CampaignSetDataEvent")
				if alert && data.Campaign.Status != types.CAMPAIGN_STATUS_PAUSED_BY_ALERT && campaign.Status == types.CAMPAIGN_STATUS_PAUSED_BY_ALERT {
					campaignLog.Info("alert resolved")
					alert = false
					statusStep = Approved
				} else if !alert && data.Campaign.Status == types.CAMPAIGN_STATUS_PAUSED_BY_ALERT {
					campaignLog.Info("external alert recieved")
					alert = true
					statusStep = Approved
				} else if statusStep == WaitFirst {
					statusStep = WaitSecond
				} else if statusStep == WaitSecond {
					statusStep = Approved
				}
				campaign = data.Campaign
				multiphone = data.Multiphone
				if sendDataCh != nil {
					select {
					case <-sendDataCh:
					default:
					}
					sendDataCh <- CampaignSetDataEvent{
						Campaign:   campaign,
						Multiphone: multiphone,
					}
				}

			case v := <-doneSendCh:
				campaignLog.Info("done send calls")
				queueSize = v.QueueSize
				callFinishCh = v.CallFinishCh
				select {
				case <-sendDataCh:
				default:
				}
				close(sendDataCh)
				sendDataCh = nil
				doneSendCh = nil

			case <-callFinishCh:
				queueSize--

			case noTasks = <-noTasksCh:

			case <-timer.C:
				timer.Reset(time.Second)
				if !isActive {
					if queueSize < 1 && doneSendCh == nil && statusDoneCh == nil {
						cancelTasks()
						return
					}
					continue
				}
				inActiveTime = campaign.ScheduleSettings.InActiveTime()
				nextStatus := DefineCampaignStatus(status, campaign.IsTest, alert, inActiveTime, noTasks)
				if campaign.Status != nextStatus && statusStep == Approved {
					if statusDoneCh != nil {
						campaignLog.Warn("status change by api is still in progress")
					} else {
						statusDoneCh = make(chan struct{}, 1)
						go func(campaignId types.CampaignId, status types.CampaignStatus) {
							err := syncDB.DataStorage.SetCampaignStatus(campaignId, status)
							if err != nil {
								campaignLog.TraceError(err)
							}
							close(statusDoneCh)
						}(campaign.Id, nextStatus)
					}
				}
				if nextStatus != types.CAMPAIGN_STATUS_ACTIVE && nextStatus != types.CAMPAIGN_STATUS_NO_TASKS {
					cancelTasks()
				} else if doneSendCh == nil {
					campaignLog.Info("restart send calls")
					cancelTasks()
					ctxTasks, cancelTasks = context.WithCancel(ctx)
					syncDB.IncSendTasks(campaign.Id)
					sendDataCh, doneSendCh = c.SendCalls(ctxTasks, queueSize, campaign, multiphone, syncDB, callFinishCh, noTasksCh)
					callFinishCh = nil
				}
				if status != nextStatus {
					campaignLog.Info("change status '%s' -> '%s'", status, nextStatus)
					status = nextStatus
				}
			}
		}
	}()
}

type CallTaskStateData struct {
	NextCallAt     time.Time
	NextPhoneIndex int
	LastCallId     types.CallId
	LastTrunk      types.TrunkContext
	LastCallerId   string
	CallStates     map[types.CallId]CallState
}

type CallTaskState struct {
	Campaign     types.Campaign
	CallTask     CallTask
	Data         CallTaskStateData
	CallFinishCh chan struct{}      `json:"-"`
	EventCh      chan interface{}   `json:"-"`
	Log          types.ITypedLogger `json:"-"`
}

func NewCallTaskState(campaign types.Campaign, task types.CallTask) CallTaskState {
	callTaskState := CallTaskState{
		Campaign: campaign,
		Data: CallTaskStateData{
			CallStates:     map[types.CallId]CallState{},
			NextCallAt:     task.NextCallAt,
			NextPhoneIndex: task.NextPhoneIndex,
			LastCallId:     task.LastCallId,
			LastTrunk:      task.LastTrunk,
			LastCallerId:   task.LastCallerId,
		},
		CallTask: CallTask{
			Id:         task.Id,
			RegistryId: task.RegistryId,
			CampaignId: task.CampaignId,
			ExternalId: task.ExternalId,
			Phones:     task.Phones,
			IsTest:     task.IsTest,
			EndDate:    task.EndDate,
			CreatedAt:  task.CreatedAt,
			CallData:   task.FullCallData,
		},
		EventCh: make(chan interface{}, 64),
		Log:     log.CampaignId(campaign.Id).TaskId(task.Id).CallId(task.LastCallId),
	}
	if task.NextCallAt.Unix() < 1 {
		callTaskState.Data.NextCallAt = time.Now()
	}
	for _, callResult := range task.CallResults {
		callResult := callResult
		callTaskState.Data.CallStates[callResult.CallId] = CallState{
			Phone: types.TaskPhone{
				Phone:    callResult.Phone,
				Timezone: callResult.Tz,
				Region:   callResult.CallData.Region,
			},
			CallerId: callResult.CallerId,
			Trunk:    types.TrunkContextFromString(callResult.Trunk),
			States:   []types.CallerCallStatus{},
			Attempt:  &callResult,
		}
	}
	// Звонки без ответа
	if _, ok := callTaskState.Data.CallStates[task.LastCallId]; !ok && task.LastCallId != "" {
		callState := CallState{
			CallerId: task.LastCallerId,
			Trunk:    task.LastTrunk,
			States:   []types.CallerCallStatus{},
			Attempt:  nil,
		}
		if len(task.Phones) > task.NextPhoneIndex {
			callState.Phone = task.Phones[task.NextPhoneIndex]
		}
		callTaskState.Data.CallStates[task.LastCallId] = callState
	}
	if callTaskState.CallTask.CallData == nil {
		callTaskState.CallTask.CallData = types.M{}
	}
	if callState := callTaskState.Data.CallStates[task.LastCallId]; task.LastCallId != "" &&
		callState.Attempt != nil {
		for _, field := range callState.Attempt.ContextData.Fields {
			if field.Name == "previous_call_end_state" && field.Value != nil {
				callTaskState.CallTask.CallData["previous_call_end_state"] = field.Value
			}
		}
	}
	return callTaskState
}

func (c CallTaskState) Validate() error {
	if !c.CallTask.EndDate.IsZero() &&
		c.CallTask.EndDate.Unix() != 0 &&
		!time.Now().Before(c.CallTask.EndDate) {
		return errors.New("task end date reached")
	}
	if len(c.CallTask.Phones) == 0 {
		return errors.New("no phones in task")
	}
	return nil
}

func (c CallTaskState) InCallingTime() bool {
	now := time.Now()
	userTz := utils.LocationByOffset(c.CallTask.Phones[c.Data.NextPhoneIndex].Timezone)
	campaignTz := utils.LocationByOffset(3)
	userTime := now.In(userTz)
	userSec := userTime.Hour()*3600 + userTime.Minute()*60 + userTime.Second()
	if c.Campaign.ScheduleSettings.LocalUserTime.Start != c.Campaign.ScheduleSettings.LocalUserTime.End &&
		(userSec < c.Campaign.ScheduleSettings.LocalUserTime.Start || userSec >= c.Campaign.ScheduleSettings.LocalUserTime.End) {
		return false
	}

	campaignStart := time.Date(
		userTime.Year(),
		userTime.Month(),
		userTime.Day(),
		0,
		0,
		c.Campaign.ScheduleSettings.CampaignTime.Start,
		0,
		campaignTz,
	).In(userTz)
	campaignEnd := time.Date(
		userTime.Year(),
		userTime.Month(),
		userTime.Day(),
		0,
		0,
		c.Campaign.ScheduleSettings.CampaignTime.End,
		0,
		campaignTz,
	).In(userTz)
	campaignStartSec := campaignStart.Hour()*3600 + campaignStart.Minute()*60 + campaignStart.Second()
	campaignEndSec := campaignEnd.Hour()*3600 + campaignEnd.Minute()*60 + campaignEnd.Second()
	if campaignStartSec < campaignEndSec && (userSec < campaignStartSec || userSec >= campaignEndSec) {
		return false
	}
	if campaignStartSec > campaignEndSec && (userSec < campaignStartSec && userSec >= campaignEndSec) {
		return false
	}
	return true
}

func (c CallTaskState) Push(publisher types.RmqPublisher, queue string) error {
	body, err := json.Marshal(c)
	if err != nil {
		return err
	}
	body, err = utils.GzipData(body)
	if err != nil {
		return err
	}
	headers := rabbitmq.Table{
		"task": types.RMQ_TASK_CALL_STARTED.String(),
	}
	return publisher.Publish(
		body,
		[]string{queue},
		rabbitmq.WithPublishOptionsHeaders(headers),
		rabbitmq.WithPublishOptionsCorrelationID(uuid.New().String()),
		rabbitmq.WithPublishOptionsMandatory,
	)
}

func (c CallTaskState) SetCallId(callId types.CallId) CallTaskState {
	c.Data.LastCallId = callId
	c.Log = log.CampaignId(c.CallTask.CampaignId).TaskId(c.CallTask.Id).CallId(callId)
	c.Data.CallStates[callId] = CallState{
		Phone: types.TaskPhone{
			Phone:    c.CallTask.Phones[c.Data.NextPhoneIndex].Phone,
			Timezone: c.CallTask.Phones[c.Data.NextPhoneIndex].Timezone,
			Region:   c.CallTask.Phones[c.Data.NextPhoneIndex].Region,
		},
		CallerId: c.Data.LastCallerId,
		Trunk:    c.Data.LastTrunk,
		States:   []types.CallerCallStatus{},
	}
	return c
}

type TaskDataStorage interface {
	GetCallTaskById(ctx context.Context, id types.CallTaskId) (callTask types.ExtendedCallTask, err error)
	GetCampaignById(ctx context.Context, id types.CampaignId) (campaign types.Campaign, err error)
	GetAttemptsByTaskId(ctx context.Context, tasksId []types.CallTaskId) (<-chan types.Attempt, error)
}

func LoadCallTaskStateFromDB(
	ctx context.Context,
	taskId types.CallTaskId,
	ds TaskDataStorage,
) (
	callTaskState CallTaskState,
	err error,
) {
	callTask, err := ds.GetCallTaskById(ctx, taskId)
	if err != nil {
		return callTaskState, err
	}
	campaign, errGetCampaign := ds.GetCampaignById(ctx, callTask.CampaignId)
	if errGetCampaign != nil {
		return callTaskState, errGetCampaign
	}
	callResultsCh, err := ds.GetAttemptsByTaskId(ctx, []types.CallTaskId{callTask.Id})
	if err != nil {
		return callTaskState, err
	}
	for callResult := range callResultsCh {
		callTask.CallResults = append(callTask.CallResults, callResult)
	}
	callTaskState = NewCallTaskState(campaign, callTask.CallTask)
	return callTaskState, err
}

type CallTask struct {
	Id         types.CallTaskId
	RegistryId types.RegistryId
	CampaignId types.CampaignId
	ExternalId string
	Phones     []types.TaskPhone
	IsTest     bool
	EndDate    time.Time
	CreatedAt  time.Time
	CallData   types.M
}

type SyncDBDataStorage interface {
	SetCampaignStatus(id types.CampaignId, status types.CampaignStatus) error
	CreateRecord(ctx context.Context, callRecord types.CallResult) error
	SetIndexes(ctx context.Context, requiredIndexes []mongodb.Idx, collectionName string) error
	GetCallTaskById(ctx context.Context, id types.CallTaskId) (callTask types.ExtendedCallTask, err error)
	GetCampaignById(ctx context.Context, id types.CampaignId) (campaign types.Campaign, err error)
	GetActiveCampaigns(ctx context.Context) (<-chan types.Campaign, error)
	UpdateTask(ctx context.Context, taskId types.CallTaskId, update types.M) (int, error)
	GetMultiphonesId(ctx context.Context, ids []types.CallerMultiphoneId) (multiphonesId map[types.CallerMultiphoneId]types.CallerMultiphone, err error)
	GetAttemptsByTaskId(ctx context.Context, tasksId []types.CallTaskId) (<-chan types.Attempt, error)
	GetCampaignActualTasks(ctx context.Context, campaign types.Campaign, t time.Time) <-chan types.CallTask
	HasActualTasks(ctx context.Context, campaignId types.CampaignId, isTest bool) (bool, error)
	GetCleanEveryDayCampaignsId(ctx context.Context) (result []types.CampaignId, err error)
	StopCampaignTasks(ctx context.Context, campaignId []types.CampaignId) error
	GetProgressTasks(ctx context.Context) (<-chan types.CallTask, error)
}

type TrunksState interface {
	Incr(tc types.TrunkContext)
	Decr(tc types.TrunkContext)
	ChooseContext(tc []types.TrunkContext) *types.TrunkContext
	SetContexts(tc map[types.TrunkContext]struct{})
	Info() (trunksInfo map[string]string, serversInfo map[string]string)
}

type CampaignRecoverData struct {
	Campaign     types.Campaign
	QueueSize    int
	CallFinishCh chan struct{}
}

type SyncDB struct {
	TrunkManager      TrunksState
	Publisher         types.RmqPublisher
	CampaignStates    map[types.CampaignId]CampaignState
	RecieveCallCh     chan<- interface{}
	DialerMonitorChIn chan<- interface{}
	AlertsMonitorChIn chan<- interface{}
	DataStorage       SyncDBDataStorage
	DurationToClean   func() time.Duration
	RndGen            utils.RandomGenerator
	ReplyToGen        utils.SyncRoundRobin
	// Поиск бага дублирования задач
	DoubleLoopMonitoring      map[types.CampaignId]int
	DLMmutex                  sync.Mutex
	DoubleSendTasksMonitoring map[types.CampaignId]int
	DSTMmutex                 sync.Mutex
}

func NewSyncDB(
	ctx context.Context,
	publisher types.RmqPublisher,
	trunkManager TrunksState,
	dataStorage SyncDBDataStorage,
	recieveCallCh chan<- interface{},
	dialerMonitorChanIn chan<- interface{},
	alertsMonitorChanIn chan<- interface{},
) *SyncDB {
	syncDB := SyncDB{
		TrunkManager:      trunkManager,
		Publisher:         publisher,
		CampaignStates:    map[types.CampaignId]CampaignState{},
		DataStorage:       dataStorage,
		RecieveCallCh:     recieveCallCh,
		DialerMonitorChIn: dialerMonitorChanIn,
		AlertsMonitorChIn: alertsMonitorChanIn,
		DurationToClean: func() time.Duration {
			h, m, s := time.Now().In(utils.LocationByOffset(3)).Clock()
			return time.Hour*24 - time.Second*time.Duration(h*3600+m*60+s)
		},
		RndGen:                    utils.NewRandomGenerator(ctx),
		ReplyToGen:                utils.NewSyncRoundRobin(ctx, config.Cfg.DialerConsumeConcurrency),
		DoubleLoopMonitoring:      make(map[types.CampaignId]int, 200),
		DLMmutex:                  sync.Mutex{},
		DoubleSendTasksMonitoring: make(map[types.CampaignId]int, 200),
		DSTMmutex:                 sync.Mutex{},
	}
	return &syncDB
}

func (s *SyncDB) IncLoop(id types.CampaignId) {
	s.DLMmutex.Lock()
	defer s.DLMmutex.Unlock()

	loopCount, ok := s.DoubleLoopMonitoring[id]
	if !ok || loopCount == 0 {
		s.DoubleLoopMonitoring[id] = 1
	} else {
		// тут этого кейса не должно быть, но на всякий случай проверяем
		log.Error("DoubleLoopMonitoring campaignId: %s, already started Loops: %d", id, loopCount)
		loopCount++
		s.DoubleLoopMonitoring[id] = loopCount
	}
}

func (s *SyncDB) DecrLoop(id types.CampaignId) {
	s.DLMmutex.Lock()
	defer s.DLMmutex.Unlock()

	loopCount, ok := s.DoubleLoopMonitoring[id]
	if !ok || loopCount == 0 {
		log.Error("DoubleLoopMonitoring loopCount invalid, error with monitoring, campaignId: %s, loopCount: %d, ok: %t", id, loopCount, ok)
	} else {
		loopCount--
		s.DoubleLoopMonitoring[id] = loopCount
	}
}

func (s *SyncDB) IncSendTasks(id types.CampaignId) {
	s.DSTMmutex.Lock()
	defer s.DSTMmutex.Unlock()

	loopCount, ok := s.DoubleSendTasksMonitoring[id]
	if !ok || loopCount == 0 {
		s.DoubleSendTasksMonitoring[id] = 1
	} else {
		// тут этого кейса не должно быть, но на всякий случай проверяем
		log.Error("DoubleSendTasksMonitoring campaignId: %s, already started SendTasks: %d", id, loopCount)
		loopCount++
		s.DoubleSendTasksMonitoring[id] = loopCount
	}
}

func (s *SyncDB) DecrSendTasks(id types.CampaignId) {
	s.DSTMmutex.Lock()
	defer s.DSTMmutex.Unlock()

	loopCount, ok := s.DoubleSendTasksMonitoring[id]
	if !ok {
		log.Error("DoubleSendTasksMonitoring loopCount invalid, error with monitoring, campaignId: %s, loopCount: %d, ok: %t", id, loopCount, ok)
	} else {
		loopCount--
		s.DoubleSendTasksMonitoring[id] = loopCount
	}
}

func (s *SyncDB) Recover(ctx context.Context) error {
	campaigns := map[types.CampaignId]CampaignRecoverData{}
	multiphonesId := []types.CallerMultiphoneId{}
	trunkContexts := map[types.TrunkContext]struct{}{}
	usedTrunks := []types.TrunkContext{}
	tasksCh, err := s.DataStorage.GetProgressTasks(ctx)
	if err != nil {
		return err
	}
	for callTask := range tasksCh {
		campaignRecoverData, ok := campaigns[callTask.CampaignId]
		if !ok {
			var campaign types.Campaign
			campaign, err = s.DataStorage.GetCampaignById(ctx, callTask.CampaignId)
			if err != nil {
				return err
			}
			campaignRecoverData = CampaignRecoverData{
				Campaign:     campaign,
				CallFinishCh: make(chan struct{}, 1),
			}
			if campaign.TrunkSettings.MultiphoneId != "" {
				multiphonesId = append(multiphonesId, campaign.TrunkSettings.MultiphoneId)
			}
			for _, trunkContext := range campaign.TrunkSettings.Context {
				trunkContexts[trunkContext] = struct{}{}
			}
		}
		campaignRecoverData.QueueSize++
		usedTrunks = append(usedTrunks, callTask.LastTrunk)
		callTaskState := NewCallTaskState(campaignRecoverData.Campaign, callTask)
		callTaskState.CallFinishCh = campaignRecoverData.CallFinishCh
		s.RecieveCallCh <- callTaskState
		campaigns[callTask.CampaignId] = campaignRecoverData
	}
	multiphones, err := s.DataStorage.GetMultiphonesId(ctx, multiphonesId)
	if err != nil {
		return err
	}
	s.TrunkManager.SetContexts(trunkContexts)
	for _, trunk := range usedTrunks {
		s.TrunkManager.Incr(trunk)
	}
	for k, v := range campaigns {
		campaignLog := log.CampaignId(k)
		campaignState := NewCampaignState(v.Campaign.CallDirection, v.Campaign.CallerSettings.TaskPriorityType)

		s.DialerMonitorChIn <- DialerLoadMonitorCampaignLines{Id: v.Campaign.Id, Lines: v.Campaign.Lines}
		s.AlertsMonitorChIn <- AlertsMonitorCampaignStarted{Campaign: v.Campaign, AlertCh: campaignState.AlertCh}

		if v.Campaign.CallDirection == types.CAMPAIGN_CALL_DIRECTION_OUT {
			// внутри проверка дублей и логи
			s.IncLoop(k)
			// запуск
			campaignState.Loop(ctx, v.Campaign, multiphones[v.Campaign.TrunkSettings.MultiphoneId], s, v.QueueSize, v.CallFinishCh)
		}

		s.CampaignStates[k] = campaignState

		campaignLog.Info("campaign added to pool")
		campaignLog.Info("recover %d sended tasks", v.QueueSize)
	}
	return nil
}

func (s *SyncDB) SetIndexes(ctx context.Context) error {
	if err := s.DataStorage.SetIndexes(
		ctx,
		[]mongodb.Idx{
			{
				{Key: "status", Order: 1},
				{Key: "isActive", Order: 1},
			},
		},
		config.Cfg.MongoCampaignCollection,
	); err != nil {
		return err
	}
	if err := s.DataStorage.SetIndexes(
		ctx,
		[]mongodb.Idx{
			{
				{Key: "taskSettings.cleanEveryDay", Order: 1},
				{Key: "callDirection", Order: 1},
			},
		},
		config.Cfg.MongoCampaignCollection,
	); err != nil {
		return err
	}

	// сортировка тасок по умолчанию
	if err := s.DataStorage.SetIndexes(
		ctx,
		[]mongodb.Idx{
			{
				{Key: "campaignId", Order: 1},
				{Key: "status", Order: 1},
				{Key: "isTest", Order: 1},
				{Key: "priority", Order: -1},
				{Key: "nextTz", Order: -1},
				{Key: "nextCallAt", Order: 1},
				{Key: "_id", Order: 1},
				{Key: "lastCallStatus", Order: 1},
				{Key: "startDate", Order: 1},
			},
		},
		config.Cfg.MongoCallTaskCollection,
	); err != nil {
		return err
	}

	// кастомная сортировка тасок
	for name, sortRules := range types.CallTasksSortMap {
		if name == types.CampaignSortType_default {
			continue
		}
		index := mongodb.Idx{
			// ключи стандартного фильтра по точному значению
			{Key: "campaignId", Order: 1},
			{Key: "status", Order: 1},
			{Key: "isTest", Order: 1},
		}
		for _, rule := range sortRules {
			index = append(index, mongodb.IdxKey{Key: rule.Key, Order: rule.Value.(int)})
		}
		// ключи индекса для полного покрытия полей запроса
		index = append(index,
			mongodb.IdxKey{Key: "startDate", Order: 1},
		)
		if err := s.DataStorage.SetIndexes(
			ctx,
			[]mongodb.Idx{index},
			config.Cfg.MongoCallTaskCollection,
		); err != nil {
			return err
		}
	}

	if err := s.DataStorage.SetIndexes(
		ctx,
		[]mongodb.Idx{
			{
				{Key: "callTaskId", Order: 1},
				{Key: "end_ts", Order: 1},
			},
		},
		config.Cfg.MongoCallResultCollection,
	); err != nil {
		return err
	}
	return nil
}

func (s *SyncDB) Start(ctx context.Context, tick time.Duration) {
	doneCleanCh := make(chan struct{}, 1)
	defer func() {
		<-doneCleanCh
	}()
	go func() {
		defer close(doneCleanCh)
		s.CleanEveryDay(ctx)
	}()
	timer := time.NewTimer(0)
	log.Info("start SyncDB")
	for {
		select {
		case <-ctx.Done():
			return

		case <-timer.C:
			timer.Reset(tick)
			log.Debug("sync campaigns")
			if err := s.SyncCampaigns(ctx); err != nil {
				log.TraceError(err)
			}
		}
	}
}

func (s *SyncDB) CleanEveryDay(ctx context.Context) {
	timer := time.NewTimer(s.DurationToClean())
	for {
		select {
		case <-ctx.Done():
			return

		case <-timer.C:
			timer.Reset(time.Hour * 24)
			ids, err := s.DataStorage.GetCleanEveryDayCampaignsId(ctx)
			if err != nil {
				log.TraceError(err)
				continue
			}
			err = s.DataStorage.StopCampaignTasks(ctx, ids)
			if err != nil {
				log.TraceError(err)
			}
		}
	}
}

func (s *SyncDB) SyncCampaigns(ctx context.Context) error {
	campaignCh, err := s.DataStorage.GetActiveCampaigns(ctx)
	if err != nil {
		return err
	}

	loadedCampaigns := make(map[types.CampaignId]types.Campaign, len(s.CampaignStates))
	multiphonesId := make([]types.CallerMultiphoneId, 0, len(s.CampaignStates))
	trunkContexts := map[types.TrunkContext]struct{}{}
	for campaign := range campaignCh {
		loadedCampaigns[campaign.Id] = campaign
		if campaign.TrunkSettings.MultiphoneId != "" {
			multiphonesId = append(multiphonesId, campaign.TrunkSettings.MultiphoneId)
		}
		for _, trunkContext := range campaign.TrunkSettings.Context {
			trunkContexts[trunkContext] = struct{}{}
		}
	}
	s.TrunkManager.SetContexts(trunkContexts)

	multiphones, err := s.DataStorage.GetMultiphonesId(ctx, multiphonesId)
	if err != nil {
		return err
	}

	// Удаление более не активных кампаний
	for campaignId, campaignState := range s.CampaignStates {
		log := log.CampaignId(campaignId)
		select {
		// @todo может тут чего-то не хватает для корректного завершения? возможно нужна доп проверка активности перед удалением
		case <-campaignState.DoneCh:
			log.Info("campaign delete from pool")
			delete(s.CampaignStates, campaignId)
			close(campaignState.DataCh)
			close(campaignState.ActiveCh)
			s.AlertsMonitorChIn <- AlertsMonitorCampaignStopped{Id: campaignId}
			continue

		default:
		}

		// Если кампания присутствует в списке активных, то пропускаем
		_, ok := loadedCampaigns[campaignId]
		if ok {
			continue
		}
		// все остальные удаляем

		// Для входящих
		if campaignState.Direction == types.CAMPAIGN_CALL_DIRECTION_IN {
			log.Info("campaign delete from pool")
			delete(s.CampaignStates, campaignId)
			s.AlertsMonitorChIn <- AlertsMonitorCampaignStopped{Id: campaignId}
			continue
		}

		// вторая часть удаления исходящих
		// @todo разобраться с этим, точно ли этого достаточно для завершения
		if campaignState.IsActive {
			campaignState.IsActive = false
			campaignState.ActiveCh <- false
			s.CampaignStates[campaignId] = campaignState
			continue
		}

		log.Info("campaign already stopping")
	}

	for campaignId, campaign := range loadedCampaigns {
		log := log.CampaignId(campaignId)
		s.DialerMonitorChIn <- DialerLoadMonitorCampaignLines{Id: campaignId, Lines: campaign.Lines}

		// Входящие
		if campaign.CallDirection == types.CAMPAIGN_CALL_DIRECTION_IN {
			campaignState, ok := s.CampaignStates[campaignId]
			if !ok {
				s.CampaignStates[campaignId] = NewCampaignState(types.CAMPAIGN_CALL_DIRECTION_IN, "")
				log.Info("incoming campaign added to pool")
			} else if !campaignState.IsActive {
				campaignState.IsActive = true
				s.CampaignStates[campaignId] = campaignState
			}
			continue
		}

		// Исходящие
		multiphone := multiphones[campaign.TrunkSettings.MultiphoneId]
		campaignState, ok := s.CampaignStates[campaignId]
		// Добавление кампаний
		if !ok {
			campaignState = NewCampaignState(types.CAMPAIGN_CALL_DIRECTION_OUT, campaign.CallerSettings.TaskPriorityType)
			s.AlertsMonitorChIn <- AlertsMonitorCampaignStarted{Campaign: campaign, AlertCh: campaignState.AlertCh}

			// внутри проверка дублей и логи
			s.IncLoop(campaignId)
			// запуск
			campaignState.Loop(ctx, campaign, multiphone, s, 0, make(chan struct{}, 1))

			s.CampaignStates[campaignId] = campaignState
			log.Info("campaign added to pool")
			continue
		}

		// Обновление кампании
		if campaignState.IsActive {
			// Если изменился тип сортировки тасок, то останавливаем обработку, в следующей итерации она перезапустится
			if campaignState.TaskPriority != campaign.CallerSettings.TaskPriorityType {
				campaignState.IsActive = false
				campaignState.ActiveCh <- false
				s.CampaignStates[campaignId] = campaignState
			}
			// @todo multiphone просто выкидывается в функции Loop
			campaignState.DataCh <- CampaignSetDataEvent{Campaign: campaign, Multiphone: multiphone}
		}
	}
	return nil
}

func DefineCampaignStatus(currentStatus types.CampaignStatus, isTest, alert, inActiveTime, noTasks bool) types.CampaignStatus {
	switch {
	case !isTest && alert:
		return types.CAMPAIGN_STATUS_PAUSED_BY_ALERT

	case !isTest && !inActiveTime && currentStatus != types.CAMPAIGN_STATUS_PAUSED_BY_ALERT:
		return types.CAMPAIGN_STATUS_PAUSED_BY_TIME

	case noTasks:
		return types.CAMPAIGN_STATUS_NO_TASKS

	default:
		return types.CAMPAIGN_STATUS_ACTIVE
	}
}
