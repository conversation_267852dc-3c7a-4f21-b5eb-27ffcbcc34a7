package outdoing

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"time"

	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

type RecieveCallEventsDataStorage interface {
	UpdateTask(ctx context.Context, taskId types.CallTaskId, update types.M) (int, error)
	CreateRecord(ctx context.Context, callRecord types.CallResult) error
	GetCallTaskById(ctx context.Context, id types.CallTaskId) (callTask types.ExtendedCallTask, err error)
	GetCampaignById(ctx context.Context, id types.CampaignId) (campaign types.Campaign, err error)
	GetAttemptsByTaskId(ctx context.Context, tasksId []types.CallTaskId) (<-chan types.Attempt, error)
}

var ErrUnexpectedCallResult = fmt.Errorf("unexpected call result")

func StartRecieveCallEvents(
	ctx context.Context,
	callResultsCh <-chan interface{},
	monitorCh chan<- interface{},
	alertsCh chan<- interface{},
	ds RecieveCallEventsDataStorage,
	ts TrunksState,
	publisher types.RmqPublisher,
) error {
	deleteCh := make(chan types.CallId)
	calls := map[types.CallId]CallTaskState{}
	defer func() {
		for len(calls) > 0 {
			delete(calls, <-deleteCh)
		}
	}()
	log.Info("start recieve call events")
	for {
		select {
		case <-ctx.Done():
			return nil
		case id := <-deleteCh:
			delete(calls, id)
		case e := <-callResultsCh:
			switch e := e.(type) {
			case CallTaskState:
				e.Log.Debug("CallTaskState")
				calls[e.Data.LastCallId] = e
				monitorCh <- DialerLoadMonitorTaskStarted{CampaignId: e.Campaign.Id, CallId: e.Data.LastCallId}
				alertsCh <- AlertsMonitorCallStarted{Campaign: e.Campaign}
				go func() {
					e = e.Process(ctx, monitorCh, alertsCh, ds, ts, func(postResult types.PostResult) error {
						return postResult.Push(publisher)
					})
					deleteCh <- e.Data.LastCallId
				}()
			case CallStatusData:
				log := log.CampaignId(e.DialerStatus.CampaignId).
					TaskId(e.DialerStatus.TaskId).
					CallId(e.DialerStatus.CallId)
				log.Debug("CallStatusData")
				if call, ok := calls[e.DialerStatus.CallId]; !ok {
					log.Warn("not found in cache")
					e.ResponseCh <- rabbitmq.NackDiscard
				} else if cap(call.EventCh) == len(call.EventCh) {
					log.Error("call event buffer overflow")
					e.ResponseCh <- rabbitmq.NackDiscard
				} else {
					call.EventCh <- e
				}
			case CallResultData:
				log := log.CampaignId(e.DialerResult.CampaignId).
					TaskId(e.DialerResult.CallTaskId).
					CallId(e.DialerResult.Id)
				log.Debug("CallResultData")
				if call, ok := calls[e.DialerResult.Id]; !ok {
					log.Warn("not found in cache")
					e.ResponseCh <- rabbitmq.NackDiscard
				} else if cap(call.EventCh) == len(call.EventCh) {
					log.Error("call event buffer overflow")
					e.ResponseCh <- rabbitmq.NackDiscard
				} else {
					call.EventCh <- e
				}
			default:
				return fmt.Errorf("StartRecieveCallEvents: call result %T is unexpected -> %w", e, ErrUnexpectedCallResult)
			}
		}
	}
}

type WrappedPostResult struct {
	Data struct {
		Report types.PostResult `json:"report"`
	} `json:"data"`
}

func ConsumeHandler(reieveCallCh chan<- interface{}) rabbitmq.Handler {
	return func(d rabbitmq.Delivery) rabbitmq.Action {
		raw, err := utils.UnzipData(d.Body)
		if err != nil {
			log.TraceError(err)
			return rabbitmq.NackDiscard
		}
		switch d.Headers["task"] {
		case types.RMQ_TASK_CALL_RESULT.String():
			res := types.DialerResponseAny{}
			err = json.Unmarshal(raw, &res)
			if err != nil {
				log.TraceError(err)
				return rabbitmq.NackDiscard
			}

			switch res.Status {
			case types.CELERY_STATUS_COMPLETE.String():
				var dialerResult types.DialerResult
				err = json.Unmarshal(res.Data, &dialerResult)
				if err != nil {
					log.TraceError(err)
					return rabbitmq.NackDiscard
				}
				callResultData := CallResultData{
					DialerResult: dialerResult,
					ResponseCh:   make(chan rabbitmq.Action, 1),
				}
				reieveCallCh <- callResultData
				return <-callResultData.ResponseCh

			case types.CELERY_STATUS_PROGRESS.String():
				var dialerStatusChange types.DialerStatus
				err = json.Unmarshal(res.Data, &dialerStatusChange)
				if err != nil {
					log.TraceError(err)
					return rabbitmq.NackDiscard
				}
				callStatusData := CallStatusData{
					DialerStatus: dialerStatusChange,
					ResponseCh:   make(chan rabbitmq.Action, 1),
				}
				reieveCallCh <- callStatusData
				return <-callStatusData.ResponseCh

			default:
				log.Error("get unknown data - %+v", res)
				return rabbitmq.NackDiscard
			}

		// только входящие?
		case types.RMQ_TASK_CALL_STARTED.String():
			callTaskState := CallTaskState{}
			err := json.Unmarshal(raw, &callTaskState)
			if err != nil {
				log.TraceError(err)
				return rabbitmq.NackDiscard
			}
			callTaskState.Data.CallStates = make(map[types.CallId]CallState)
			callTaskState.EventCh = make(chan interface{}, 64)
			callTaskState = callTaskState.SetCallId(callTaskState.Data.LastCallId)
			reieveCallCh <- callTaskState
			return rabbitmq.Ack

		default:
			log.Error("get unknown data %q", d.Headers["task"])
			return rabbitmq.NackDiscard
		}
	}
}

func (c CallTaskState) Process(
	ctx context.Context,
	monitorCh chan<- interface{},
	alertCh chan<- interface{},
	ds RecieveCallEventsDataStorage,
	ts TrunksState,
	sendPostResult func(postResult types.PostResult) error,
) CallTaskState {
	ctxProcess, cancelProcess := context.WithTimeout(ctx, time.Duration(
		c.Campaign.TrunkSettings.CallTTL+c.Campaign.TrunkSettings.Timeout+600,
	)*time.Second)
	defer func() {
		c.Log.Debug("stop call loop")
		if c.CallFinishCh != nil {
			c.CallFinishCh <- struct{}{}
			ts.Decr(c.Data.LastTrunk)
		}

		monitorCh <- DialerLoadMonitorTaskStopped{
			CampaignId: c.Campaign.Id,
			CallId:     c.Data.LastCallId,
		}
		cancelProcess()
	}()
	c.Log.Debug("start call loop")
	for {
		select {
		case <-ctxProcess.Done():
			callResult := c.WriteTimeout(ctx, ds)
			alertCh <- AlertsMonitorCallResult{
				Status:   callResult.Status,
				Campaign: c.Campaign,
			}
			return c

		case e := <-c.EventCh:
			switch e := e.(type) {
			case CallStatusData:
				monitorCh <- DialerLoadMonitorTaskCallStatus{
					CallId:     c.Data.LastCallId,
					Status:     e.DialerStatus.Status,
					DialerId:   e.DialerStatus.DialerId,
					CampaignId: c.Campaign.Id,
				}
				var action rabbitmq.Action
				c, action = c.ProcessDialerStatus(e.DialerStatus.Status)
				e.ResponseCh <- action

			case CallResultData:
				var action rabbitmq.Action
				c, action = c.ProcessDialerResult(ctx, e.DialerResult, alertCh, ds, sendPostResult)
				e.ResponseCh <- action
				return c

			default:
				c.Log.Panic("unexpected type %e", e)
			}
		}
	}
}

func (c CallTaskState) WriteTimeout(ctx context.Context, ds RecieveCallEventsDataStorage) types.CallResult {
	c.Log.Warn("Task stopped by reason: caller timeout")
	callResult := c.CreateCallResultTimeout()
	err := ds.CreateRecord(ctx, callResult)
	if err != nil {
		c.Log.TraceError(err)
	}

	// task 6292
	now := time.Now()
	updateMap := types.M{
		"status":     types.TASK_STATUS_STOP,
		"lastCallAt": now,
	}
	if callResult.IsFirst {
		updateMap["firstCallAt"] = now
	}

	_, err = ds.UpdateTask(
		ctx,
		c.CallTask.Id,
		types.M{"$set": updateMap},
	)
	if err != nil {
		c.Log.TraceError(err)
	}
	return callResult
}

func (c CallTaskState) ProcessDialerStatus(dialerStatus types.CallerCallStatus) (CallTaskState, rabbitmq.Action) {
	c.Log.Debug("processing dialer status")
	defer c.Log.Debug("processed dialer status")
	callState, ok := c.Data.CallStates[c.Data.LastCallId]
	if !ok {
		c.Log.Warn("callState not found in cache")
		return c, rabbitmq.Ack
	}
	callState.States = append(callState.States, dialerStatus)
	c.Data.CallStates[c.Data.LastCallId] = callState
	return c, rabbitmq.Ack
}

var stopStatuses = map[types.CallStatus]struct{}{
	types.CALL_STATUS_SUCCESS:                  {},
	types.CALL_STATUS_ERROR_AFTER_UP:           {},
	types.CALL_STATUS_CALL_TRANSFER_HANG_UP:    {},
	types.CALL_STATUS_CALL_TRANSFER_NO_PICK_UP: {},
	types.CALL_STATUS_CALL_TRANSFER_SUCCESS:    {},
}

type attemptCounter struct {
	idx                 int
	attempts            int
	attemptsByStatus    int
	attemptsByBotResult int
}

func (c CallTaskState) Recall(recallTime time.Time) (nextIndex int, nextCallAt time.Time, nextStatus types.TaskStatus) {
	nextCallAt = c.Data.NextCallAt
	lastAttempt := c.Data.CallStates[c.Data.LastCallId]
	c.Log.Info(
		"calculate recall for %s direction task by after call result status %s",
		c.Campaign.CallDirection,
		lastAttempt.Attempt.Status,
	)
	if c.Campaign.CallDirection == types.CAMPAIGN_CALL_DIRECTION_IN {
		nextStatus = types.TASK_STATUS_STOP
	} else if _, ok := stopStatuses[lastAttempt.Attempt.Status]; ok {
		nextStatus = types.TASK_STATUS_STOP
	} else if c.Campaign.CallerSettings.SilenceAsDown && lastAttempt.Attempt.Status == types.CALL_STATUS_SILENCE_ON_FIRST_MESSAGE {
		nextStatus = types.TASK_STATUS_STOP
	} else {
		settingsByStatus := c.Campaign.RecallSettings.AttemptsByStatus[lastAttempt.Attempt.Status.String()]
		settingsByBotResult := c.Campaign.RecallSettings.RecallsByCallResult[lastAttempt.Attempt.ResultCall]

		if c.Campaign.CallerSettings.PhonesAttemptCounterType == types.COUNTER_TYPE_BY_ONE {
			phones := make(map[string]attemptCounter, len(c.CallTask.Phones))
			if lastAttempt.Attempt.Status == types.CALL_STATUS_REQUEST_RECALL {
				phones[lastAttempt.Phone.Phone] = attemptCounter{idx: c.Data.NextPhoneIndex}
			} else {
				for i, phone := range c.CallTask.Phones {
					phones[phone.Phone] = attemptCounter{idx: i}
				}
			}
			for _, dialerStatus := range c.Data.CallStates {
				if dialerStatus.Attempt == nil {
					continue
				}
				phoneNumber := dialerStatus.Phone.Phone
				phoneAtt, ok := phones[phoneNumber]
				if !ok {
					continue
				}
				phoneAtt.attempts++
				if phoneAtt.attempts >= c.Campaign.RecallSettings.DefaultAttempts {
					c.Log.Info("attempts limit exceeded by phone %s", phoneNumber)
					delete(phones, phoneNumber)
					continue
				}
				if dialerStatus.Attempt.Status == lastAttempt.Attempt.Status {
					phoneAtt.attemptsByStatus++
				}
				if settingsByStatus.Number > 0 && phoneAtt.attemptsByStatus >= settingsByStatus.Number {
					c.Log.Info("attempts limit exceeded by status and phone %s", phoneNumber)
					delete(phones, phoneNumber)
					continue
				}
				if dialerStatus.Attempt.ResultCall == lastAttempt.Attempt.ResultCall {
					phoneAtt.attemptsByBotResult++
				}
				if settingsByBotResult.Number > 0 && phoneAtt.attemptsByBotResult >= settingsByBotResult.Number {
					c.Log.Info("attempts limit exceeded by bot result and phone %s", phoneNumber)
					delete(phones, phoneNumber)
					continue
				}
				phones[phoneNumber] = phoneAtt
			}
			nextStatus = types.TASK_STATUS_STOP
			point := (c.Data.NextPhoneIndex + 1) % len(c.CallTask.Phones)
			for _, phone := range append(c.CallTask.Phones[point:], c.CallTask.Phones[:point]...) {
				if ctr, ok := phones[phone.Phone]; ok {
					nextIndex = ctr.idx
					nextStatus = types.TASK_STATUS_DELAY
					break
				}
			}
		} else {
			attempts := 0
			attemptsByStatus := 0
			attemptsByBotResult := 0
			nextStatus = types.TASK_STATUS_DELAY
			if lastAttempt.Attempt.Status == types.CALL_STATUS_REQUEST_RECALL {
				nextIndex = c.Data.NextPhoneIndex
			} else {
				nextIndex = (c.Data.NextPhoneIndex + 1) % len(c.CallTask.Phones)
			}
			for _, dialerStatus := range c.Data.CallStates {
				if dialerStatus.Attempt == nil {
					continue
				}
				attempts++
				if attempts >= c.Campaign.RecallSettings.DefaultAttempts {
					c.Log.Info("attempts limit exceeded")
					nextStatus = types.TASK_STATUS_STOP
					break
				}
				if dialerStatus.Attempt.Status == lastAttempt.Attempt.Status {
					attemptsByStatus++
				}
				if settingsByStatus.Number > 0 && attemptsByStatus >= settingsByStatus.Number {
					c.Log.Info("attempts limit by status %s exceeded", lastAttempt.Attempt.Status)
					nextStatus = types.TASK_STATUS_STOP
					break
				}
				if dialerStatus.Attempt.ResultCall == lastAttempt.Attempt.ResultCall {
					attemptsByBotResult++
				}
				if settingsByBotResult.Number > 0 && attemptsByBotResult >= settingsByBotResult.Number {
					c.Log.Info("attempts limit by call result %s exceeded", lastAttempt.Attempt.ResultCall)
					nextStatus = types.TASK_STATUS_STOP
					break
				}
			}
		}
		if nextStatus == types.TASK_STATUS_STOP {
			c.Log.Info("stopping task")
		} else if !recallTime.IsZero() &&
			recallTime.Unix() != 0 &&
			lastAttempt.Attempt.Status == types.CALL_STATUS_REQUEST_RECALL {
			nextCallAt = recallTime
			c.Log.Info("task smart recall with status %s at %s to %s",
				lastAttempt.Attempt.Status,
				nextCallAt,
				c.CallTask.Phones[nextIndex].Phone,
			)
		} else {
			nextCallAt = time.Now().Add(time.Duration(func() int {
				if nextIndex != c.Data.NextPhoneIndex && nextIndex != 0 {
					return 0
				}
				if settingsByStatus.Timeout > 0 {
					return settingsByStatus.Timeout
				}
				if settingsByBotResult.Timeout > 0 {
					return settingsByBotResult.Timeout
				}
				return c.Campaign.RecallSettings.DefaultTimeout
			}()) * time.Second)
			c.Log.Info("task default recall with status %s at %s to %s",
				lastAttempt.Attempt.Status,
				nextCallAt,
				c.CallTask.Phones[nextIndex].Phone,
			)
		}
		if !c.CallTask.EndDate.IsZero() && c.CallTask.EndDate.Unix() != 0 && !nextCallAt.Before(c.CallTask.EndDate) {
			c.Log.Info("stopping task cause next call after end date %s", c.CallTask.EndDate)
			nextStatus = types.TASK_STATUS_STOP
		}
	}
	return nextIndex, nextCallAt, nextStatus
}

func (c CallTaskState) ProcessDialerResult(
	ctx context.Context,
	dialerResult types.DialerResult,
	alertCh chan<- interface{},
	ds RecieveCallEventsDataStorage,
	sendPostResult func(postResult types.PostResult) error,
) (CallTaskState, rabbitmq.Action) {
	c.Log.Debug("processing dialer result")
	defer c.Log.Debug("processed dialer result")
	previousData := c.Data
	attempt := c.CreateAttempt(dialerResult)
	callState, ok := c.Data.CallStates[dialerResult.Id]
	if ok {
		callState.Attempt = &attempt
	} else {
		c.Log.Warn("callState not found in cache")
		callState = CallState{
			Phone:    c.CallTask.Phones[c.Data.NextPhoneIndex],
			CallerId: c.Data.LastCallerId,
			Trunk:    c.Data.LastTrunk,
			States:   []types.CallerCallStatus{},
			Attempt:  &attempt,
		}
	}
	c.Data.CallStates[dialerResult.Id] = callState
	if dialerResult.DataBaseId == "" {
		c.Log.Info("dialer result without database id, create database record")
		ctxCreateRecord, cancelCreateRecord := context.WithTimeout(ctx, time.Minute)
		defer cancelCreateRecord()
		errCreateRecord := ds.CreateRecord(
			ctxCreateRecord,
			c.CreateCallResult(callState, &dialerResult),
		)
		if errCreateRecord != nil {
			c.Log.TraceError(errCreateRecord)
		}
	}
	updateMap := types.M{"lastCallStatus": dialerResult.Status}

	var nextStatus types.TaskStatus
	c.Data.NextPhoneIndex, c.Data.NextCallAt, nextStatus = c.Recall(dialerResult.ParseRecallDatetime())
	updateMap["status"] = nextStatus
	attemptNumber := len(c.Data.CallStates)
	updateMap["attemptNumber"] = attemptNumber
	if nextStatus != types.TASK_STATUS_STOP {
		updateMap["nextCallAt"] = c.Data.NextCallAt
		updateMap["nextPhoneIndex"] = c.Data.NextPhoneIndex
		updateMap["nextTz"] = c.CallTask.Phones[c.Data.NextPhoneIndex].Timezone
	}

	// task 6292
	now := time.Now()
	if attemptNumber == 1 {
		updateMap["firstCallAt"] = now
	}
	if _, ok := stopStatuses[callState.Attempt.Status]; ok {
		updateMap["lastCallAt"] = now
	}
	res, err := ds.UpdateTask(
		ctx,
		c.CallTask.Id,
		types.M{
			"$set": updateMap,
		},
	)
	if err != nil {
		c.Log.TraceError(err)
	} else if res == 0 {
		delete(updateMap, "status")
		_, err = ds.UpdateTask(
			ctx,
			c.CallTask.Id,
			types.M{
				"$set": updateMap,
			},
		)
		if err != nil {
			c.Log.TraceError(err)
		}
	}
	if c.Campaign.CallerSettings.PostResult {
		err = sendPostResult(c.GetPostResult(previousData, nextStatus, dialerResult))
		if err != nil {
			c.Log.TraceError(err)
		} else {
			c.Log.Info("post result sended")
		}
	}
	alertCh <- AlertsMonitorCallResult{
		Status:   dialerResult.Status,
		Campaign: c.Campaign,
	}
	return c, rabbitmq.Ack
}

type CallStatusData struct {
	DialerStatus types.DialerStatus
	ResponseCh   chan rabbitmq.Action
}

type CallResultData struct {
	DialerResult types.DialerResult
	ResponseCh   chan rabbitmq.Action
}

func (c CallTaskState) CreateAttempt(dr types.DialerResult) (attempt types.Attempt) {
	attempt = types.Attempt{
		CallId:     dr.Id,
		Status:     dr.Status,
		ResultCall: dr.BotResult,
		Trunk:      c.Data.LastTrunk.Name(),
		Phone:      c.CallTask.Phones[c.Data.NextPhoneIndex].Phone,
		CallerId:   c.Data.LastCallerId,
		Tz:         c.CallTask.Phones[c.Data.NextPhoneIndex].Timezone,
		CallData: struct {
			Region string `bson:"region"`
		}{
			Region: c.CallTask.Phones[c.Data.NextPhoneIndex].Region,
		},
	}
	if dr.PreviousCallEndState != "" {
		attempt.ContextData.Fields = append(
			attempt.ContextData.Fields,
			types.ContextDataField{
				Name:     "previous_call_end_state",
				Value:    dr.PreviousCallEndState,
				Approved: true,
			},
		)
	}
	if dr.RecallDatetime != nil {
		attempt.ContextData.Fields = append(
			attempt.ContextData.Fields,
			types.ContextDataField{
				Name:     "recall_datetime",
				Value:    dr.RecallDatetime,
				Approved: true,
			},
		)
	}
	return attempt
}

func (c CallTaskState) CreateCallResultTimeout() types.CallResult {
	callResult := c.CreateCallResult(c.Data.CallStates[c.Data.LastCallId], nil)
	callResult.ErrorMsg = "caller timeout"
	callResult.IsLast = true
	return callResult
}

func (c CallTaskState) GetPostResult(
	previousData CallTaskStateData,
	nextStatus types.TaskStatus,
	dialerResult types.DialerResult,
) types.PostResult {
	postResult := types.PostResult{
		NextStatus:     nextStatus,
		AttemptsCount:  len(c.Data.CallStates),
		Phone:          c.CallTask.Phones[previousData.NextPhoneIndex].Phone,
		Tz:             c.CallTask.Phones[previousData.NextPhoneIndex].Timezone,
		ExternalId:     c.CallTask.ExternalId,
		DateCreated:    c.CallTask.CreatedAt.Unix(),
		CampaignId:     c.Campaign.PublicId,
		RegistryId:     c.CallTask.RegistryId,
		DialogueName:   c.Campaign.BuildInfo.Dialogue.Name,
		CallId:         dialerResult.Id,
		Status:         dialerResult.Status,
		DataBaseId:     dialerResult.DataBaseId,
		StartDate:      utils.TsToString(dialerResult.StartDate, types.PostResult_TimeTemplate, time.UTC),
		EndDate:        utils.TsToString(dialerResult.EndDate, types.PostResult_TimeTemplate, time.UTC),
		Duration:       fmt.Sprint(int(math.Ceil(dialerResult.Duration))),
		DurationBefore: fmt.Sprint(int(math.Ceil(dialerResult.DurationBefore))),
		IsTest:         c.CallTask.IsTest,
		Project:        c.Campaign.BuildInfo.Project,
	}
	if dialerResult.Data.ContextData == nil {
		fields := make([]types.ContextDataField, 0, len(c.CallTask.CallData))
		for k, v := range c.CallTask.CallData {
			fields = append(fields, types.ContextDataField{Name: k, Value: v})
		}
		postResult.Service = types.DialerResultData{
			Log:         dialerResult.Data.Log,
			ContextData: types.CallResultContextData{Fields: fields},
		}
	} else {
		postResult.Service = dialerResult.Data
	}
	if nextStatus != types.TASK_STATUS_STOP {
		postResult.NextCallDate = c.Data.NextCallAt.In(time.UTC).Format(types.PostResult_TimeTemplate)
	}
	return postResult
}

var errorStatusesMap = map[struct {
	CallDirection types.CampaignCallDirection
	CallStatus    types.CallStatus
}][2]string{
	{types.CAMPAIGN_CALL_DIRECTION_IN, types.CALL_STATUS_ERROR_BEFORE_UP}:  {"Отсутствие диалога", "Ошибка при соединении"},
	{types.CAMPAIGN_CALL_DIRECTION_IN, types.CALL_STATUS_ERROR_AFTER_UP}:   {"Диалог прерван", "Ошибка во время звонка"},
	{types.CAMPAIGN_CALL_DIRECTION_OUT, types.CALL_STATUS_ERROR_BEFORE_UP}: {"Недозвон", "Ошибка при дозвоне"},
	{types.CAMPAIGN_CALL_DIRECTION_OUT, types.CALL_STATUS_ERROR_AFTER_UP}:  {"Диалог прерван", "Ошибка во время звонка"},
}

func (c CallTaskState) CreateCallResult(
	callState CallState,
	dr *types.DialerResult,
) types.CallResult {
	now := time.Now()
	fields := make([]types.ContextDataField, 0, len(c.CallTask.CallData))
	for k, v := range c.CallTask.CallData {
		fields = append(fields, types.ContextDataField{
			Name:     k,
			Value:    v,
			Approved: true,
		})
	}
	callResult := types.CallResult{
		StartTs:            int(now.UnixMilli()),
		EndTs:              int(now.UnixMilli()),
		CallTaskId:         c.CallTask.Id,
		StartDatabase:      now,
		EndDatabase:        now,
		TaskAddedDatabase:  now,
		DuplicateMessages:  map[string]string{},
		StructuredLogs:     []interface{}{},
		CallWithProblems:   true,
		CampaignId:         c.Campaign.Id,
		RegistryId:         c.CallTask.RegistryId,
		CompanyId:          string(c.Campaign.Id),
		CallerQueueId:      string(c.CallTask.Id),
		IsTest:             c.CallTask.IsTest,
		BuildInfo:          c.Campaign.BuildInfo,
		Direction:          c.Campaign.CallDirection.ToCallDirection(),
		InterruptionStatus: 0,
		Source:             "caller",
		CallData: types.CallResultCallData{
			CallDate:            int32(c.CallTask.CreatedAt.Unix()),
			CallerId:            callState.CallerId,
			Phone:               callState.Phone.Phone,
			Dest:                callState.Trunk.Dest(callState.Phone.Phone),
			Region:              callState.Phone.Region,
			CallerQueueId:       string(c.CallTask.Id),
			EnableSilenceAsDown: c.Campaign.CallerSettings.SilenceAsDown,
			Timeout:             c.Campaign.TrunkSettings.Timeout,
			Trunk:               callState.Trunk.Name(),
			SecondsForSuccess:   c.Campaign.CallerSettings.ShortCallTimeout,
			AttemptNumber:       len(c.Data.CallStates),
			ExternalId:          c.CallTask.ExternalId,
			Phones: func() []string {
				phones := make([]string, 0, len(c.CallTask.Phones))
				for i := range c.CallTask.Phones {
					phones = append(phones, c.CallTask.Phones[i].Phone)
				}
				return phones
			}(),
		},
		Attempt: types.Attempt{
			CallId:      c.Data.LastCallId,
			Phone:       c.CallTask.Phones[c.Data.NextPhoneIndex].Phone,
			Tz:          c.CallTask.Phones[c.Data.NextPhoneIndex].Timezone,
			Trunk:       callState.Trunk.Name(),
			ContextData: types.CallResultContextData{Fields: fields},
		},
	}
	if callState.Attempt != nil {
		callResult.Status = callState.Attempt.Status
		callResult.CallerId = callState.CallerId
	} else {
		callResult.Status = callState.DefineErrorStatus()
	}
	if dr != nil {
		callResult.ErrorMsg = dr.Error
		callResult.DialerId = dr.DialerId
		callResult.BotId = dr.BotId
		callResult.Duration = dr.Duration
		callResult.DurationBefore = dr.DurationBefore
		if dr.AudioFile != "" {
			callResult.AudioFile = dr.AudioFile
			if dr.AudioStorageTime != nil {
				callResult.Lifecycle = int(now.UnixMilli()) +
					(int(*dr.AudioStorageTime) * 31536000000)
			}
		}
	}
	secondaryStatuses := errorStatusesMap[struct {
		CallDirection types.CampaignCallDirection
		CallStatus    types.CallStatus
	}{
		c.Campaign.CallDirection,
		callResult.Status,
	}]
	callResult.CategoryStatus, callResult.ResultCall = secondaryStatuses[0], secondaryStatuses[1]
	callResult.Direction = c.Campaign.CallDirection.ToCallDirection()

	if callResult.CallData.AttemptNumber == 1 {
		callResult.IsFirst = true
	}
	if _, ok := stopStatuses[callResult.Status]; ok {
		callResult.IsLast = true
	}
	return callResult
}

func (d CallState) DefineErrorStatus() types.CallStatus {
	for _, status := range d.States {
		if status != types.CALLER_CALL_STATUS_PREPARING {
			return types.CALL_STATUS_ERROR_AFTER_UP
		}
	}
	return types.CALL_STATUS_ERROR_BEFORE_UP
}

func (d CallTaskStateData) CountCallResultsByStatus(status types.CallStatus) (result int) {
	for _, callState := range d.CallStates {
		if callState.Attempt != nil && callState.Attempt.Status == status {
			result++
		}
	}
	return result
}

func (d CallTaskStateData) CountCallResultsByBotResult(botResult string) (result int) {
	for _, callState := range d.CallStates {
		if callState.Attempt != nil && callState.Attempt.ResultCall == botResult {
			result++
		}
	}
	return result
}
