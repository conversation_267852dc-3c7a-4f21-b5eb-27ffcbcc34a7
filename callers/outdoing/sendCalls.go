package outdoing

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"time"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

type DoneSendCalls struct {
	QueueSize    int
	CallFinishCh chan struct{}
}

func (c CampaignState) SendCalls(
	ctx context.Context,
	queueSize int,
	campaign types.Campaign,
	callerId types.CallerMultiphone,
	syncDB *SyncDB,
	callFinishCh chan struct{},
	noTasksCh chan<- bool,
) (chan CampaignSetDataEvent, <-chan Done<PERSON>end<PERSON>alls) {
	ctx, cancel := context.WithCancel(ctx)
	ctxQuery, cancelQuery := context.WithCancel(ctx)
	campaignLog := log.CampaignId(campaign.Id)
	doneCh := make(chan <PERSON>, 1)
	campaignDataCh := make(chan CampaignSetDataEvent, 1)
	nextCallerId := campaign.CallerIdGen(callerId)
	var nextTaskCh <-chan types.CallTask
	tryNextCh := make(chan struct{}, 1)
	tryNext := func() {
		nextTaskCh = nil
		if len(tryNextCh) == 0 {
			tryNextCh <- struct{}{}
		}
	}
	go func() {
		var trunkP *types.TrunkContext
		var tasksCh <-chan types.CallTask
		defer syncDB.DecrSendTasks(campaign.Id)
		defer func() {
			cancel()
			doneCh <- DoneSendCalls{
				QueueSize:    queueSize,
				CallFinishCh: callFinishCh,
			}
			if trunkP != nil {
				syncDB.TrunkManager.Decr(*trunkP)
			}
			tasksOk := tasksCh != nil
			campaignDataOk := campaignDataCh != nil
			for tasksOk || campaignDataOk {
				select {
				case _, campaignDataOk = <-campaignDataCh:
				case _, tasksOk = <-tasksCh:
				}
			}
		}()

		go func(campaign types.Campaign) {
			hasTasks, err := syncDB.DataStorage.HasActualTasks(ctx, campaign.Id, campaign.IsTest)
			if err != nil {
				if !errors.Is(err, ctx.Err()) {
					campaignLog.TraceError(err)
				}
				cancel()
				return
			}
			noTasksCh <- !hasTasks
			if !hasTasks {
				cancel()
			} else {
				cancelQuery()
			}
		}(campaign)

		campaignLog.Debug("start send calls")
		defer campaignLog.Debug("stop send calls")
		for {
			select {
			case <-ctx.Done():
				return

			case <-callFinishCh:
				queueSize--
				tryNext()

			case dat, ok := <-campaignDataCh:
				if !ok {
					campaignDataCh = nil
					continue
				}
				if campaign.IsTest != dat.Campaign.IsTest {
					campaignLog.Info(
						"change is test flag from %t to %t",
						campaign.IsTest,
						dat.Campaign.IsTest,
					)
					cancelQuery()
				}
				if campaign.ScheduleSettings.LocalUserTime != dat.Campaign.ScheduleSettings.LocalUserTime {
					campaignLog.Info(
						"change local user time from %+v to %+v",
						dat.Campaign.ScheduleSettings.LocalUserTime,
						campaign.ScheduleSettings.LocalUserTime,
					)
					cancelQuery()
				}
				if !callerId.Equal(dat.Multiphone) {
					callerId = dat.Multiphone
					nextCallerId = campaign.CallerIdGen(callerId)
				}
				campaign = dat.Campaign
				tryNext()

			case <-ctxQuery.Done():
				select {
				case <-ctx.Done():
					return
				default:
				}
				for tasksCh != nil {
					if _, ok := <-tasksCh; !ok {
						tasksCh = nil
					}
				}
				now := time.Now()

				// костыль чтобы перезапустить выгрузку тасок с учётом фильтра локального времени
				ctxQuery, cancelQuery = context.WithTimeout(ctx, func() time.Duration {
					nowSec := (now.Minute()*60 + now.Second()) % 3600
					startSec := campaign.ScheduleSettings.LocalUserTime.Start % 3600
					endSec := campaign.ScheduleSettings.LocalUserTime.End % 3600
					if startSec > endSec {
						startSec, endSec = endSec, startSec
					}
					if nowSec < startSec {
						nowSec = startSec - nowSec
					} else if nowSec < endSec {
						nowSec = endSec - nowSec
					} else {
						nowSec = (3600 - nowSec) + startSec
					}
					if nowSec == 0 {
						nowSec = 3600
					}
					return time.Duration(nowSec) * time.Second
				}())

				tasksCh = syncDB.DataStorage.GetCampaignActualTasks(ctxQuery, campaign, now)
				tryNext()

			case <-tryNextCh:
				if tasksCh == nil {
					continue
				}
				if queueSize >= campaign.Lines {
					continue
				}
				if trunkP == nil {
					trunkP = syncDB.TrunkManager.ChooseContext(campaign.TrunkSettings.Context)
				}
				if trunkP == nil {
					select {
					case tryNextCh <- struct{}{}:
					default:
					}

					continue
				}
				nextTaskCh = tasksCh

			case callTask, ok := <-nextTaskCh:
				if !ok {
					tasksCh = nil
					return
				}

				taskLog := log.CampaignId(campaign.Id).TaskId(callTask.Id)
				task := NewCallTaskState(campaign, callTask)

				if err := task.Validate(); err != nil {
					taskLog.Warn("task skipped, validation error: %v", err)
					if _, err := syncDB.DataStorage.UpdateTask(
						ctx,
						task.CallTask.Id,
						types.M{
							"$set": types.M{
								"status": types.TASK_STATUS_STOP,
							},
						},
					); err != nil {
						if !errors.Is(err, ctx.Err()) {
							taskLog.TraceError(err)
						}
						return
					}
					tryNext()
					continue
				}
				task.CallFinishCh = callFinishCh
				trunk := *trunkP
				callerId := nextCallerId()
				phone := task.CallTask.Phones[task.Data.NextPhoneIndex]
				dialerTask, err := task.ParseDialerTask(campaign, callerId, phone, trunk, syncDB.RndGen)
				if err != nil {
					taskLog.TraceError(err)
					return
				}
				rollback := func() func() {
					taskData := task.Data
					return func() {
						ctxRollback, cancelRollback := context.WithTimeout(context.WithoutCancel(ctx), time.Minute)
						defer cancelRollback()
						res, errRollback := syncDB.DataStorage.UpdateTask(
							ctxRollback,
							task.CallTask.Id,
							types.M{
								"$set": types.M{
									"lastCallId":   taskData.LastCallId,
									"lastTrunk":    taskData.LastTrunk,
									"lastCallerId": taskData.LastCallerId,
									"status":       types.TASK_STATUS_DELAY,
								},
							},
						)
						if errRollback != nil {
							task.Log.TraceError(err)
						} else if res == 0 {
							if _, errRollback := syncDB.DataStorage.UpdateTask(
								ctxRollback,
								task.CallTask.Id,
								types.M{
									"$set": types.M{
										"lastCallId":   taskData.LastCallId,
										"lastTrunk":    taskData.LastTrunk,
										"lastCallerId": taskData.LastCallerId,
									},
								},
							); errRollback != nil {
								task.Log.TraceError(errRollback)
							}
						}
					}
				}()
				task.Data.LastCallerId = callerId
				task.Data.LastTrunk = trunk
				task = task.SetCallId(dialerTask.Id)
				queue := campaign.QueueName(config.Cfg.RMQ.DialerQueueSuffix)
				res, err := syncDB.DataStorage.UpdateTask(
					ctx,
					task.CallTask.Id,
					types.M{
						"$set": types.M{
							"lastRequestToDialerAt": time.Now(),
							"lastCallId":            dialerTask.Id,
							"lastTrunk":             trunk,
							"lastCallerId":          callerId,
							"status":                types.TASK_STATUS_PROGRESS,
						},
					},
				)
				if err != nil {
					if !errors.Is(err, ctx.Err()) {
						task.Log.TraceError(err)
					}
					rollback()
					return
				} else if res == 0 {
					tryNext()
					continue
				}
				syncDB.RecieveCallCh <- task
				err = types.DialerTask{
					Func: "dialing_task",
					Data: dialerTask,
				}.Push(syncDB.Publisher, queue, fmt.Sprintf("%s.%d", config.Cfg.RMQ.DialerResults, syncDB.ReplyToGen.Next()))
				if err != nil {
					task.Log.TraceError(err)
					rollback()
					return
				}
				task.Log.Info("sent task to queue - %s", queue)
				queueSize++
				trunkP = nil
				tryNext()
			}
		}
	}()
	return campaignDataCh, doneCh
}

func (c CallTaskState) ParseDialerTask(
	campaign types.Campaign,
	callerId string,
	phone types.TaskPhone,
	trunk types.TrunkContext,
	rndGen utils.RandomGenerator,
) (dialerTask types.DialerTaskData, err error) {
	botData, err := json.Marshal(c.CallTask.CallData)
	if err != nil {
		return dialerTask, err
	}
	campaignData, err := json.Marshal(campaign.FieldData)
	if err != nil {
		return dialerTask, err
	}
	dialerTask = types.DialerTaskData{
		Id:           types.CallId(rndGen.GenerateId().String()),
		TaskId:       c.CallTask.Id,
		RegistryId:   c.CallTask.RegistryId,
		Phone:        phone.Phone,
		CompanyId:    campaign.Id,
		BotData:      string(botData),
		CampaignData: string(campaignData),
		Timezone:     phone.Timezone,
		CallTtl:      campaign.TrunkSettings.CallTTL,
		IsTest:       c.CallTask.IsTest,
		CallData: types.DialerCallData{
			CallerId:            callerId,
			Trunk:               trunk.Name(),
			SecondsForSuccess:   campaign.CallerSettings.ShortCallTimeout,
			CallTaskId:          c.CallTask.Id,
			Dest:                trunk.Dest(phone.Phone),
			Timeout:             campaign.TrunkSettings.Timeout,
			Phone:               phone.Phone,
			Region:              phone.Region,
			ExternalId:          c.CallTask.ExternalId,
			EnableSilenceAsDown: campaign.CallerSettings.SilenceAsDown,
			AttemptNumber:       len(c.Data.CallStates) + 1,
			CallDate:            c.CallTask.CreatedAt.Unix(),
		},
	}
	return dialerTask, nil
}
