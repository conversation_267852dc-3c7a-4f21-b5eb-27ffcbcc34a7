package twilio

import (
	"context"
	"fmt"
	"net/http"
	"runtime/debug"
	"strconv"
	"time"

	"github.com/pkg/errors"

	"github.com/gorilla/mux"
	logger "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/logger/typed_logger"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/mongodb"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/portal"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
)

var log = logger.DefaultLogger

// RunTwilioService При входящем звонке через twilio, на этот ендпоинт будет приходить информация по звонку
func RunTwilioService(
	ctx context.Context,
	databaseProvider mongodb.Client,
	publisher types.RmqPublisher,
	serviceActiveTasks IServiceActiveTasks,
) {
	randomGenerator := utils.NewRandomGenerator(ctx)
	queueGenerator := utils.NewSyncRoundRobin(ctx, config.Cfg.DialerConsumeConcurrency)
	promptsClient := portal.NewPortalClient().PromptsClient()
	router := mux.NewRouter()
	alerts := utils.MakeTgKvintServiceSender(
		config.Cfg.Telegram.KvintServiceEndpoint,
		config.Cfg.Telegram.KvintServiceChannelId,
		config.Cfg.Telegram.KvintServiceApiKey,
	)

	// https://twilio-demo.agent.jiq.ai/twilio-income/65fbed13005dc67524421118/5
	router.HandleFunc("/twilio-income/{campaignId}/{agentId}", func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if r := recover(); r != nil {
				log.Error("accept call, unexpected error - %+v", r)
				fmt.Println(string(debug.Stack()))
			}
		}()
		callId := types.CallId(randomGenerator.GenerateId().String())
		urlValues := r.URL.Query()
		callSid := urlValues.Get("CallSid")
		phoneFrom := urlValues.Get("From")
		phoneTo := urlValues.Get("To")

		vars := mux.Vars(r)
		campaignId := types.CampaignId(vars["campaignId"])
		agentId := vars["agentId"]

		// twilio can retry request, so we need to reply on case of error
		handleError := func(errMsg string, err error) {
			log.TraceError(err)
			w.Header().Set("Content-Type", "application/xml; charset=utf-8")
			if _, err := w.Write([]byte(GenerateResponseForTwilioHangup())); err != nil {
				log.TraceError(err)
			}
			errFmt := fmt.Sprintf("twilio caller error - %s (%s)\ncampaign id - %s\nagent id - %s", errMsg, callId, campaignId, agentId)
			if err := alerts.SendAlert(errFmt); err != nil {
				log.TraceError(err)
			}
		}

		// TASK-4754 тестовый агент подменяется на реальный по номеру,
		// другие номера - завершаем звонок
		if agentId == "test" {
			newAgentId, err := promptsClient.GetAgentIdByPhone(phoneFrom)
			if err != nil {
				handleError(HandleCallError_FailedGetAgentId, fmt.Errorf("failed to get agent id - %s", err))
				PostFailedRecord(databaseProvider, nil, nil, HandleCallError_FailedGetAgentId, phoneFrom, callId)
				return
			}
			if newAgentId.AssistantId == 0 {
				handleError(HandleCallError_FailedGetAgentId, fmt.Errorf("failed to get agent id - %s", err))
				PostFailedRecord(databaseProvider, nil, nil, HandleCallError_FailedGetAgentId, phoneFrom, callId)
				return
			}
			agentId = fmt.Sprintf("%d", newAgentId.AssistantId)
		}

		// По campaignId нужно определить buildId и найти свободный слот для звонка
		slotToCall, campaign, err := GetBuildAndSlotToCall(ctx, databaseProvider, serviceActiveTasks, agentId, callId, campaignId)
		if err != nil {
			handleError(HandleCallError_SlotNotFound, fmt.Errorf("failed to get slot to call - %s", err))
			PostFailedRecord(databaseProvider, campaign, nil, HandleCallError_SlotNotFound, phoneFrom, callId)
			return
		}

		log.CallId(callId).Info("income, campaignId - %s, agentId - %s, container - %s",
			campaignId, agentId, slotToCall.ContainerName)

		// Сохранение таска звонка
		queueNum := queueGenerator.Next()
		dialerTask, callTask, _, err := GetActiveTask(ctx, publisher, databaseProvider, callId, callSid, phoneFrom, phoneTo, agentId, campaignId, queueNum)
		if err != nil {
			handleError(HandleCallError_FailedCreateTask, fmt.Errorf("failed to get active task - %s", err))
			PostFailedRecord(databaseProvider, campaign, &callTask, HandleCallError_FailedCreateTask, phoneFrom, callId)
			return
		}
		serviceActiveTasks.SetQueueNum(callId, queueNum)

		agentData, err := promptsClient.GetPromptsByAgentId(agentId)
		if err != nil {
			handleError(HandleCallError_FailedGetAgentData, fmt.Errorf("failed to get agent - %s", err))
			PostFailedRecord(databaseProvider, campaign, &callTask, HandleCallError_FailedGetAgentData, phoneFrom, callId)
			return
		}

		replyTo := fmt.Sprintf("%s.%s", config.Cfg.DialerResults, "twilio-incoming")
		queue := fmt.Sprintf("%s.%s.%s", config.Cfg.DialerQueueSuffix, slotToCall.ContainerName, slotToCall.SlotName)
		log.
			CampaignId(campaign.Id).
			TaskId(callTask.Id).
			CallId(dialerTask.Data.Id).
			Info("task for dialer - %s, channelId - %s (%s -> %s)", queue, dialerTask.Data.CallData.ChannelId, phoneFrom, phoneTo)

		var agentError string
		if agentData.AssistantSettings.MinutesQuantity <= 0 ||
			agentData.AssistantSettings.AssistantTechnicalStatus == types.AssistantTechnicalStatus_BalanceBlock {
			agentError = HandleCallError_AgentNoMoney
		} else if agentData.AssistantSettings.Status != "ACTIVE" {
			agentError = HandleCallError_AgentNotActive
		} else if err = serviceActiveTasks.IncreaseAgentLines(*campaign, agentId, callId); err != nil {
			log.CallId(callId).Error("failed increase lines: %s", err)
			agentError = HandleCallError_AgentLimitExceeded
		}

		// Reply to twilio to hang up a call. Dialer should send task with error to bot to set correct status
		if agentError != "" {
			dialerTask.Data.Error = agentError
			handleError(HandleCallError_AgentNotActive, errors.New(agentError))
			err = dialerTask.Push(publisher, queue, replyTo)
			if err != nil {
				handleError(HandleCallError_FailedPushTask, fmt.Errorf("failed push task - %s", err))
				PostFailedRecord(databaseProvider, campaign, &callTask, HandleCallError_FailedGetAgentData, phoneFrom, callId)
			}
			return
		}

		// Reply to twilio to make connection to dialer. Send task to dialer and start call
		content := GenerateResponseForTwilio(slotToCall, callId)
		w.Header().Set("Content-Type", "application/xml; charset=utf-8")
		_, err = w.Write([]byte(content))
		if err != nil {
			log.TraceError(err)
		}
		err = dialerTask.Push(publisher, queue, replyTo)
		if err != nil {
			handleError(HandleCallError_FailedPushTask, fmt.Errorf("failed push task - %s", err))
			PostFailedRecord(databaseProvider, campaign, &callTask, HandleCallError_FailedGetAgentData, phoneFrom, callId)
			return
		}
	})
	router.HandleFunc("/twilio-caller/status-callback", func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if r := recover(); r != nil {
				log.Error("status callback, unexpected error - %+v", r)
				fmt.Println(string(debug.Stack()))
			}
		}()
		err := r.ParseForm()
		if err != nil {
			log.TraceError(err)
			return
		}
		callSid := r.PostForm["CallSid"][0]
		callStatus := r.PostForm["CallStatus"][0]
		callDuration := r.PostForm["CallDuration"][0]
		log.Info("status callback, callSid - %s, callStatus - %s, callDuration - %s", callSid, callStatus, callDuration)

		try := 0
		for try <= 12 {
			try++
			log.Info("twilio, update status try - %d", try)

			shouldBreak := func() bool {
				filter := types.M{"call_data.channel_id": callSid}
				ctxFind, cancelCtxFind := context.WithTimeout(ctx, time.Minute)
				defer cancelCtxFind()
				record, err := databaseProvider.FindOneCallResult(ctxFind, filter)
				if err != nil {
					log.TraceError(err)
					return false
				}
				// status already set
				log.Info("status callback, record status - %s", record.Status)
				if record.Status != types.CALL_STATUS_IN_PROGRESS {
					return true
				}

				valuesToUpdate := types.M{
					"status": "call_transfer_success",
				}
				if callStatus == "completed" && callDuration == "0" {
					valuesToUpdate["status"] = "call_transfer_no_pick_up"
				}

				ctxUpdate, cancelUpdate := context.WithTimeout(ctx, time.Minute)
				defer cancelUpdate()
				if matchedCount, err := databaseProvider.UpdateOneCallResult(
					ctxUpdate,
					types.M{"call_data.channel_id": callSid},
					valuesToUpdate,
				); err != nil {
					log.TraceError(err)
					return false
				} else {
					return matchedCount == 1
				}
			}()
			if shouldBreak {
				break
			}
			time.Sleep(10 * time.Second)
		}
	})
	router.HandleFunc("/twilio-caller/audio-record", func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if r := recover(); r != nil {
				log.Error("audio record, unexpected error - %+v", r)
				fmt.Println(string(debug.Stack()))
			}
		}()

		err := r.ParseForm()
		if err != nil {
			log.TraceError(err)
			return
		}
		callSid := r.PostForm["CallSid"][0]
		recordingUrl := r.PostForm["RecordingUrl"][0]
		recordingDur := r.PostForm["RecordingDuration"][0]

		// prepare audio to save
		resp, err := http.Get(recordingUrl)
		if err != nil {
			log.TraceError(err)
			return
		}
		defer resp.Body.Close()

		minioUrl, err := utils.SaveTwilioAudio(callSid, config.Cfg.Minio.MinioAudioBucket, resp.Body, resp.ContentLength, utils.MinioClientConf{
			Host:      config.Cfg.Minio.Host,
			Port:      config.Cfg.Minio.Port,
			AccessKey: config.Cfg.Minio.AccessKey,
			SecretKey: config.Cfg.Minio.SecretKey,
			Secure:    config.Cfg.Minio.Secure,
		}, randomGenerator)
		if err != nil {
			log.TraceError(err)
			return
		}

		// update record in database
		valuesToUpdate := types.M{
			"audio_file": minioUrl,
		}
		if v, err := strconv.ParseFloat(recordingDur, 64); err == nil {
			valuesToUpdate["duration"] = v
			valuesToUpdate["durations.duration_bot"] = v
		}
		filter := types.M{"call_data.channel_id": callSid}

		// save audio to exists record in database. Callback can be catched faster than bot will save record, so we need to try save audio for 2 minute long
		try := 0
		for try <= 12 {
			try++
			log.Info("twilio, update record try - %d", try)

			shouldBreak := func() bool {
				ctxUpdate, cancelUpdate := context.WithTimeout(ctx, time.Minute)
				defer cancelUpdate()
				if matchedCount, err := databaseProvider.UpdateOneCallResult(
					ctxUpdate,
					filter,
					valuesToUpdate,
				); err != nil {
					log.TraceError(err)
					return false
				} else {
					return matchedCount == 1
				}
			}()
			if shouldBreak {
				break
			}
			time.Sleep(10 * time.Second)
		}
	})

	if err := http.ListenAndServe(":50002", router); err != nil {
		if !errors.Is(err, http.ErrServerClosed) {
			log.Panic("error running http server: %s", err)
		}
	}
}
