package twilio

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/outdoing"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/mongodb"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

const (
	HandleCallError_SlotNotFound       = "slot not found"
	HandleCallError_FailedCreateTask   = "failed create task"
	HandleCallError_FailedGetAgentData = "failed get agent data"
	HandleCallError_FailedGetAgentId   = "failed get agent id by phone"
	HandleCallError_FailedPushTask     = "failed push task"
	HandleCallError_AgentNotActive     = "agent is not active"
	HandleCallError_AgentLimitExceeded = "agent limit exceeded"
	HandleCallError_AgentNoMoney       = "agent no money"
)

func GetActiveTask(
	ctx context.Context,
	publisher types.RmqPublisher,
	databaseProvider mongodb.Client,
	callId types.CallId,
	channelId, phone, phoneTo, agentId string,
	campaignId types.CampaignId,
	queueNum int,
) (dialerTask types.DialerTask, callTask types.ExtendedCallTask, campaign types.Campaign, err error) {
	// find campaign
	ctxGetCampaignById, cancelGetCampaignById := context.WithTimeout(ctx, time.Minute)
	defer cancelGetCampaignById()
	campaign, err = databaseProvider.GetCampaignById(ctxGetCampaignById, campaignId)
	if err != nil {
		return dialerTask, callTask, campaign, err
	}
	err = ctxGetCampaignById.Err()
	if err != nil {
		return dialerTask, callTask, campaign, err
	}

	// create task
	taskTime := time.Now()
	incomingTask := types.ExtendedCallTask{
		Project:               campaign.BuildInfo.Project,
		CallData:              types.CallData{AgentId: agentId},
		LastRequestToDialerAt: taskTime,
		AttemptNumber:         1,
		UpdatedAt:             taskTime,
		Status:                types.TASK_STATUS_PROGRESS,
		CallTask: types.CallTask{
			CampaignId: campaign.Id,
			Phones: []types.TaskPhone{{
				Phone:           phone,
				NormalizedPhone: phone,
				Timezone:        0,
				Region:          "",
			}},
			CreatedAt:    taskTime,
			NextCallAt:   taskTime,
			LastCallId:   callId,
			LastCallerId: phoneTo,
			LastTrunk:    types.TrunkContext{Trunk: campaign.TrunkSettings.CallerId, Server: "1"},
		},
	}
	ctxCreateIncomingTask, cancelCreateIncomingTask := context.WithTimeout(ctx, time.Minute)
	defer cancelCreateIncomingTask()
	callTask, err = databaseProvider.CreateIncomingTask(ctxCreateIncomingTask, incomingTask)
	if err != nil {
		return dialerTask, callTask, campaign, err
	}
	err = ctxCreateIncomingTask.Err()
	if err != nil {
		return dialerTask, callTask, campaign, err
	}

	// prepare task for dialer
	phoneToCall := callTask.Phones[0]
	dialerTask = types.DialerTask{
		Func: "dialing_task",
		Data: types.DialerTaskData{
			Id:        callId,
			TaskId:    callTask.Id,
			Phone:     phoneToCall.Phone,
			CompanyId: campaignId,
			Timezone:  phoneToCall.Timezone,
			CallTtl:   campaign.TrunkSettings.CallTTL,
			IsTest:    callTask.IsTest,
			CallData: types.DialerCallData{
				Trunk:               callTask.LastTrunk.Name(),
				SecondsForSuccess:   campaign.CallerSettings.ShortCallTimeout,
				CallTaskId:          callTask.Id,
				Dest:                callTask.LastTrunk.Dest(phoneToCall.Phone),
				Timeout:             campaign.TrunkSettings.Timeout,
				Phone:               phoneToCall.Phone,
				Region:              phoneToCall.Region,
				ChannelId:           channelId, // Для входящих и твилио
				EnableSilenceAsDown: campaign.CallerSettings.SilenceAsDown,
				CallDate:            callTask.CreatedAt.Unix(),
				AttemptNumber:       callTask.AttemptNumber,
				DialerCallDataIn: &types.DialerCallDataIn{
					PhoneTo: phoneTo,
				},
			},
		},
	}

	botData, err := json.Marshal(
		types.M{
			"agent_id": agentId,
		},
	)
	if err != nil {
		return dialerTask, callTask, campaign, err
	}
	dialerTask.Data.BotData = string(botData)
	campaignData, err := json.Marshal(campaign.FieldData)
	if err != nil {
		return dialerTask, callTask, campaign, err
	}
	dialerTask.Data.CampaignData = string(campaignData)

	now := time.Now()
	err = outdoing.NewCallTaskState(campaign, types.CallTask{
		Id:           callTask.Id,
		CampaignId:   campaign.Id,
		Phones:       callTask.Phones,
		IsTest:       campaign.IsTest,
		CreatedAt:    now,
		NextCallAt:   now,
		LastCallId:   callId,
		LastCallerId: phone,
		LastTrunk:    callTask.LastTrunk,
	},
	).Push(publisher, fmt.Sprintf("%s.%d", config.Cfg.DialerResults, queueNum))
	return dialerTask, callTask, campaign, err
}

type SlotToCall struct {
	ContainerName string // 1-06
	SlotName      string // 1-06sl
	ContainerIp   string // **********
	ContainerPort string // 50002
}

func GetCampaign(
	ctx context.Context,
	databaseProvider mongodb.Client,
	campaignId types.CampaignId,
) (types.Campaign, error) {
	var result types.Campaign
	ctxGetCampaignById, cancelGetCampaignById := context.WithTimeout(ctx, time.Minute)
	defer cancelGetCampaignById()
	campaign, errC := databaseProvider.GetCampaignById(ctxGetCampaignById, campaignId)
	if err := ctxGetCampaignById.Err(); err != nil {
		return result, err
	}
	if errC != nil {
		return result, errC
	}
	return campaign, nil
}

func GetBuildAndSlotToCall(
	ctx context.Context,
	databaseProvider mongodb.Client,
	serviceActiveTasks IServiceActiveTasks,
	agentId string,
	callId types.CallId,
	campaignId types.CampaignId,
) (SlotToCall, *types.Campaign, error) {
	var result SlotToCall

	campaign, err := GetCampaign(ctx, databaseProvider, campaignId)
	if err != nil {
		return result, nil, err
	}

	dialerInfo := <-serviceActiveTasks.FindSlotQueueByBuildId(callId, campaign.BuildId, campaignId)
	if dialerInfo.Error != nil {
		return result, &campaign, dialerInfo.Error
	}

	result.ContainerName = dialerInfo.Result.ContainerName
	result.SlotName = fmt.Sprintf("%ssl", dialerInfo.Result.ContainerName)
	result.ContainerIp = dialerInfo.Result.ContainerIp
	result.ContainerPort = "50002"
	return result, &campaign, nil
}

func GenerateResponseForTwilio(slotToCall SlotToCall, callId types.CallId) string {
	return fmt.Sprintf(`<?xml version="1.0" encoding="UTF-8"?>
	<Response>
		<Connect>
			<Stream url="%s/dialer/%s/%s/%s" />
		</Connect>
	</Response>`, config.Cfg.Twilio.MainWsUrl.String(), slotToCall.ContainerIp, slotToCall.ContainerPort, callId)
}

func GenerateResponseForTwilioHangup() string {
	return `<?xml version="1.0" encoding="UTF-8"?>
	<Response>
		<Reject reason="busy" />
	</Response>`
}

func PostFailedRecord(
	databaseProvider mongodb.Client,
	campaign *types.Campaign,
	task *types.ExtendedCallTask,
	failType, phoneFrom string,
	callId types.CallId,
) {
	now := time.Now()
	callResult := types.CallResult{
		StartTs:            int(now.UnixMilli()),
		EndTs:              int(now.UnixMilli()),
		StartDatabase:      now,
		EndDatabase:        now,
		TaskAddedDatabase:  now,
		DuplicateMessages:  map[string]string{},
		StructuredLogs:     []interface{}{},
		CallWithProblems:   true,
		ErrorMsg:           failType,
		Direction:          types.CALL_DIRECTION_IN,
		InterruptionStatus: 0,
		Source:             "caller",
		CallData:           types.CallResultCallData{},
		CategoryStatus:     "Отсутствие диалога",
		DialerId:           "",
		Attempt: types.Attempt{
			CallId:     callId,
			Phone:      phoneFrom,
			Status:     types.CALL_STATUS_ERROR_BEFORE_UP,
			Tz:         3,
			Trunk:      "twilio",
			ResultCall: "Ошибка обработки звонка",
		},
	}
	if task != nil {
		callResult.CallTaskId = task.Id
	}
	if campaign != nil {
		callResult.CampaignId = campaign.Id
		callResult.BuildInfo = campaign.BuildInfo
	} else {
		callResult.BuildInfo.Project = "failed_calls"
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
	defer cancel()
	if err := databaseProvider.CreateRecord(ctx, callResult); err != nil {
		log.CallId(callId).Error("postFailedRecord, error - %s", err)
	}
}

func RmqConsumerAutoAck[T any](
	conn *rabbitmq.Conn,
	queueListen string,
	handler func(result T, err error),
) (*rabbitmq.Consumer, error) {
	consumer, err := rabbitmq.NewConsumer(
		conn,
		func(d rabbitmq.Delivery) rabbitmq.Action {
			var data T
			raw, err := utils.UnzipData(d.Body)
			if err != nil {
				handler(data, err)
				return rabbitmq.NackDiscard
			}
			err = json.Unmarshal(raw, &data)
			if err != nil {
				handler(data, err)
				return rabbitmq.NackDiscard
			}

			handler(data, nil)
			return rabbitmq.Ack
		},
		queueListen,
		rabbitmq.WithConsumerOptionsQueueDurable,
	)
	return consumer, err
}

func Publish(publisher types.RmqPublisher, packet types.RmqCeleryPacket, queueTarget string) error {
	headers := rabbitmq.Table{
		"correlation_id": packet.CorrelationId,
		"reply_to":       packet.AnswerQueue,
		"id":             packet.CorrelationId,
	}
	bytes, err := json.Marshal(packet)
	if err != nil {
		return err
	}
	gzipped, err := utils.GzipData(bytes)
	if err != nil {
		return err
	}

	return publisher.Publish(
		gzipped,
		[]string{queueTarget},
		rabbitmq.WithPublishOptionsHeaders(headers),
		rabbitmq.WithPublishOptionsCorrelationID(packet.CorrelationId),
		rabbitmq.WithPublishOptionsReplyTo(packet.AnswerQueue),
		rabbitmq.WithPublishOptionsMandatory,
	)
}

func MakeCeleryPacket(data interface{}, answerQueue string) types.RmqCeleryPacket {
	corrId := uuid.New().String()
	now := time.Now()
	started := fmt.Sprintf("%s %s", now.Format("2006-01-02"), now.Format("01:02:05"))
	return types.RmqCeleryPacket{
		Version:       1,
		CorrelationId: corrId,
		Status:        "Task",
		Num:           1,
		StartDate:     started,
		SendDate:      started,
		From:          "go-caller",
		To:            "unknown",
		Ttl:           60,
		AnswerQueue:   answerQueue,
		Data:          data,
	}
}
