package twilio

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"sync"
	"time"

	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

type IServiceActiveTasks interface {
	FindSlotQueueByBuildId(callId types.CallId, buildId types.BuildId, campaignId types.CampaignId) chan DialerInfoResponse
	IncreaseAgentLines(campaign types.Campaign, agentId string, callId types.CallId) error
	SetQueueNum(callId types.CallId, queueNum int)
}

type ServiceActiveTasks struct {
	publisher         types.RmqPublisher
	agentLimits       *AgentLimits
	queueToListen     string
	queueTwilioCaller string
	slotsPerBuildId   map[types.BuildId]map[string]SavedDialerInfo
	listeners         map[types.BuildId]map[types.CallId]chan types.DialerShowInfo
	queueMap          *utils.TTLMap[types.CallId, int]
	mu                sync.Mutex
}

type SavedDialerInfo struct {
	Value     types.DialerShowInfo
	FreeLines int
	ExpiredAt time.Time
}

type DialerInfoResponse struct {
	Result types.DialerShowInfo
	Error  error
}

// FindSlotQueueByBuildId - функция поиска нужного слота по buildId. Если таймаут истечет, запрос считается неудачным.
func (s *ServiceActiveTasks) FindSlotQueueByBuildId(callId types.CallId, buildId types.BuildId, campaignId types.CampaignId) chan DialerInfoResponse {
	result := make(chan DialerInfoResponse, 1)
	if slot, err := s.getSlotFromCache(buildId); err == nil {
		result <- DialerInfoResponse{
			Result: slot.Value,
			Error:  nil,
		}
		return result
	}

	listener := s.setListener(callId, buildId)
	defer s.resetListener(callId, buildId)

	s.requestValueFromDialer(campaignId, buildId, false)
	select {
	case info := <-listener:
		result <- DialerInfoResponse{
			Result: info,
			Error:  nil,
		}

	case <-time.After(15 * time.Second):
		result <- DialerInfoResponse{
			Result: types.DialerShowInfo{},
			Error:  errors.New("too long wait for response"),
		}
	}

	return result
}

func (s *ServiceActiveTasks) IncreaseAgentLines(campaign types.Campaign, agentId string, callId types.CallId) error {
	return s.agentLimits.IncreaseAgentLines(campaign, agentId, callId)
}

func (s *ServiceActiveTasks) SetQueueNum(callId types.CallId, queueNum int) {
	s.queueMap.Set(callId, queueNum)
}

// setListener - Либо значение сразу достается из кеша, либо устанавливается подписка на событие
func (s *ServiceActiveTasks) setListener(callId types.CallId, buildId types.BuildId) chan types.DialerShowInfo {
	s.mu.Lock()
	defer s.mu.Unlock()
	if _, ok := s.listeners[buildId]; !ok {
		s.listeners[buildId] = make(map[types.CallId]chan types.DialerShowInfo)
	}
	s.listeners[buildId][callId] = make(chan types.DialerShowInfo, 1)

	return s.listeners[buildId][callId]
}

// resetListener - Удаление подписки
func (s *ServiceActiveTasks) resetListener(callId types.CallId, buildId types.BuildId) {
	s.mu.Lock()
	defer s.mu.Unlock()
	delete(s.listeners[buildId], callId)
}

// getSlotFromCache - Попытка взять нужный слот из кеша
func (s *ServiceActiveTasks) getSlotFromCache(buildId types.BuildId) (SavedDialerInfo, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	slots, ok := s.slotsPerBuildId[buildId]
	if !ok {
		return SavedDialerInfo{}, errors.New("no slot")
	}

	for key, slot := range slots {
		if time.Now().After(slot.ExpiredAt) {
			delete(s.slotsPerBuildId[buildId], key)
			continue
		}
		if slot.FreeLines <= 0 {
			delete(s.slotsPerBuildId[buildId], key)
			continue
		}
		if slot.FreeLines > 0 {
			slot.FreeLines--
			s.slotsPerBuildId[buildId][key] = slot
			return slot, nil
		}
	}
	return SavedDialerInfo{}, errors.New("no slot")
}

// requestValueFromDialer Запрос в апи дайлера (на одной очереди может быть много консьюмеров)
func (s *ServiceActiveTasks) requestValueFromDialer(campaignId types.CampaignId, buildId types.BuildId, delayed bool) {
	if delayed {
		time.Sleep(2 * time.Second)
	}

	queueTarget := fmt.Sprintf("kv.twilio.%s.%s.api", campaignId, buildId)
	packet := MakeCeleryPacket(
		types.DialerShowInfoRequest{Func: "showInfo"},
		s.queueToListen,
	)
	if err := Publish(s.publisher, packet, queueTarget); err != nil {
		log.TraceError(err)
	}
}

// saveValueFromRmq Прием ответов от слотов. Сохранение в кеше, и рассылка по подписчикам
func (s *ServiceActiveTasks) saveValueFromRmq(info types.DialerShowInfo) {
	s.mu.Lock()
	defer s.mu.Unlock()
	if info.BuildId == "" || info.ContainerName == "" {
		return
	}

	// value is fresh, so we're ignoring possible existing value in map
	savedInfo := SavedDialerInfo{
		Value:     info,
		FreeLines: info.LinesLimit - info.TasksInWork,
		ExpiredAt: time.Now().Add(1 * time.Minute),
	}
	if _, ok := s.slotsPerBuildId[info.BuildId]; !ok {
		s.slotsPerBuildId[info.BuildId] = make(map[string]SavedDialerInfo)
	}
	s.slotsPerBuildId[info.BuildId][info.ContainerName] = savedInfo

	listeners, ok := s.listeners[info.BuildId]
	if !ok {
		return
	}

	for _, ch := range listeners {
		// Если свободных слотов нет, а подписчик на событие все ещё есть - делаем запрос снова
		if savedInfo.FreeLines <= 0 {
			s.requestValueFromDialer(info.CampaignId, info.BuildId, true) // delay, to prevent cycle re-ask through rabbitmq
			return
		}

		if ch != nil {
			ch <- savedInfo.Value
		}
		savedInfo.FreeLines--
		s.slotsPerBuildId[info.BuildId][info.ContainerName] = savedInfo
	}
}

// one agent can have only 1 parallel call
type AgentLimits struct {
	agentLines     map[ /*agent_id*/ string]int
	activeCallIds  map[types.CallId] /*agent_id*/ string
	expiredCallIds map[types.CallId]time.Time
	mu             sync.RWMutex
}

func MakeAgentLimits() *AgentLimits {
	return &AgentLimits{
		agentLines:     make(map[string]int),
		activeCallIds:  make(map[types.CallId]string),
		expiredCallIds: make(map[types.CallId]time.Time),
	}
}

func (a *AgentLimits) IncreaseAgentLines(campaign types.Campaign, agentId string, callId types.CallId) error {
	a.mu.Lock()
	defer a.mu.Unlock()
	if v, ok := a.agentLines[agentId]; ok && v > 5 {
		return errors.New("agent busy with 5 calls already")
	}
	a.agentLines[agentId]++
	a.activeCallIds[callId] = agentId
	a.expiredCallIds[callId] = time.Now().Add(
		time.Duration(campaign.TrunkSettings.CallTTL+campaign.TrunkSettings.Timeout+600) * time.Second,
	)
	return nil
}

func (a *AgentLimits) DecreaseAgentLines(callId types.CallId) error {
	a.mu.Lock()
	defer a.mu.Unlock()
	agentId, ok := a.activeCallIds[callId]
	if !ok {
		return errors.New("failed to find agent_id by call_id")
	}
	delete(a.activeCallIds, callId)
	delete(a.expiredCallIds, callId)

	if _, ok := a.agentLines[agentId]; !ok {
		return errors.New("failed to decrease agent lines")
	}
	a.agentLines[agentId]--
	if a.agentLines[agentId] <= 0 {
		delete(a.agentLines, agentId)
	}
	return nil
}

func (a *AgentLimits) getExpiredCalls() []types.CallId {
	a.mu.RLock()
	defer a.mu.RUnlock()
	callIds := make([]types.CallId, 0)
	for callId, expiredAt := range a.expiredCallIds {
		if time.Now().After(expiredAt) {
			callIds = append(callIds, callId)
		}
	}
	return callIds
}

func (a *AgentLimits) runCheckExpiredCalls(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-time.After(10 * time.Minute):
			for _, v := range a.getExpiredCalls() {
				if err := a.DecreaseAgentLines(v); err != nil {
					err = fmt.Errorf("failed to decrease expired line (%s) %w", v, err)
					log.TraceError(err)
				}
			}
		}
	}
}

// RunServiceActiveTasks service to find correct slot to call + count active lines for agent id
func RunServiceActiveTasks(ctx context.Context, conn *rabbitmq.Conn, publisher types.RmqPublisher) IServiceActiveTasks {
	service := &ServiceActiveTasks{
		agentLimits:       MakeAgentLimits(),
		queueToListen:     "kv.twilio.apply-info-from-slots",
		queueTwilioCaller: fmt.Sprintf("%s.%s", config.Cfg.DialerResults, "twilio-incoming"),
		slotsPerBuildId:   make(map[types.BuildId]map[string]SavedDialerInfo),
		listeners:         make(map[types.BuildId]map[types.CallId]chan types.DialerShowInfo),
		queueMap:          utils.MakeTTLMap[types.CallId, int](time.Hour),
		publisher:         publisher,
	}

	// check timeouted calls
	go service.agentLimits.runCheckExpiredCalls(ctx)

	// consume dialer responses about his server info (to let twilio know, what url he should try to request)
	handler := func(info types.RmqDialerShowInfo, err error) {
		if err != nil {
			log.TraceError(err)
			return
		}
		service.saveValueFromRmq(info.Data)
	}
	consumerDialerServerInfo, errC := RmqConsumerAutoAck[types.RmqDialerShowInfo](conn, service.queueToListen, handler)
	if errC != nil {
		log.Panic(errC.Error())
	}

	// consume dialer responses and resend them to main caller
	consumerDialerResults, errC := rabbitmq.NewConsumer(
		conn,
		func(d rabbitmq.Delivery) rabbitmq.Action {
			raw, err := utils.UnzipData(d.Body)
			if err != nil {
				log.TraceError(err)
				return rabbitmq.NackDiscard
			}
			var res types.DialerResponseAny
			err = json.Unmarshal(raw, &res)
			if err != nil {
				log.TraceError(err)
				return rabbitmq.NackDiscard
			}
			headerId, ok := d.Headers["id"].(string)
			if !ok {
				return rabbitmq.Ack
			}
			callId := types.CallId(headerId)
			if res.Status == types.CELERY_STATUS_COMPLETE.String() {
				err = service.agentLimits.DecreaseAgentLines(callId)
			}
			if err != nil {
				log.TraceError(err)
			}
			queueNum, ok := service.queueMap.Get(callId)
			if !ok {
				return rabbitmq.Ack
			}
			if res.Status == types.CELERY_STATUS_COMPLETE.String() {
				service.queueMap.Reset(callId)
			}
			if err := publisher.Publish(
				d.Body,
				[]string{fmt.Sprintf("%s.%d", config.Cfg.RMQ.DialerResults, queueNum)},
				rabbitmq.WithPublishOptionsHeaders(rabbitmq.Table(d.Headers)),
				rabbitmq.WithPublishOptionsCorrelationID(res.CorrelationId),
				rabbitmq.WithPublishOptionsMandatory,
			); err != nil {
				log.TraceError(err)
			}
			return rabbitmq.Ack
		},
		service.queueTwilioCaller,
		rabbitmq.WithConsumerOptionsQueueDurable,
		rabbitmq.WithConsumerOptionsQOSPrefetch(32),
	)
	if errC != nil {
		log.Panic(errC.Error())
	}

	// listen signal to quit
	go func() {
		log.Info("Started")
		<-ctx.Done()
		consumerDialerServerInfo.Close()
		consumerDialerResults.Close()
		log.Info("Stopped")
	}()

	return service
}
