package web

import (
	"context"
	"encoding/json"
	"errors"
	"sync"

	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

// ActiveTasksRequests map, to catch responses from dialer and send to correct ongoing request on caller
type ActiveTasksRequests struct {
	requests map[types.CallTaskId]chan types.DialerServerInfo // key = task_id
	mu       sync.Mutex
}

func (a *ActiveTasksRequests) SetRequest(taskId types.CallTaskId, chResult chan types.DialerServerInfo) error {
	a.mu.Lock()
	defer a.mu.Unlock()
	if _, prs := a.requests[taskId]; prs {
		return errors.New("task_id already registered in requests")
	}
	a.requests[taskId] = chResult
	return nil
}

func (a *ActiveTasksRequests) ResetRequest(taskId types.CallTaskId) {
	a.mu.Lock()
	defer a.mu.Unlock()
	delete(a.requests, taskId)
}

func (a *ActiveTasksRequests) ApplyResponse(dialerInfo types.DialerServerInfo) error {
	a.mu.Lock()
	defer a.mu.Unlock()
	if ch, ok := a.requests[dialerInfo.TaskId]; ok {
		ch <- dialerInfo
		return nil
	}
	return errors.New("not found task id to response")
}

func (a *ActiveTasksRequests) ConsumeDialerServerInfo(
	ctx context.Context,
	conn *rabbitmq.Conn,
	queue string,
) {
	log.Info("consumeDialerReplies, start")
	consumer, err := rabbitmq.NewConsumer(
		conn,
		func(d rabbitmq.Delivery) rabbitmq.Action {
			raw, err := utils.UnzipData(d.Body)
			if err != nil {
				log.TraceError(err)
				return rabbitmq.NackDiscard
			}
			var res types.DialerResponseAny
			err = json.Unmarshal(raw, &res)
			if err != nil {
				log.TraceError(err)
				return rabbitmq.NackDiscard
			}
			var data types.DialerServerInfo
			err = json.Unmarshal(res.Data, &data)
			if err != nil {
				log.TraceError(err)
				return rabbitmq.NackDiscard
			}
			if res.Status != types.CELERY_STATUS_COMPLETE.String() {
				log.Error("unknown status - %s", res.Status)
				return rabbitmq.NackDiscard
			}

			if err := a.ApplyResponse(data); err != nil {
				log.TraceError(err)
			}
			return rabbitmq.Ack
		},
		queue,
		rabbitmq.WithConsumerOptionsQueueDurable,
		func(options *rabbitmq.ConsumerOptions) {
			args := make(rabbitmq.Table)
			args["x-expires"] = 86400000 // 1day in milliseconds
			options.QueueOptions.Args = args
		},
	)
	if err != nil {
		log.Panic("serviceActiveTasks, failed run consumer - %s", err)
	}
	<-ctx.Done()
	consumer.Close()
}
