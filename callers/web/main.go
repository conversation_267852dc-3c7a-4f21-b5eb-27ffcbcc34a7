package web

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/mongodb"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	logger "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/logger/typed_logger"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

var log = logger.DefaultLogger

type WebCaller struct {
	randomGen             utils.RandomGenerator
	queueCallerForResults string
	queueForDialerInfo    string
	dbProvider            mongodb.Client
	requests              *ActiveTasksRequests
	consumer              *rabbitmq.Consumer
	publisher             *rabbitmq.Publisher
}

func RunService(
	ctx context.Context,
	databaseProvider mongodb.Client,
	rmqConn *rabbitmq.Conn,
) *WebCaller {
	service := &WebCaller{
		randomGen:             utils.NewRandomGenerator(ctx),
		queueCallerForResults: fmt.Sprintf("%s.%s", config.Cfg.DialerResults, "web-incoming"),
		queueForDialerInfo:    fmt.Sprintf("%s.%s", config.Cfg.DialerResults, "web-incoming.dialer-info"),
		dbProvider:            databaseProvider,
		requests: &ActiveTasksRequests{
			requests: make(map[types.CallTaskId]chan types.DialerServerInfo),
		},
	}

	publisher, err := rabbitmq.NewPublisher(rmqConn)
	if err != nil {
		log.Panic(err.Error())
	}
	publisher.NotifyReturn(func(r rabbitmq.Return) {
		log.Error("queue not exist %s", r.RoutingKey)
	})
	service.publisher = publisher

	// apply server info from dialer slot
	go service.requests.ConsumeDialerServerInfo(ctx, rmqConn, service.queueForDialerInfo)

	// receive request from rabbitmq and send to dialer queue
	consumer, errC := rabbitmq.NewConsumer(
		rmqConn,
		func(d rabbitmq.Delivery) rabbitmq.Action {
			raw, err_ := utils.UnzipData(d.Body)
			if err_ != nil {
				log.TraceError(err)
				return rabbitmq.NackDiscard
			}
			fmt.Printf("==== raw: %s\n", string(raw))
			webRequest := WebIncomingRequest{}
			celeryData := &types.RmqCeleryPacket{Data: &webRequest}
			err_ = json.Unmarshal(raw, &celeryData)
			if err_ != nil {
				log.TraceError(err)
				return rabbitmq.NackDiscard
			}

			callId := types.CallId(service.randomGen.GenerateId().String())
			log.CallId(callId).Info("web request: %+v", webRequest)

			// text web dialer
			if !webRequest.IsAudio {
				if err := service.SendTextTask(ctx, callId, d.ReplyTo, webRequest); err != nil {
					log.TraceError(err)
					return rabbitmq.NackDiscard
				}
				return rabbitmq.Ack
			}

			// audio web dialer
			res := service.SendAudioTask(ctx, callId, d.ReplyTo, webRequest)
			log.CallId(callId).Info("web response (%s): %+v", webRequest.ChannelId, res)

			headers := map[string]interface{}{
				"correlation_id": celeryData.CorrelationId,
				"id":             celeryData.CorrelationId,
				"task":           d.ReplyTo,
			}
			bytes, err := json.MarshalIndent(res, "", "  ")
			if err != nil {
				log.TraceError(err)
				return rabbitmq.NackDiscard
			}
			gzipped, err := utils.GzipData(bytes)
			if err != nil {
				log.TraceError(err)
				return rabbitmq.NackDiscard
			}
			if err := publisher.Publish(
				gzipped,
				[]string{d.ReplyTo},
				rabbitmq.WithPublishOptionsHeaders(headers),
				rabbitmq.WithPublishOptionsCorrelationID(celeryData.CorrelationId),
			); err != nil {
				log.TraceError(err)
				return rabbitmq.NackDiscard
			}

			return rabbitmq.Ack
		},
		config.Cfg.Web.Queue,
		rabbitmq.WithConsumerOptionsQueueDurable,
		rabbitmq.WithConsumerOptionsQOSPrefetch(32),
	)
	if errC != nil {
		log.Panic("failed to run consumer - %s", errC)
	}
	service.consumer = consumer

	return service
}

func (s *WebCaller) Close() {
	if s.consumer != nil {
		s.consumer.Close()
	}
	if s.publisher != nil {
		s.publisher.Close()
	}
}

// SendTextTask works with web-dialer, that works only with text (supposed to use for telegram, whatsapp, vk, etc)
func (s *WebCaller) SendTextTask(
	ctx context.Context,
	callId types.CallId,
	replyTo string,
	webRequest WebIncomingRequest,
) error {
	dialerTask, _, campaign, err := GetActiveTask(
		ctx,
		s.dbProvider,
		callId,
		webRequest.AgentId,
		webRequest.CampaignId,
	)
	if err != nil {
		return err
	}
	// dialer will make reply with "create_session" data
	dialerTask.Data.DialerTaskDataWeb = &types.DialerTaskDataWeb{
		CallerQueueToMakeReplies: replyTo,
	}

	return dialerTask.Push(
		s.publisher,
		campaign.QueueName(config.Cfg.DialerQueueSuffix),
		s.queueCallerForResults,
	)
}

// SendAudioTask works with audio agent dialer (agent = hardcode and it must be only one audio dialer?)
func (s *WebCaller) SendAudioTask(
	ctx context.Context,
	callId types.CallId,
	replyTo string,
	webRequest WebIncomingRequest,
) WebIncomingResponse {
	dialerTask, _, campaign, err := GetActiveTask(
		ctx,
		s.dbProvider,
		callId,
		webRequest.AgentId,
		webRequest.CampaignId,
	)
	if err != nil {
		return WebIncomingResponse{Error: err.Error()}
	}

	// set queue for dialer to response with info about slot (ip, server, container, etc)
	dialerTask.Data.DialerTaskDataIn = &types.DialerTaskDataIn{
		CallerQueueOnTaskReceived: s.queueForDialerInfo,
	}
	result := make(chan types.DialerServerInfo, 1)
	if err = s.requests.SetRequest(dialerTask.Data.TaskId, result); err != nil {
		return WebIncomingResponse{Error: err.Error()}
	}
	defer s.requests.ResetRequest(dialerTask.Data.TaskId)

	// Отправка таска на дайлер
	if err := dialerTask.Push(
		s.publisher,
		campaign.QueueName(config.Cfg.DialerQueueSuffix),
		s.queueCallerForResults,
	); err != nil {
		return WebIncomingResponse{Error: err.Error()}
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	select {
	case <-ctx.Done():
		return WebIncomingResponse{
			Error: "timeout on waiting server info from dialer",
		}
	case v := <-result:
		// // for local dev
		// if webRequest.CampaignId == "6732f50dba92a2b9ec161dcc" {
		// 	v.ContainerIp = "127.0.0.1"
		// }
		return WebIncomingResponse{
			CallId: callId,
			Url: fmt.Sprintf(
				"%s/dialer/%s/%s/%s",
				config.Cfg.WebMainWsUrl.String(),
				v.ContainerIp,
				v.ContainerPort,
				v.CallId,
			),
		}
	}
}

type WebIncomingRequest struct {
	CampaignId types.CampaignId `json:"campaign_id"`
	AgentId    string           `json:"agent_id"`
	ChannelId  string           `json:"channel_id"` // for debug logs from web to caller-dialer
	IsAudio    bool             `json:"is_audio"`
}

type WebIncomingResponse struct {
	CallId types.CallId `json:"call_id"`
	Url    string       `json:"url"`
	Error  string       `json:"error"`
}

func GetActiveTask(
	ctx context.Context,
	databaseProvider mongodb.Client,
	callId types.CallId,
	agentId string,
	campaignId types.CampaignId,
) (dialerTask types.DialerTask, callTask types.ExtendedCallTask, campaign types.Campaign, err error) {
	// find campaign
	ctxGetCampaignById, cancelGetCampaignById := context.WithTimeout(ctx, time.Minute)
	defer cancelGetCampaignById()
	campaign, err = databaseProvider.GetCampaignById(ctxGetCampaignById, campaignId)
	if err != nil {
		return dialerTask, callTask, campaign, err
	}
	err = ctxGetCampaignById.Err()
	if err != nil {
		return dialerTask, callTask, campaign, err
	}

	// create task
	taskTime := time.Now()
	incomingTask := types.ExtendedCallTask{
		Project:               campaign.BuildInfo.Project,
		LastRequestToDialerAt: taskTime,
		AttemptNumber:         1,
		UpdatedAt:             taskTime,
		CallData:              types.CallData{AgentId: agentId},
		Status:                types.TASK_STATUS_PROGRESS,
		CallTask: types.CallTask{
			CampaignId: campaign.Id,
			CreatedAt:  taskTime,
			NextCallAt: taskTime,
			Phones: []types.TaskPhone{{
				Phone:           "",
				NormalizedPhone: "",
				Timezone:        0,
				Region:          "",
			}},
			LastCallId: callId,
		},
	}
	ctxCreateIncomingTask, cancelCreateIncomingTask := context.WithTimeout(ctx, time.Minute)
	defer cancelCreateIncomingTask()
	callTask, err = databaseProvider.CreateIncomingTask(ctxCreateIncomingTask, incomingTask)
	if err != nil {
		return dialerTask, callTask, campaign, err
	}
	err = ctxCreateIncomingTask.Err()
	if err != nil {
		return dialerTask, callTask, campaign, err
	}

	// prepare task for dialer
	phoneToCall := callTask.Phones[0]
	dialerTask = types.DialerTask{
		Func: "dialing_task",
		Data: types.DialerTaskData{
			Id:        callId,
			TaskId:    callTask.Id,
			Phone:     phoneToCall.Phone,
			CompanyId: campaignId,
			Timezone:  phoneToCall.Timezone,
			CallTtl:   campaign.TrunkSettings.CallTTL,
			IsTest:    callTask.IsTest,
			CallData: types.DialerCallData{
				Trunk:               callTask.LastTrunk.Name(),
				SecondsForSuccess:   campaign.CallerSettings.ShortCallTimeout,
				CallTaskId:          callTask.Id,
				Dest:                callTask.LastTrunk.Dest(phoneToCall.Phone),
				Timeout:             campaign.TrunkSettings.Timeout,
				Phone:               phoneToCall.Phone,
				Region:              phoneToCall.Region,
				ExternalId:          callTask.ExternalId,
				EnableSilenceAsDown: campaign.CallerSettings.SilenceAsDown,
				CallDate:            callTask.CreatedAt.Unix(),
				AttemptNumber:       callTask.AttemptNumber,
			},
		},
	}

	botData, err := json.Marshal(
		types.M{
			"agent_id": agentId,
		},
	)
	if err != nil {
		return dialerTask, callTask, campaign, err
	}
	dialerTask.Data.BotData = string(botData)
	campaignData, err := json.Marshal(campaign.FieldData)
	if err != nil {
		return dialerTask, callTask, campaign, err
	}
	dialerTask.Data.CampaignData = string(campaignData)
	return dialerTask, callTask, campaign, nil
}
