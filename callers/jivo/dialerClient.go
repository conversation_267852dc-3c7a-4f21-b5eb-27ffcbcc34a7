package jivo

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"sync"

	"github.com/google/uuid"
	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

type DialerTask uint8

const (
	dialerTaskCreateSession = "CREATE_SESSION"
	dialerTaskCloseSession  = "CLOSE_SESSION"
	dialerTaskClientMessage = "CLIENT_MESSAGE"
)

const (
	CREATE_SESSION DialerTask = iota
	CLOSE_SESSION
	CLIENT_MESSAGE
)

var (
	dialerTasks = [3]string{
		CREATE_SESSION: dialerTaskCreateSession,
		CLOSE_SESSION:  dialerTaskCloseSession,
		CLIENT_MESSAGE: dialerTaskClientMessage,
	}
)

func (d DialerTask) String() string {
	return dialerTasks[d]
}

var createSessionQueue = fmt.Sprintf("kv.dialer.%s", config.Cfg.Jivo.CampaignId)

type DialerRPC struct {
	ConsumerQueue string
	Consumer      *rabbitmq.Consumer
	Publisher     *rabbitmq.Publisher
	Requests      map[string]chan []byte
	Mu            sync.RWMutex
}

func NewDialerRPC(consumerQueue string, conn *rabbitmq.Conn) (*DialerRPC, error) {
	dialerRPC := &DialerRPC{
		ConsumerQueue: consumerQueue,
		Requests:      map[string]chan []byte{},
	}
	publisher, err := rabbitmq.NewPublisher(conn)
	if err != nil {
		return nil, err
	}
	dialerRPC.Publisher = publisher
	consumer, err := rabbitmq.NewConsumer(
		conn,
		func(d rabbitmq.Delivery) rabbitmq.Action {
			raw, errZip := utils.UnzipData(d.Body)
			if errZip != nil {
				return rabbitmq.NackDiscard
			}
			dialerRPC.Mu.RLock()
			defer dialerRPC.Mu.RUnlock()
			if resCh, ok := dialerRPC.Requests[d.CorrelationId]; ok {
				resCh <- raw
				return rabbitmq.Ack
			} else {
				return rabbitmq.NackDiscard
			}
		},
		consumerQueue,
		rabbitmq.WithConsumerOptionsQueueDurable,
		rabbitmq.WithConsumerOptionsQOSPrefetch(32),
	)
	if err != nil {
		return nil, err
	}
	dialerRPC.Consumer = consumer
	return dialerRPC, nil
}

func (c *DialerRPC) Send(b []byte, routingKey string, task DialerTask, id types.CallId) error {
	gzipped, err := utils.GzipData(b)
	if err != nil {
		return err
	}
	headers := rabbitmq.Table{
		"task": task.String(),
		"id":   string(id),
	}
	err = c.Publisher.Publish(
		gzipped,
		[]string{routingKey},
		rabbitmq.WithPublishOptionsHeaders(headers),
		rabbitmq.WithPublishOptionsCorrelationID(uuid.New().String()),
		rabbitmq.WithPublishOptionsMandatory,
	)
	return err
}

func (c *DialerRPC) Call(ctx context.Context, b []byte, routingKey string, task DialerTask, id types.CallId) (res []byte, err error) {
	gzipped, err := utils.GzipData(b)
	if err != nil {
		return res, err
	}
	correlationId := uuid.New().String()
	headers := rabbitmq.Table{
		"task": task.String(),
		"id":   string(id),
	}
	resCh := make(chan []byte, 1)
	c.Mu.Lock()
	c.Requests[correlationId] = resCh
	c.Mu.Unlock()
	defer func() {
		c.Mu.Lock()
		delete(c.Requests, correlationId)
		c.Mu.Unlock()
	}()
	err = c.Publisher.Publish(
		gzipped,
		[]string{routingKey},
		rabbitmq.WithPublishOptionsHeaders(headers),
		rabbitmq.WithPublishOptionsCorrelationID(correlationId),
		rabbitmq.WithPublishOptionsReplyTo(c.ConsumerQueue),
		rabbitmq.WithPublishOptionsMandatory,
	)
	if err != nil {
		return res, err
	}
	select {
	case <-ctx.Done():
		return res, ctx.Err()
	case res := <-resCh:
		return res, err
	}
}

type DialerClientText struct {
	Text string `json:"text"`
}

type DialerSession struct {
	client   DialerClient
	mu       sync.Mutex
	isOpen   bool
	campaign types.Campaign
	task     types.ExtendedCallTask
}

func (s *DialerSession) Task() types.ExtendedCallTask {
	return s.task
}

func (s *DialerSession) Campaign() types.Campaign {
	return s.campaign
}

func (s *DialerSession) Open(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	if s.isOpen {
		return nil
	}
	s.isOpen = true
	b, err := json.Marshal(types.DialerTask{
		Func: CREATE_SESSION.String(),
		Data: types.DialerTaskData{
			Id:        s.task.LastCallId,
			TaskId:    s.task.Id,
			CompanyId: s.campaign.Id,
			CallTtl:   s.campaign.TrunkSettings.CallTTL,
			IsTest:    s.task.IsTest,
			CallData: types.DialerCallData{
				CallTaskId:    s.task.Id,
				Timeout:       s.campaign.TrunkSettings.Timeout,
				AttemptNumber: s.task.AttemptNumber,
				DialerCallDataTwilio: &types.DialerCallDataTwilio{
					AgentId: config.Cfg.Jivo.AgentId, //TODO: getting from database?
				},
				JivoData: &types.JivoData{
					ChatId:   s.task.CallData.ChatId,
					ClientId: s.task.CallData.ClientId,
					JivoURL:  s.task.CallData.JivoURL,
				},
			},
		},
	})
	if err != nil {
		return err
	}
	res, err := s.client.Call(ctx, b, createSessionQueue, CREATE_SESSION, s.task.LastCallId)
	if err != nil {
		return err
	}
	var openResp types.JivoData
	if err := json.Unmarshal(res, &openResp); err != nil {
		return err
	}
	if openResp.InstanceQueue == "" {
		return errors.New("instance_queue is empty")
	}
	s.task.CallData.InstanceQueue = openResp.InstanceQueue
	return nil
}

func (s *DialerSession) Close(ctx context.Context) (dr types.DialerResult, err error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	if !s.isOpen {
		return dr, nil
	}
	s.isOpen = false
	res, err := s.client.Call(ctx, []byte{}, s.task.CallData.InstanceQueue, CLOSE_SESSION, s.task.LastCallId)
	if err != nil {
		return dr, err
	}
	err = json.Unmarshal(res, &dr)
	if err != nil {
		return dr, err
	}
	return dr, err
}

func (s *DialerSession) Message(text string) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	if !s.isOpen {
		return nil
	}
	b, err := json.Marshal(DialerClientText{Text: text})
	if err != nil {
		return err
	}
	return s.client.Send(b, s.task.CallData.InstanceQueue, CLIENT_MESSAGE, s.task.LastCallId)
}
