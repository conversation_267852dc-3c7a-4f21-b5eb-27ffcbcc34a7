package tests

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/jivo"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type JivoMockClient struct{}

func (c JivoMockClient) Request(jivoEvent jivo.JivoEvent) error {
	return nil
}

type DialerMockClient struct {
	CallReturn []byte
}

func (d *DialerMockClient) Send(b []byte, routingKey string, task jivo.DialerTask, id types.CallId) error {
	return nil
}

func (d *DialerMockClient) Call(ctx context.Context, b []byte, routingKey string, task jivo.DialerTask, id types.CallId) (res []byte, err error) {
	return d.CallReturn, nil
}

type DialerMockSession struct{}

func (s *DialerMockSession) Open(ctx context.Context) error {
	return nil
}

func (s *DialerMockSession) Close(ctx context.Context) error {
	return nil
}

func (s *DialerMockSession) Message(req jivo.JivoEvent) error {
	return nil
}

type MockDBProvider struct{}

func (db *MockDBProvider) CreateIncomingTask(ctx context.Context, callTask types.ExtendedCallTask) (types.ExtendedCallTask, error) {
	res := callTask
	res.Id = types.CallTaskId(primitive.NewObjectID().Hex())
	return res, nil
}

func (db *MockDBProvider) UpdateTask(ctx context.Context, taskId types.CallTaskId, update types.M) (int, error) {
	return 1, nil
}

func (db *MockDBProvider) CreateRecord(ctx context.Context, callRecord types.CallResult) error {
	return nil
}

func TestHandleBot(t *testing.T) {
	dat, err := os.ReadFile("clientMessage.json")
	if err != nil {
		t.Error(err)
		return
	}
	var jivoEvent jivo.JivoEvent
	err = json.Unmarshal(dat, &jivoEvent)
	if err != nil {
		t.Error(err)
		return
	}
	b, err := json.Marshal(types.JivoData{InstanceQueue: "testqueue"})
	if err != nil {
		t.Error(err)
		return
	}
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	campaignCh := make(chan chan types.Campaign)
	go func() {
		campaign := types.Campaign{
			IsActive: true,
		}
		for {
			select {
			case <-ctx.Done():
				return
			case resCh := <-campaignCh:
				resCh <- campaign
			}
		}
	}()
	handler := jivo.HandleEvent(
		ctx,
		campaignCh,
		&DialerMockClient{CallReturn: b},
		&MockDBProvider{},
	)
	req := httptest.NewRequest(http.MethodPost, "http://example.com/66853a6840d5fe038f7118b3", bytes.NewBuffer(dat))
	req.Header.Add(jivo.HeaderKey_ContentType, jivo.ContentType_ApplicationJson)
	w := httptest.NewRecorder()
	handler(w, req)
	res := w.Result()
	if res.StatusCode != http.StatusOK {
		t.Errorf("response code is not ok")
		body, err := io.ReadAll(res.Body)
		if err != nil {
			t.Error(err)
		}
		t.Error(string(body))
	}
}
