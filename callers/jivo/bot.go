package jivo

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/google/uuid"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/mongodb"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

var JivoURL = fmt.Sprintf("%s/%s/%s", config.Cfg.Jivo.BotApiUri.String(), config.Cfg.Jivo.ProviderId, config.Cfg.Jivo.BotId)

type Chats struct {
	Sessions map[string]Session
	Mu       sync.RWMutex
}

func (c *Chats) Event(
	ctx context.Context,
	campaignCh chan<- chan types.Campaign,
	jivoEvent JivoEvent,
	dialerClient DialerClient,
	dbProvider DBProvider,
	rndGen utils.RandomGenerator,
	logger *log.Logger,
) (err error) {
	c.Mu.RLock()
	session := c.Sessions[jivoEvent.ChatId]
	c.Mu.RUnlock()
	if session != nil {
		logger.SetPrefix(fmt.Sprintf(
			"Id:%s ClientId:%s ChatId:%s CallId:%s ",
			jivoEvent.Id,
			jivoEvent.ClientId,
			jivoEvent.ChatId,
			session.Task().LastCallId,
		))
	}

	switch jivoEvent.Event {
	case JIVO_INC_EVENT_CLIENT_MESSAGE:
		logger.Println(string(jivoEvent.Message))
		now := time.Now()
		if session == nil {
			resCh := make(chan types.Campaign)
			campaignCh <- resCh
			campaign := <-resCh
			if !campaign.IsActive {
				logger.Println("campaign is not active")
				return ErrorInfo{
					HttpCode: http.StatusInternalServerError,
					Code:     ErrorCode_invalidRequest,
					Message:  MessageTemplate_FailedCreateSession,
				}
			}
			callId := types.CallId(rndGen.GenerateId().String())
			logger.SetPrefix(fmt.Sprintf(
				"Id:%s ClientId:%s ChatId:%s CallId:%s ",
				jivoEvent.Id,
				jivoEvent.ClientId,
				jivoEvent.ChatId,
				callId,
			))
			task := types.ExtendedCallTask{
				Project:               campaign.BuildInfo.Project,
				Status:                types.TASK_STATUS_PROGRESS,
				AttemptNumber:         1,
				LastRequestToDialerAt: now,
				UpdatedAt:             now,
				CallData: types.CallData{
					JivoData: &types.JivoData{
						ChatId:   jivoEvent.ChatId,
						ClientId: jivoEvent.ClientId,
						JivoURL:  JivoURL,
					},
				},
				CallTask: types.CallTask{
					CampaignId: campaign.Id,
					ExternalId: jivoEvent.ClientId,
					IsTest:     campaign.IsTest,
					LastCallId: callId,
					Phones:     []types.TaskPhone{},
					CreatedAt:  now,
					NextCallAt: now,
				},
			}
			task, err := dbProvider.CreateIncomingTask(ctx, task)
			if err != nil {
				logger.Println(err)
				return ErrorInfo{
					HttpCode: http.StatusInternalServerError,
					Code:     ErrorCode_invalidRequest,
					Message:  MessageTemplate_FailedCreateSession,
				}
			}
			session = &DialerSession{
				client:   dialerClient,
				campaign: campaign,
				task:     task,
			}
			ctxDB := context.WithoutCancel(ctx)
			if err := session.Open(ctx); err != nil {
				logger.Println(err)
				task := session.Task()
				if _, err := dbProvider.UpdateTask(ctxDB, task.Id, types.M{
					"$set": types.M{
						"status":         types.TASK_STATUS_STOP,
						"lastCallStatus": types.CALL_STATUS_ERROR_AFTER_UP,
					},
				}); err != nil {
					logger.Println(err)
				}
				record := types.CallResult{
					CallTaskId:         task.Id,
					StartTs:            int(now.UnixMilli()),
					EndTs:              int(now.UnixMilli()),
					Duration:           0,
					DurationBefore:     0,
					CampaignName:       campaign.Name,
					StartDatabase:      now,
					EndDatabase:        now,
					TaskAddedDatabase:  now,
					CallWithProblems:   true,
					CampaignId:         campaign.Id,
					CompanyId:          string(campaign.Id),
					CallerQueueId:      string(task.Id),
					IsTest:             task.IsTest,
					BuildInfo:          campaign.BuildInfo,
					Direction:          campaign.CallDirection.ToCallDirection(),
					InterruptionStatus: 0,
					Source:             "caller",
					ErrorMsg:           MessageTemplate_FailedCreateSession,
					CallData: types.CallResultCallData{
						CallDate:      int32(now.Unix()),
						CallerQueueId: string(task.Id),
						Timeout:       campaign.TrunkSettings.Timeout,
						AttemptNumber: task.AttemptNumber,
						ExternalId:    task.ExternalId,
					},
					Attempt: types.Attempt{
						CallId: task.LastCallId,
						Status: types.CALL_STATUS_ERROR_AFTER_UP,
					},
				}
				if err := dbProvider.CreateRecord(ctxDB, record); err != nil {
					logger.Println(err)
				}
				return ErrorInfo{
					HttpCode: http.StatusInternalServerError,
					Code:     ErrorCode_invalidRequest,
					Message:  MessageTemplate_FailedCreateSession,
				}
			}
			c.Mu.Lock()
			c.Sessions[jivoEvent.ChatId] = session
			c.Mu.Unlock()
			go func() {
				timer := time.NewTimer(time.Duration(campaign.TrunkSettings.CallTTL) * time.Second)
				<-timer.C
				eventCtx, cancelEvent := context.WithTimeout(context.WithoutCancel(ctx), time.Minute)
				defer cancelEvent()
				if err := c.Event(
					eventCtx,
					campaignCh,
					JivoEvent{
						Id:       uuid.NewString(),
						Event:    JIVO_INC_EVENT_CHAT_CLOSED,
						ClientId: jivoEvent.ClientId,
						ChatId:   jivoEvent.ChatId,
					},
					dialerClient,
					dbProvider,
					rndGen,
					logger,
				); err != nil {
					logger.Println(err)
				}
			}()
		}
		var message MessageText
		if err := json.Unmarshal(jivoEvent.Message, &message); err != nil {
			logger.Println(err)
			return ErrorInfo{
				HttpCode: http.StatusInternalServerError,
				Code:     ErrorCode_invalidRequest,
				Message:  MessageTemplate_FailedBotMessage,
			}
		}
		if err := session.Message(message.Text); err != nil {
			logger.Println(err)
			return ErrorInfo{
				HttpCode: http.StatusInternalServerError,
				Code:     ErrorCode_invalidRequest,
				Message:  MessageTemplate_FailedBotMessage,
			}
		}

	case JIVO_INC_EVENT_CHAT_CLOSED:
		c.Mu.Lock()
		session := c.Sessions[jivoEvent.ChatId]
		delete(c.Sessions, jivoEvent.ChatId)
		c.Mu.Unlock()
		if session == nil {
			return nil
		}
		session.Task()
		dr, err := session.Close(ctx)
		if err != nil {
			logger.Println(err)
			dr.Error = err.Error()
			dr.Status = types.CALL_STATUS_ERROR_AFTER_UP
		}

		ctxDB := context.WithoutCancel(ctx)
		if dr.DataBaseId == "" {
			now := time.Now()
			campaign := session.Campaign()
			task := session.Task()
			record := types.CallResult{
				StartTs:            int(now.UnixMilli()),
				EndTs:              int(now.UnixMilli()),
				CallTaskId:         task.Id,
				Duration:           dr.Duration,
				DurationBefore:     dr.DurationBefore,
				CampaignName:       campaign.Name,
				StartDatabase:      now,
				EndDatabase:        now,
				TaskAddedDatabase:  now,
				CallWithProblems:   true,
				CampaignId:         campaign.Id,
				CompanyId:          string(campaign.Id),
				CallerQueueId:      string(task.Id),
				IsTest:             task.IsTest,
				BuildInfo:          campaign.BuildInfo,
				Direction:          campaign.CallDirection.ToCallDirection(),
				InterruptionStatus: 0,
				Source:             "caller",
				ErrorMsg:           dr.Error,
				CallData: types.CallResultCallData{
					CallDate:      int32(now.Unix()),
					CallerQueueId: string(task.Id),
					Timeout:       campaign.TrunkSettings.Timeout,
					AttemptNumber: task.AttemptNumber,
					ExternalId:    task.ExternalId,
				},
				Attempt: types.Attempt{
					CallId: task.LastCallId,
					Status: dr.Status,
				},
			}
			if err := dbProvider.CreateRecord(ctxDB, record); err != nil {
				logger.Println(err)
			}
		}
		if _, err := dbProvider.UpdateTask(
			ctxDB,
			session.Task().Id,
			types.M{
				"$set": types.M{
					"status":         types.TASK_STATUS_STOP,
					"lastCallStatus": dr.Status,
				},
			},
		); err != nil {
			logger.Println(err)
		}

	case JIVO_INC_EVENT_CLIENT_RATED:
	case JIVO_INC_EVENT_AGENT_UNAVAILABLE:
	default:
		return ErrorInfo{
			HttpCode: http.StatusBadRequest,
			Code:     ErrorCode_invalidRequest,
			Message:  fmt.Sprintf(MessageTemplate_UnsupportedEvent, jivoEvent.Event.String()),
		}
	}
	return nil
}

func CampaignLoop(ctx context.Context, db mongodb.Client, campaignId types.CampaignId, reqCh <-chan chan types.Campaign) {
	logger := log.Default()
	if campaignId == "" {
		logger.Panic("empty campaignId")
	}
	campaign, err := db.GetCampaignById(ctx, campaignId)
	if err != nil {
		logger.Panic(err)
	}

	campaignDataCh := make(chan types.Campaign)
	go func() {
		ticker := time.NewTicker(time.Second)
		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				if cmp, err := db.GetCampaignById(ctx, campaignId); err != nil {
					logger.Println(err)
				} else {
					campaignDataCh <- cmp
				}
			}
		}
	}()

	for {
		select {
		case <-ctx.Done():
			return
		case resCh := <-reqCh:
			resCh <- campaign
		case campaign = <-campaignDataCh:
		}
	}
}
