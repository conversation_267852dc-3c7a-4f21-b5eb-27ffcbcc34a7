package jivo

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
)

type ErrorResponse struct {
	ErrorInfo ErrorInfo `json:"error"`
}

type ErrorInfo struct {
	HttpCode int    `json:"-"`
	Code     string `json:"code"`
	Message  string `json:"message"`
}

func (e ErrorInfo) Error() string {
	return fmt.Sprintf("code: %s, message: %s", e.Code, e.Message)
}

type JivoEvent struct {
	Id       string          `json:"id"`
	Event    JivoIncEvenv    `json:"event"`
	ClientId string          `json:"client_id"`
	ChatId   string          `json:"chat_id"`
	Message  json.RawMessage `json:"message,omitempty"`
}

type MessageText struct {
	Type      string `json:"type"`
	Text      string `json:"text"`
	Timestamp int64  `json:"timestamp"` // Unix
}

type JivoIncEvenv uint8

var (
	jivoIncEvents = [4]string{
		JIVO_INC_EVENT_CLIENT_MESSAGE:    jivoIncEvent_ClientMessage,
		JIVO_INC_EVENT_AGENT_UNAVAILABLE: jivoIncEvent_AgentUnavailable,
		JIVO_INC_EVENT_CHAT_CLOSED:       jivoIncEvent_ChatClosed,
		JIVO_INC_EVENT_CLIENT_RATED:      jivoIncEvent_ClientRated,
	}
	jivoIncEventMap = map[string]JivoIncEvenv{
		jivoIncEvent_ClientMessage:    JIVO_INC_EVENT_CLIENT_MESSAGE,
		jivoIncEvent_AgentUnavailable: JIVO_INC_EVENT_AGENT_UNAVAILABLE,
		jivoIncEvent_ChatClosed:       JIVO_INC_EVENT_CHAT_CLOSED,
		jivoIncEvent_ClientRated:      JIVO_INC_EVENT_CLIENT_RATED,
	}
)

func (e JivoIncEvenv) String() string {
	return jivoIncEvents[e]
}

func (e JivoIncEvenv) MarshalJSON() ([]byte, error) {
	if int(e) >= len(jivoIncEvents) {
		return nil, fmt.Errorf("invalid value: %d", e)
	}
	return json.Marshal(jivoIncEvents[e])
}

func (e *JivoIncEvenv) UnmarshalJSON(data []byte) error {
	var str string
	if err := json.Unmarshal(data, &str); err != nil {
		return err
	}
	val, ok := jivoIncEventMap[str]
	if !ok {
		return fmt.Errorf("invalid value: %s", str)
	}
	*e = val
	return nil
}

type Session interface {
	Open(ctx context.Context) error
	Close(ctx context.Context) (types.DialerResult, error)
	Message(string) error
	Campaign() types.Campaign
	Task() types.ExtendedCallTask
}

type DialerClient interface {
	Send(b []byte, routingKey string, task DialerTask, id types.CallId) error
	Call(ctx context.Context, b []byte, routingKey string, task DialerTask, id types.CallId) (res []byte, err error)
}

type DBProvider interface {
	CreateIncomingTask(ctx context.Context, callTask types.ExtendedCallTask) (types.ExtendedCallTask, error)
	UpdateTask(ctx context.Context, taskId types.CallTaskId, update types.M) (int, error)
	CreateRecord(ctx context.Context, callRecord types.CallResult) error
}
