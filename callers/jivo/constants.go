package jivo

const (
	ErrorCode_invalidClient      = "invalid_client"
	ErrorCode_unauthorizedClient = "unauthorized_client"
	ErrorCode_invalidRequest     = "invalid_request"
)

const (
	MessageTemplate_MethodNotAllowed     = "method %s not allowed"
	MessageTemplate_UnsupportedMediaType = "unsupported media type %s"
	MessageTemplate_WrongScheme          = "wrong scheme"
	MessageTemplate_UnsupportedEvent     = "unsupported event %s"
	MessageTemplate_FailedBotMessage     = "failed bot message"
	MessageTemplate_FailedCreateSession  = "failed create session"
)

const (
	HeaderKey_ContentType = "Content-type"
)

const (
	ContentType_ApplicationJson = "application/json"
)

const (
	jivoIncEvent_ClientMessage    = "CLIENT_MESSAGE"
	jivoIncEvent_AgentUnavailable = "AGENT_UNAVAILABLE"
	jivoIncEvent_ChatClosed       = "CHAT_CLOSED"
	jivoIncEvent_ClientRated      = "CLIENT_RATED"
)

const (
	JIVO_INC_EVENT_CLIENT_MESSAGE JivoIncEvenv = iota
	JIVO_INC_EVENT_AGENT_UNAVAILABLE
	JIVO_INC_EVENT_CHAT_CLOSED
	JIVO_INC_EVENT_CLIENT_RATED
)
