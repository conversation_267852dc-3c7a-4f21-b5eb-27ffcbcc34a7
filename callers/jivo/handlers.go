package jivo

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"sync"
	"time"

	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
)

type RequestData struct {
	Time time.Time
	Res  chan error
}

func HandleEvent(
	ctx context.Context,
	campaignCh chan<- chan types.Campaign,
	dialerClient DialerClient,
	dbProvider DBProvider,
) func(w http.ResponseWriter, r *http.Request) {
	var processedMu sync.Mutex
	processed := map[string]RequestData{}
	rndGen := utils.NewRandomGenerator(ctx)
	go func() {
		ticker := time.NewTicker(time.Minute)
		defer ticker.Stop()
		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				processedMu.Lock()
				for k, v := range processed {
					if time.Since(v.Time) > time.Minute {
						delete(processed, k)
					}
				}
				processedMu.Unlock()
			}
		}
	}()
	chats := Chats{Sessions: map[string]Session{}}
	return func(w http.ResponseWriter, r *http.Request) {
		logger := log.New(os.Stdout, "", log.Ldate|log.Ltime)
		logger.Printf("%s %s\n", r.URL, r.Method)
		err := func() (err error) {
			err = ValidateJivoRequest(r)
			if err != nil {
				logger.Println(err)
				return err
			}
			jivoEvent, err := DecodeJivoEvent(r)
			if err != nil {
				logger.Println(err)
				return err
			}
			logger.SetPrefix(fmt.Sprintf(
				"Id:%s ClientId:%s ChatId:%s ",
				jivoEvent.Id,
				jivoEvent.ClientId,
				jivoEvent.ChatId,
			))
			logger.Println(jivoEvent.Event)
			processedMu.Lock()
			if reqDat, ok := processed[jivoEvent.Id]; ok {
				processedMu.Unlock()
				logger.Println("event already recieved")
				errRes := <-reqDat.Res
				reqDat.Res <- errRes
				return errRes
			}
			ch := make(chan error, 1)
			processed[jivoEvent.Id] = RequestData{
				Time: time.Now(),
				Res:  ch,
			}
			processedMu.Unlock()
			go func() {
				ctxEvent, cancelEvent := context.WithTimeout(ctx, time.Minute)
				ch <- chats.Event(ctxEvent, campaignCh, jivoEvent, dialerClient, dbProvider, rndGen, logger)
				cancelEvent()
			}()
			err = <-ch
			ch <- err
			return err
		}()
		WriteJivoResponse(w, err, logger)
	}
}

func WriteJivoResponse(w http.ResponseWriter, err error, logger *log.Logger) {
	w.Header().Add(HeaderKey_ContentType, ContentType_ApplicationJson)
	switch errInfo := err.(type) {
	case nil:
		logger.Println(http.StatusOK)
	case ErrorInfo:
		errResponse := ErrorResponse{ErrorInfo: errInfo}
		body, err := json.Marshal(errResponse)
		if err != nil {
			logger.Panic(err)
		}
		http.Error(w, string(body), errInfo.HttpCode)
		logger.Println(errInfo.HttpCode, string(body))
	default:
		errMsg := "internal error"
		http.Error(w, errMsg, http.StatusInternalServerError)
		logger.Println(http.StatusInternalServerError, errMsg)
	}
}

func ValidateJivoRequest(r *http.Request) error {
	if r.Method != http.MethodPost {
		return ErrorInfo{
			HttpCode: http.StatusMethodNotAllowed,
			Code:     ErrorCode_invalidRequest,
			Message:  fmt.Sprintf(MessageTemplate_MethodNotAllowed, r.Method),
		}
	}

	if contentType := r.Header.Get(HeaderKey_ContentType); contentType != ContentType_ApplicationJson {
		return ErrorInfo{
			HttpCode: http.StatusUnsupportedMediaType,
			Code:     ErrorCode_invalidRequest,
			Message:  fmt.Sprintf(MessageTemplate_UnsupportedMediaType, contentType),
		}
	}
	return nil
}

func DecodeJivoEvent(r *http.Request) (jivoEvent JivoEvent, err error) {
	err = json.NewDecoder(r.Body).Decode(&jivoEvent)
	if err != nil {
		return jivoEvent, ErrorInfo{
			HttpCode: http.StatusBadRequest,
			Code:     ErrorCode_invalidRequest,
			Message:  MessageTemplate_WrongScheme,
		}
	}
	return jivoEvent, nil
}
