package tg_caller

import (
	"context"
	"errors"
	"fmt"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/tg_caller/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/telegram"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	logger "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/logger/typed_logger"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/utils"
	"strings"
	"sync"
	"time"
)

const (
	CallerQueueBotTemplate = "kv.caller.telegram.%s.%s"
	// campaignId + buildId
	DialerQueueTemplate = "kv.dialer.%s.%s.web"
)

var BotNotFoundError = errors.New("bot not found")

type (
	TgCaller struct {
		tgProvider   ITelegramProvider
		tgRepo       ITelegramRepo
		dialerClient IDialerClient
		log          *logger.Logger
		ctx          context.Context
		ctxMu        sync.RWMutex
		wg           sync.WaitGroup
		botList      map[string]*TelegramBot
		blMu         sync.RWMutex
		chResults    chan *types.DialerResultResponse
		callIdGen    utils.RandomGenerator
		mon          *TgMonitoring
	}
)

func New(provider ITelegramProvider, repo ITelegramRepo, dialerClient IDialerClient, mon *TgMonitoring) *TgCaller {
	return &TgCaller{
		tgProvider:   provider,
		tgRepo:       repo,
		dialerClient: dialerClient,
		log:          &logger.DefaultLogger,
		mon:          mon,
		wg:           sync.WaitGroup{},
		botList:      make(map[string]*TelegramBot, 1),
		ctxMu:        sync.RWMutex{},
	}
}

func (t *TgCaller) Run(ctx context.Context) error {
	t.log.Info("tg caller init...")
	t.ctxMu.Lock()
	t.ctx = ctx
	t.ctxMu.Unlock()
	t.callIdGen = utils.NewRandomGenerator(ctx)

	botInfoList, err := t.tgRepo.GetBotInfoList(ctx)
	if err != nil {
		return err
	}

	t.blMu.Lock()
	for _, bi := range botInfoList {
		ctxBot, cancel := context.WithCancel(t.ctx)
		t.botList[bi.Token] = &TelegramBot{
			Token:      bi.Token,
			CampaignId: bi.CampaignId,
			BuildId:    bi.BuildId,
			ctx:        ctxBot,
			ctxCancel:  cancel,
		}
	}
	t.blMu.Unlock()

	go t.handleAlerts()

	t.initTelegram()
	t.initDialer()

	t.blMu.RLock()
	for _, tgBot := range t.botList {
		tgBot := tgBot
		t.wg.Add(2)
		go func() {
			defer t.wg.Done()
			t.handleBotMessages(tgBot)
		}()
		go func() {
			defer t.wg.Done()
			t.handleDialerMessages(tgBot)
		}()
	}
	t.blMu.RUnlock()

	t.wg.Add(1)
	go func() {
		defer t.wg.Done()
		t.handleResults(t.ctx)
	}()

	t.log.Info("tg caller started")
	t.wg.Wait()
	t.log.Info("tg caller stopped")
	return nil
}

func (t *TgCaller) StopBot(token string) error {
	t.blMu.Lock()
	defer t.blMu.Unlock()
	bot, ok := t.botList[token]
	if !ok {
		return BotNotFoundError
	}
	bot.ctxCancel()
	delete(t.botList, token)
	return nil
}

func (t *TgCaller) StopBotByCampaign(campaignId string) error {
	var token string
	t.blMu.RLock()
	for t, tgBot := range t.botList {
		if tgBot.CampaignId == campaignId {
			token = t
			break
		}
	}
	t.blMu.RUnlock()

	if token == "" {
		return fmt.Errorf("can`t stop bot by campaignId %s: bot not found %w", campaignId, BotNotFoundError)
	}

	return t.StopBot(token)
}

func (t *TgCaller) RunBot(botInfo types.BotInfo) error {
	t.ctxMu.RLock()
	ctxBot, cancel := context.WithCancel(t.ctx)
	t.ctxMu.RUnlock()
	tgBot := &TelegramBot{
		Token:      botInfo.Token,
		CampaignId: botInfo.CampaignId,
		BuildId:    botInfo.BuildId,
		ctx:        ctxBot,
		ctxCancel:  cancel,
	}

	chIn, chOut, botId, err := t.tgProvider.RunBot(tgBot.ctx, tgBot.Token)
	if err != nil {
		cancel()
		return err
	}

	tgBot.Id = botId
	tgBot.TgChIn = chIn
	tgBot.TgChOut = chOut
	tgBot.CallerQueue = fmt.Sprintf(CallerQueueBotTemplate, tgBot.CampaignId, tgBot.BuildId)
	tgBot.Sessions = make(map[int64]*Session)
	tgBot.SessionChatMap = make(map[string]int64)

	chDialer, err := t.dialerClient.RunConsumer(tgBot.ctx, tgBot.CallerQueue)
	if err != nil {
		cancel()
		return err
	}

	tgBot.DialerChIn = chDialer

	t.blMu.Lock()
	t.botList[botInfo.Token] = tgBot
	t.blMu.Unlock()

	t.wg.Add(2)
	go func() {
		defer t.wg.Done()
		t.handleBotMessages(tgBot)
	}()
	go func() {
		defer t.wg.Done()
		t.handleDialerMessages(tgBot)
	}()

	return nil
}

func (t *TgCaller) handleAlerts() {
	for {
		select {
		case <-t.ctx.Done():
			return
		case alert := <-t.mon.AlertsCh():
			// todo как-то обрабатывать алерты
			switch alert.alert {
			default:
				t.log.Error("alert: %s", AlertMap[alert.alert])
			}
		}
	}
}

func (t *TgCaller) initTelegram() {
	t.log.Info("telegram listener preparing...")

	t.blMu.Lock()
	defer t.blMu.Unlock()
	for token, tgBot := range t.botList {
		chIn, chOut, botId, err := t.tgProvider.RunBot(tgBot.ctx, tgBot.Token)
		if err != nil {
			t.log.TraceError(err)
		}
		t.botList[token].Id = botId
		t.botList[token].TgChIn = chIn
		t.botList[token].TgChOut = chOut
		t.botList[token].CallerQueue = fmt.Sprintf(CallerQueueBotTemplate, tgBot.CampaignId, tgBot.BuildId)
		t.botList[token].Sessions = make(map[int64]*Session)
		t.botList[token].SessionChatMap = make(map[string]int64)
	}
	t.log.Info("telegram initialized")
}

func (t *TgCaller) handleBotMessages(tgBot *TelegramBot) {
	for {
		select {
		case <-tgBot.ctx.Done():
			t.log.Info("bot listener stopped. campaign: %s", tgBot.CampaignId)
			return
		case msg, ok := <-tgBot.TgChIn:
			if !ok {
				return
			}
			t.log.Debug("got message from tg: %+v", msg)
			t.mon.MessageReceived(tgBot.Id)
			session, ok := tgBot.GetSession(msg.ChatId)
			if !ok {
				if msg.Text[:6] == "/start" {
					_ = msg.Text
					// todo читать идентификатор из диплинка и попытаться идентифицировать пользователя
				}

				// Коллер отправляет `таск` и получает ответ в виде `func: create_session`. Отдельный запрос create_session не нужен
				// потому что в ответ на таск уже нужно получить точную очередь instance_queue и это возможно сделать только одним запросом
				sessionId := t.callIdGen.GenerateId().String()
				session = tgBot.CreateSession(msg.ChatId, sessionId)
				if err := t.createDialerSession(session, tgBot); err != nil {
					t.log.TraceError(err)
					tgBot.CloseSession(sessionId)
					// или return
					continue
				}
			}

			// ignore start command
			if msg.Text[:6] == "/start" {
				continue
			}

			select {
			case session.ChIn <- msg:
			default:
				t.log.Error("session buffer is full, taskId: %s", session.SessionId)
			}
		}
	}
}

func (t *TgCaller) createDialerSession(session *Session, tgBot *TelegramBot) error {
	msgCreateSession := &types.DialerTGMessageOut{
		Func:      telegram.FuncDialingTask,
		TaskId:    session.TaskId,
		SessionId: session.SessionId,
		ReplyTo:   tgBot.CallerQueue,
	}
	queue := fmt.Sprintf(DialerQueueTemplate, tgBot.CampaignId, tgBot.BuildId)
	if err := t.dialerClient.SendTask(msgCreateSession, queue); err != nil {
		return err
	}

	t.wg.Add(1)
	go func() {
		defer t.wg.Done()
		t.handleSession(session, tgBot)
	}()
	return nil
}

func (t *TgCaller) handleSession(session *Session, tgBot *TelegramBot) {
	timer := time.NewTimer(time.Duration(config.Cfg.TgCaller.CreateSessionTimeout) * time.Second)
	select {
	case <-tgBot.ctx.Done():
		timer.Stop()
		return
	case <-timer.C:
		t.mon.CreateSessionTimeout(tgBot.Id, session.SessionId)
		tgBot.CloseSession(session.SessionId)
		t.log.Error("create session timeout for task %s", session.SessionId)
	case <-session.StartCh:
		t.mon.SessionStarted(tgBot.Id)

		doneCh := make(chan struct{}, 1)
		go func() {
			for {
				select {
				case <-tgBot.ctx.Done():
					return
				case <-doneCh:
					return
				default:
					if err := session.limiter.Wait(tgBot.ctx); err != nil {
						t.log.Error("income msg limiter for session %s error: %s", session.SessionId, err)
						return
					}

					// Команда отправляется отдельно от сообщений
					cmd, ok := session.lastCmd.Load().(string)
					if ok && cmd != "" {
						session.lastCmd.Store("")
						msgToDialer := &types.DialerTGMessageOut{
							Func:      telegram.FuncHandleAction,
							SessionId: session.SessionId,
							TaskId:    session.TaskId,
							ReplyTo:   tgBot.CallerQueue,
							Text:      cmd,
						}

						session.AddLog(types.TSLogTypeUserCommand, cmd, telegram.FuncHandleAction)

						if err := t.dialerClient.SendMessage(msgToDialer, session.DialerQueue); err != nil {
							t.mon.DialerError(tgBot.Id, session.SessionId, err)
							t.log.TraceError(err)
						}

						continue
					}

					// Достаём все накопленные сообщения
					session.bufMu.Lock()
					if len(session.buffer) > 0 {
						merged := strings.Join(session.buffer, " ")
						session.buffer = session.buffer[:0] // Очистка буфера
						session.bufMu.Unlock()

						if len(merged) > config.Cfg.TgCaller.MaxTgMsgLength {
							merged = merged[:config.Cfg.TgCaller.MaxTgMsgLength]
						}

						//Отправляем склеенное сообщение
						msgToDialer := &types.DialerTGMessageOut{
							Func:      telegram.FuncHandleMessage,
							SessionId: session.SessionId,
							TaskId:    session.TaskId,
							Text:      merged,
							ReplyTo:   tgBot.CallerQueue,
						}

						session.AddLog(types.TSLogTypeUserMessage, merged, telegram.FuncHandleMessage)

						if err := t.dialerClient.SendMessage(msgToDialer, session.DialerQueue); err != nil {
							t.mon.DialerError(tgBot.Id, session.SessionId, err)
							t.log.TraceError(err)
						}
					} else {
						session.bufMu.Unlock()
					}
				}
			}
		}()

		// копит быстро отправленные сообщения в буфере
		for {
			select {
			case <-tgBot.ctx.Done():
				return
			case msg, ok := <-session.ChIn:
				if !ok {
					return
				}
				t.log.Debug("got message from session: %+v", msg)
				session.LastActivity = time.Now()
				if msg.IsCmd {
					session.lastCmd.Store(msg.Text)
				} else {
					session.bufMu.Lock()
					session.buffer = append(session.buffer, msg.Text)
					session.bufMu.Unlock()
				}

			case <-session.CloseCh:
				if err := t.tgRepo.SaveSessionLogs(tgBot.CampaignId, session.ChatId, session.SessionId, session.GetLogs()); err != nil {
					t.log.TraceError(err)
				}
				tgBot.CloseSession(session.SessionId)
				t.mon.SessionClosed(tgBot.Id)
				doneCh <- struct{}{}
			}
		}
	}
}

func (t *TgCaller) initDialer() {
	t.log.Info("dialer consumer preparing...")

	t.blMu.Lock()
	for token, tgBot := range t.botList {
		ch, err := t.dialerClient.RunConsumer(tgBot.ctx, tgBot.CallerQueue)
		if err != nil {
			t.log.TraceError(err)
			continue
		}
		t.botList[token].DialerChIn = ch
	}
	t.blMu.Unlock()

	chResult, err := t.dialerClient.ConsumeResults(t.ctx)
	if err != nil {
		t.log.TraceError(err)
	}
	t.chResults = chResult
	t.log.Info("dialer initialized")
}

func (t *TgCaller) handleDialerMessages(tgBot *TelegramBot) {
	for {
		select {
		case <-tgBot.ctx.Done():
			t.log.Info("bot listener stopped")
			return
		case msg, ok := <-tgBot.DialerChIn:
			if !ok {
				return
			}

			t.log.Debug("got message from bot: %+v", msg)
			session, ok := tgBot.GetSessionByTask(msg.SessionId)
			if !ok {
				t.log.Error("session %s not found", msg.SessionId)
				continue
			}

			session.AddLog(types.TSLogTypeBotMessage, msg.Text, msg.Func)

			if msg.Func == telegram.FuncCreateSession {
				session.DialerQueue = msg.DialerQueue
				session.Start()
			} else if msg.IsFinalState {
				msgToDialer := &types.DialerTGMessageOut{
					Func:      telegram.FuncCloseSession,
					SessionId: session.SessionId,
					TaskId:    session.TaskId,
					ReplyTo:   tgBot.CallerQueue,
				}
				if err := t.dialerClient.SendMessage(msgToDialer, session.DialerQueue); err != nil {
					t.log.TraceError(err)
				}
			} else if msg.Func == telegram.FuncCloseSession {
				session.Close()
				// todo сохранить результат
				t.log.Info("session %s closed by dialer, raw msg %+v", msg.SessionId, msg)
			}

			if msg.Text != "" {
				tgBot.scMu.RLock()
				chatId, ok := tgBot.SessionChatMap[msg.SessionId]
				tgBot.scMu.RUnlock()
				if !ok {
					t.log.Error("chatId for session_id %s not found", msg.SessionId)
					continue
				}
				tgMsg := &types.TGMessageOut{
					ChatId: chatId,
					Text:   msg.Text,
				}
				tgBot.TgChOut <- tgMsg
				t.mon.MessageSent(tgBot.Id)
			}
		}
	}
}

func (t *TgCaller) handleResults(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			t.log.Info("results listener stopped")
			return
		case msg := <-t.chResults:
			t.log.Debug("got result message from dialer: %+v", msg)
			// todo куда-то сохранять это
		}
	}
}
