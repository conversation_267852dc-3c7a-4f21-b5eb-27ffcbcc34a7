package tg_caller

import (
	"context"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/tg_caller/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/telegram"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	logger "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/logger/typed_logger"
	"go.uber.org/goleak"
	"os"
	"testing"
	"time"
)

const DefaultShortDelay = 100 * time.Millisecond

func TestMain(m *testing.M) {
	goleak.VerifyTestMain(m)
}

func TestSimple(t *testing.T) {
	var (
		log          = logger.DefaultLogger
		tgProvider   ITelegramProvider
		tgRepo       ITelegramRepo
		dialerClient IDialerClient
		ctx, cancel  = context.WithCancel(context.Background())
	)

	config.Cfg.LimitSecondsPerTgMessageIn = 0

	mapBotInfoTest := []*types.BotInfo{
		{
			Token:      "token1",
			CampaignId: "camp1",
			BuildId:    "build1",
		},
		{
			Token:      "token2",
			CampaignId: "camp2",
			BuildId:    "build2",
		},
	}
	tgRepo = telegram.NewTelegramRepoMock(mapBotInfoTest) //mongodb.NewMongoProvider(ctxMongo, config.Cfg.MongoUri, config.Cfg.MongoDbName, nil)

	// каждый тг-бот получит этот набор сообщений
	// последнее сообщение в сессии должно быть "finish"
	// для одного и тогоже чата должно быть одинаковое количество сообщений в каждой сессии
	tgTestMessages := []*telegram.TgMsgMock{
		// чат 1 сессия 1
		{Msg: &types.TGMessageIn{
			ChatId: 1,
			Text:   "text from tg test11",
		}},
		{Msg: &types.TGMessageIn{
			ChatId: 1,
			Text:   "text from tg test12",
		}},
		{Msg: &types.TGMessageIn{
			ChatId: 1,
			Text:   "finish",
		}},
		// чат 1 сессия 2
		{Msg: &types.TGMessageIn{
			ChatId: 1,
			Text:   "text from tg test13",
		}},
		{Msg: &types.TGMessageIn{
			ChatId: 1,
			Text:   "text from tg test14",
		}},
		{Msg: &types.TGMessageIn{
			ChatId: 1,
			Text:   "finish",
		}},
		// чат 2 сессия 1
		{Msg: &types.TGMessageIn{
			ChatId: 2,
			Text:   "text from tg test21",
		}},
		{Msg: &types.TGMessageIn{
			ChatId: 2,
			Text:   "finish",
		}},
	}

	// ожидаемое количество сессий суммарно на всех ботов
	// key = chatId, value = expected count
	expectedSessionCount := map[int64]int{
		1: 4,
		2: 2,
	}

	// на каждое сообщение из телеги приходит сообщение из дайлера + 2 на создание/закрытие сессии
	// ожидаемый размер логов сессии = (количество сообщений * 2 + 2)
	expectedSessionLogSize := map[int64]int{
		1: 8,
		2: 6,
	}

	tgProvider = telegram.NewTelegramProviderMock(tgTestMessages)

	dialerClient = telegram.NewDialerClientMock(ctx)
	mon := NewTgMonitoring(NewStdMetricStorage(os.Stdout))
	mon.Run(ctx)

	tgCaller := New(tgProvider, tgRepo, dialerClient, mon)
	go func() {
		if err := tgCaller.Run(ctx); err != nil {
			log.Error("%s", err)
		}
	}()

	log.Info("service started")
	time.Sleep(2 * time.Second)

	logs, scMap := tgRepo.(*telegram.TelegramRepoMock).GetLogs()
	checkLogs(t, logs, scMap, expectedSessionCount, expectedSessionLogSize)
	cancel()
	time.Sleep(100 * time.Millisecond)
}

func TestRateLimiter(t *testing.T) {
	var (
		ctx, cancel  = context.WithCancel(context.Background())
		log          = logger.DefaultLogger
		tgProvider   ITelegramProvider
		tgRepo       ITelegramRepo
		dialerClient IDialerClient
	)

	// первое сообщение отправится как есть, все следующие сообщения отправленные меньше чем за 1 секунду будут склеены в одно
	config.Cfg.LimitSecondsPerTgMessageIn = 1

	mapBotInfoTest := []*types.BotInfo{
		{
			Token:      "token1",
			CampaignId: "camp1",
			BuildId:    "build1",
		},
		{
			Token:      "token2",
			CampaignId: "camp2",
			BuildId:    "build2",
		},
	}
	tgRepo = telegram.NewTelegramRepoMock(mapBotInfoTest) //mongodb.NewMongoProvider(ctxMongo, config.Cfg.MongoUri, config.Cfg.MongoDbName, nil)

	// каждый тг-бот получит этот набор сообщений
	tgTestMessages := []*telegram.TgMsgMock{
		// чат 1 сессия 1 - 3 сообщения
		{
			// задержка перед отправкой
			Delay: 0,
			Msg: &types.TGMessageIn{
				ChatId: 1,
				Text:   "text from tg test11",
			},
		},
		// следующие 2 сообщения должны склеиться в одно из-за ожидания лимитера
		{
			Delay: DefaultShortDelay,
			Msg: &types.TGMessageIn{
				ChatId: 1,
				Text:   "text from tg test12",
			},
		},
		{
			Delay: DefaultShortDelay,
			Msg: &types.TGMessageIn{
				ChatId: 1,
				Text:   "text from tg test12",
			},
		},
		{
			Delay: time.Second,
			Msg: &types.TGMessageIn{
				ChatId: 1,
				Text:   "finish",
			},
		},
		// чат 2 сессия 1 - 3 сообщения
		{
			Delay: 0,
			Msg: &types.TGMessageIn{
				ChatId: 2,
				Text:   "text from tg test21",
			},
		},
		{
			Delay: DefaultShortDelay,
			Msg: &types.TGMessageIn{
				ChatId: 2,
				Text:   "text from tg test22",
			},
		},
		{
			Delay: time.Second,
			Msg: &types.TGMessageIn{
				ChatId: 2,
				Text:   "finish",
			},
		},
	}

	// ожидаемое количество сессий суммарно на всех ботов
	// key = chatId, value = expected count
	expectedSessionCount := map[int64]int{
		1: 2,
		2: 2,
	}

	// на каждое сообщение из телеги приходит сообщение из дайлера + 2 на создание/закрытие сессии
	// ожидаемый размер логов сессии = (количество сообщений * 2 + 2)
	expectedSessionLogSize := map[int64]int{
		1: 8,
		2: 8,
	}

	tgProvider = telegram.NewTelegramProviderMock(tgTestMessages)

	dialerClient = telegram.NewDialerClientMock(ctx)
	mon := NewTgMonitoring(NewStdMetricStorage(os.Stdout))
	mon.Run(ctx)

	tgCaller := New(tgProvider, tgRepo, dialerClient, mon)
	go func() {
		if err := tgCaller.Run(ctx); err != nil {
			log.Error("%s", err)
		}
	}()

	log.Info("service started")
	time.Sleep(5 * time.Second)

	logs, scMap := tgRepo.(*telegram.TelegramRepoMock).GetLogs()
	checkLogs(t, logs, scMap, expectedSessionCount, expectedSessionLogSize)
	cancel()
	time.Sleep(100 * time.Millisecond)
}

func TestController(t *testing.T) {
	var (
		log          = logger.DefaultLogger
		tgProvider   ITelegramProvider
		tgRepo       ITelegramRepo
		dialerClient IDialerClient
		ctx, cancel  = context.WithCancel(context.Background())
	)

	config.Cfg.LimitSecondsPerTgMessageIn = 0

	mapBotInfoTest := []*types.BotInfo{
		{
			Token:      "token1",
			CampaignId: "camp1",
			BuildId:    "build1",
		},
		{
			Token:      "token2",
			CampaignId: "camp2",
			BuildId:    "build2",
		},
	}
	tgRepo = telegram.NewTelegramRepoMock(mapBotInfoTest) //mongodb.NewMongoProvider(ctxMongo, config.Cfg.MongoUri, config.Cfg.MongoDbName, nil)

	// каждый тг-бот получит этот набор сообщений
	// последнее сообщение в сессии должно быть "finish"
	// для одного и тогоже чата должно быть одинаковое количество сообщений в каждой сессии
	tgTestMessages := []*telegram.TgMsgMock{
		// чат 1 сессия 1
		{Msg: &types.TGMessageIn{
			ChatId: 1,
			Text:   "text from tg test11",
		}},
		{Msg: &types.TGMessageIn{
			ChatId: 1,
			Text:   "text from tg test12",
		}},
		{Msg: &types.TGMessageIn{
			ChatId: 1,
			Text:   "finish",
		}},
		// чат 1 сессия 2
		{Msg: &types.TGMessageIn{
			ChatId: 1,
			Text:   "text from tg test13",
		}},
		{Msg: &types.TGMessageIn{
			ChatId: 1,
			Text:   "text from tg test14",
		}},
		{Msg: &types.TGMessageIn{
			ChatId: 1,
			Text:   "finish",
		}},
		// чат 2 сессия 1
		{Msg: &types.TGMessageIn{
			ChatId: 2,
			Text:   "text from tg test21",
		}},
		{Msg: &types.TGMessageIn{
			ChatId: 2,
			Text:   "finish",
		}},
	}

	tgProvider = telegram.NewTelegramProviderMock(tgTestMessages)

	dialerClient = telegram.NewDialerClientMock(ctx)
	mon := NewTgMonitoring(NewStdMetricStorage(os.Stdout))
	mon.Run(ctx)

	tgCaller := New(tgProvider, tgRepo, dialerClient, mon)

	go func() {
		if err := tgCaller.Run(ctx); err != nil {
			log.Error("%s", err)
		}
	}()

	time.Sleep(100 * time.Millisecond)

	if err := tgCaller.RunBot(types.BotInfo{
		Token:      "token3",
		CampaignId: "camp3",
		BuildId:    "build3",
	}); err != nil {
		t.Errorf("failed to run bot: %s", err)
	}

	// ожидаемое количество сессий суммарно на всех ботов
	// key = chatId, value = expected count
	expectedSessionCount := map[int64]int{
		1: 6,
		2: 3,
	}

	// на каждое сообщение из телеги приходит сообщение из дайлера + 2 на создание/закрытие сессии
	// ожидаемый размер логов сессии = (количество сообщений * 2 + 2)
	expectedSessionLogSize := map[int64]int{
		1: 8,
		2: 6,
	}

	log.Info("service started")
	time.Sleep(2 * time.Second)
	if err := tgCaller.StopBotByCampaign("camp3"); err != nil {
		t.Errorf("failed to stop bot: %s", err)
	}
	time.Sleep(100 * time.Millisecond)

	logs, scMap := tgRepo.(*telegram.TelegramRepoMock).GetLogs()
	checkLogs(t, logs, scMap, expectedSessionCount, expectedSessionLogSize)
	cancel()
	time.Sleep(100 * time.Millisecond)
}

func checkLogs(t *testing.T, logs map[string][]types.TgSessionLog, sessionChatMap map[string]int64, expSessionCount, expSessionLogSize map[int64]int) {
	sessionCount := make(map[int64]int)
	for session, log := range logs {
		chatId, ok := sessionChatMap[session]
		if !ok {
			t.Errorf("session %s not found in sessionChatMap", session)
		}
		if len(log) != expSessionLogSize[chatId] {
			t.Errorf("chat %d expected %d messages, got %d", chatId, expSessionLogSize[chatId], len(log))
		}
		sessionCount[chatId]++
	}
	for chatId, count := range expSessionCount {
		if count != sessionCount[chatId] {
			t.Errorf("chat %d expected %d sessions, got %d", chatId, count, sessionCount[chatId])
		}
	}
}
