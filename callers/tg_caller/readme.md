# Telegram caller

- Запускает телеграм-ботов и отправляет сообщения телеги от пользователя в дайлер и обратно

## Запуск

```bash
go run cmd/tg_caller/main.go
```

## Управление

Управление ботами происходит через rabbitmq. Для этого нужно отправить сообщение в очередь `tg.caller.control`.
### Запуск бота
Чтобы запустить своего бота, нужно отправить в rabbitmq сообщение вида:

```json
{
  "type": "run_bot",
  "data": {
    "Token": "токен бота",
    "CampaignID": "идентификатор кампании",
    "BuildId": "идентификатор билда"
  }
}
```

### Остановка бота
Чтобы остановить бота, нужно отправить в rabbitmq сообщение вида:

```json
{
  "type": "stop_bot",
  "data": {
    "Token": "токен бота"
  }
}
```

## Тесты

```bash
go test -race ./services/tg_caller/...
```