package tg_caller

import (
	"context"
	"github.com/wagslane/go-rabbitmq"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/tg_caller/types"
	rmq "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/telegram"
)

const (
	ControlQueue = "tg.caller.control"

	CommandRunBot  = "run_bot"
	CommandStopBot = "stop_bot"
)

type (
	CallerCommand struct {
		Command string            `json:"command"`
		Data    CallerCommandData `json:"data"`
	}

	CallerCommandData struct {
		Token      string `json:"token"`
		CampaignId string `json:"campaign_id"`
		BuildId    string `json:"build_id"`
	}

	Controller struct {
		tgCaller *TgCaller
		rmqConn  *rabbitmq.Conn
	}
)

func NewController(tgCaller *TgCaller, rmqConn *rabbitmq.Conn) Controller {
	return Controller{tgCaller: tgCaller, rmqConn: rmqConn}
}

func (c *Controller) Run(ctx context.Context) error {
	c.tgCaller.log.Info("starting bot controller...")

	cons, err := rmq.RmqConsumerAutoAckNozip(c.rmqConn, ControlQueue, func(payload CallerCommand, err error) {
		c.tgCaller.log.Debug("[tg controller] consumed value: %+v", payload)

		switch payload.Command {
		case CommandRunBot:
			c.tgCaller.log.Info("received command to run bot for campaign: %s", payload.Data.CampaignId)
			err := c.tgCaller.RunBot(types.BotInfo{
				Token:      payload.Data.Token,
				CampaignId: payload.Data.CampaignId,
				BuildId:    payload.Data.BuildId,
			})
			if err != nil {
				c.tgCaller.log.Error("failed to run bot: %s", err)
			} else {
				c.tgCaller.log.Info("bot started successfully for campaign: %s", payload.Data.CampaignId)
			}
		case CommandStopBot:
			c.tgCaller.log.Info("received command to stop bot for campaign: %s", payload.Data.CampaignId)
			if err := c.tgCaller.StopBot(payload.Data.Token); err != nil {
				c.tgCaller.log.Error("failed to stop bot: %s", err)
			} else {
				c.tgCaller.log.Info("bot stopped successfully: %s", payload.Data.Token)
			}
		}
	})
	if err != nil {
		return err
	}
	go func() {
		<-ctx.Done()
		c.tgCaller.log.Info("tg controller stopped")
		cons.Close()
	}()

	return nil
}
