package tg_caller

//
//import (
//	"context"
//	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/clients/telegram"
//	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
//	logger "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/logger/typed_logger"
//	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
//	"os"
//	"testing"
//	"time"
//)
//
//func BenchmarkTgCaller(b *testing.B) {
//	var (
//		log          = logger.DefaultLogger
//		tgProvider   ITelegramProvider
//		tgRepo       ITelegramRepo
//		dialerClient IDialerClient
//		ctx, cancel  = context.WithCancel(context.Background())
//	)
//
//	// отключаем лимитер
//	config.Cfg.LimitSecondsPerTgMessageIn = 0
//
//	mapBotInfoTest := []*types.BotInfo{
//		{
//			Token:      "token1",
//			CampaignId: "camp1",
//			BuildId:    "build1",
//		},
//		{
//			Token:      "token2",
//			CampaignId: "camp2",
//			BuildId:    "build2",
//		},
//	}
//	tgRepo = telegram.NewTelegramRepoMock(mapBotInfoTest) //mongodb.NewMongoProvider(ctxMongo, config.Cfg.MongoUri, config.Cfg.MongoDbName, nil)
//
//	// каждый тг-бот получит этот набор сообщений
//	// последнее сообщение в сессии должно быть "finish"
//	tgTestMessages := []*telegram.TgMsgMock{
//		// чат 1 сессия 1
//		{Msg: &types.TGMessageIn{
//			ChatId: 1,
//			Text:   "text from tg test11",
//		}},
//		{Msg: &types.TGMessageIn{
//			ChatId: 1,
//			Text:   "text from tg test12",
//		}},
//		{Msg: &types.TGMessageIn{
//			ChatId: 1,
//			Text:   "finish",
//		}},
//		// чат 2 сессия 1
//		{Msg: &types.TGMessageIn{
//			ChatId: 2,
//			Text:   "text from tg test21",
//		}},
//		{Msg: &types.TGMessageIn{
//			ChatId: 2,
//			Text:   "text from tg test22",
//		}},
//		{Msg: &types.TGMessageIn{
//			ChatId: 2,
//			Text:   "finish",
//		}},
//	}
//
//	tgProvider = telegram.NewTelegramProviderMock([]*telegram.TgMsgMock{})
//
//	dialerClient = telegram.NewDialerClientMock(ctx)
//	mon := NewTgMonitoring(NewStdMetricStorage(os.Stdout))
//	mon.Run(ctx)
//
//	tgCaller := New(tgProvider, tgRepo, dialerClient, mon)
//
//	go func() {
//		if err := tgCaller.Run(ctx); err != nil {
//			log.Error("%s", err)
//		}
//	}()
//
//	b.ResetTimer()
//	for i := 0; i < 100; i++ {
//		tgProvider.(*telegram.TelegramProviderMock).SendToAll(tgTestMessages)
//	}
//	// на этом моменте могут оставаться не обработанные сообщения
//	b.StopTimer()
//
//	time.Sleep(time.Second)
//	cancel()
//	time.Sleep(100 * time.Millisecond)
//}
