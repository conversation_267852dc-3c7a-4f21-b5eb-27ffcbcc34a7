package tg_caller

import (
	"context"
	"fmt"
	logger "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/logger/typed_logger"
	"io"
	"time"
)

type monitoringEvent uint8
type monitoringAlert uint8

const (
	eventBotStarted monitoringEvent = iota
	eventBotStopped
	eventSessionStarted
	eventSessionStopped
	eventMessageReceived
	eventMessageSent
	eventCreateSessionTimeout
	eventBotError
	eventDialerError
)

const (
	alertBotError monitoringAlert = iota
	alertCreateSessionTimeout
	alertDialerError
)

var AlertMap = map[monitoringAlert]string{
	alertBotError:             "Bot error",
	alertCreateSessionTimeout: "Create session timeout",
	alertDialerError:          "Dialer error",
}

const (
	tgMonBufferSize = 100
)

type (
	event struct {
		event monitoringEvent
		botID int64
		//chatId    int64
		sessionId string
		//text      string
		err error
	}

	alert struct {
		alert monitoringAlert
		botID int64
		//chatId    int64
		sessionId string
		//text      string
		err error
	}

	BotState struct {
		botID          int64
		activeSessions MinMaxCounter
		messagesIn     int
		messagesOut    int
		errors         int
	}

	// BotStateMap key = botID
	BotStateMap map[int64]*BotState

	IMetricStorage interface {
		Push(ctx context.Context, states BotStateMap) error
	}

	StdMetricStorage struct {
		w io.Writer
	}

	TgMonitoring struct {
		events        chan event
		alerts        chan alert
		botsMap       BotStateMap
		metricStorage IMetricStorage
	}

	MinMaxCounter struct {
		Min     int
		Max     int
		Current int
	}
)

func (m MinMaxCounter) Incr() MinMaxCounter {
	m.Current++
	if m.Max == 0 || m.Max < m.Current {
		m.Max = m.Current
	}
	return m
}

func (m MinMaxCounter) Decr() MinMaxCounter {
	m.Current--
	if m.Current < 0 {
		m.Current = 0
	}
	if m.Min > m.Current {
		m.Min = m.Current
	}
	return m
}

func (m MinMaxCounter) Reset(val int) MinMaxCounter {
	m.Min = val
	m.Max = val
	m.Current = val
	return m
}

func (b BotStateMap) Copy() BotStateMap {
	newMap := make(BotStateMap, len(b))
	for k, v := range b {
		newState := *v
		newMap[k] = &newState
	}

	return newMap
}

func NewStdMetricStorage(w io.Writer) *StdMetricStorage {
	return &StdMetricStorage{w: w}
}

func NewTgMonitoring(metricStorage IMetricStorage) *TgMonitoring {
	m := &TgMonitoring{
		events:        make(chan event, tgMonBufferSize),
		alerts:        make(chan alert, tgMonBufferSize),
		botsMap:       make(map[int64]*BotState),
		metricStorage: metricStorage,
	}

	return m
}

func (m *TgMonitoring) AlertsCh() <-chan alert {
	return m.alerts
}

func (m *TgMonitoring) BotStarted(botID int64) {
	m.events <- event{event: eventBotStarted, botID: botID}
}

func (m *TgMonitoring) BotStopped(botID int64) {
	m.events <- event{event: eventBotStopped, botID: botID}
}

func (m *TgMonitoring) SessionStarted(botID int64) {
	m.events <- event{event: eventSessionStarted, botID: botID}
}

func (m *TgMonitoring) SessionClosed(botID int64) {
	m.events <- event{event: eventSessionStopped, botID: botID}
}

func (m *TgMonitoring) MessageReceived(botID int64) {
	m.events <- event{event: eventMessageReceived, botID: botID}
}

func (m *TgMonitoring) MessageSent(botID int64) {
	m.events <- event{event: eventMessageSent, botID: botID}
}

func (m *TgMonitoring) CreateSessionTimeout(botID int64, sessionID string) {
	m.events <- event{event: eventCreateSessionTimeout, botID: botID, sessionId: sessionID}
}

func (m *TgMonitoring) BotError(botID int64, err error) {
	m.events <- event{event: eventBotError, botID: botID, err: err}
}

func (m *TgMonitoring) DialerError(botID int64, sessionId string, err error) {
	m.events <- event{event: eventDialerError, botID: botID, sessionId: sessionId, err: err}
}

func (m *TgMonitoring) Run(ctx context.Context) {
	go func() {
		log := logger.DefaultLogger
		ticker := time.NewTicker(time.Minute)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return

			case <-ticker.C:
				if err := m.metricStorage.Push(ctx, m.botsMap.Copy()); err != nil {
					log.Error("Error pushing metrics: %v", err)
				}

			case e := <-m.events:
				switch e.event {
				case eventBotStarted:
					m.handleBotStarted(e.botID)
				case eventBotStopped:
					m.handleBotStopped(e.botID)
				case eventSessionStarted:
					m.handleSessionStarted(e.botID)
				case eventSessionStopped:
					m.handleSessionStopped(e.botID)
				case eventMessageReceived:
					m.handleMessageReceived(e.botID)
				case eventMessageSent:
					m.handleMessageSent(e.botID)
				case eventCreateSessionTimeout:
					m.handleCreateSessionTimeout(e.botID, e.sessionId)
				case eventBotError:
					m.handleBotError(e.botID, e.err)
				case eventDialerError:
					m.handleDialerError(e.botID, e.err)
				}
			}
		}
	}()
}

func (m *TgMonitoring) handleBotStarted(botID int64) {
	if _, ok := m.botsMap[botID]; !ok {
		m.botsMap[botID] = &BotState{botID: botID}
	}
}

func (m *TgMonitoring) handleBotStopped(botID int64) {
	delete(m.botsMap, botID)
}

func (m *TgMonitoring) handleSessionStarted(botID int64) {
	if botState, ok := m.botsMap[botID]; ok {
		botState.activeSessions = botState.activeSessions.Incr()
	}
}

func (m *TgMonitoring) handleSessionStopped(botID int64) {
	if botState, ok := m.botsMap[botID]; ok {
		botState.activeSessions = botState.activeSessions.Decr()
	}
}

func (m *TgMonitoring) handleMessageReceived(botID int64) {
	if bs, ok := m.botsMap[botID]; ok {
		bs.messagesIn++
	}
}

func (m *TgMonitoring) handleMessageSent(botID int64) {
	if bs, ok := m.botsMap[botID]; ok {
		bs.messagesOut++
	}
}

func (m *TgMonitoring) handleCreateSessionTimeout(botID int64, sessionID string) {
	if bs, ok := m.botsMap[botID]; ok {
		bs.errors++
	}
	m.alerts <- alert{alert: alertCreateSessionTimeout, botID: botID, sessionId: sessionID}
}

func (m *TgMonitoring) handleBotError(botID int64, err error) {
	if bs, ok := m.botsMap[botID]; ok {
		bs.errors++
	}
	m.alerts <- alert{alert: alertBotError, botID: botID, err: err}
}

func (m *TgMonitoring) handleDialerError(botID int64, err error) {
	if bs, ok := m.botsMap[botID]; ok {
		bs.errors++
	}
	m.alerts <- alert{alert: alertDialerError, botID: botID, err: err}
}

func (m *StdMetricStorage) Push(ctx context.Context, botStates BotStateMap) error {
	fmt.Fprintln(m.w, "--- Current metrics ---")
	fmt.Fprintf(m.w, "Total bots: %d\n", len(botStates))
	for botID, tgBot := range botStates {
		fmt.Fprintf(m.w, "  BotID=%d: activeSessions=%d max=%d messagesIn=%d messagesOut=%d\n", botID, tgBot.activeSessions.Current, tgBot.activeSessions.Max, tgBot.messagesIn, tgBot.messagesOut)
	}
	fmt.Fprintln(m.w, "-----------------------")

	return nil
}
