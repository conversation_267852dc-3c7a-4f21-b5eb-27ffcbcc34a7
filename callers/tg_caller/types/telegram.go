package types

import (
	"time"

	t "gitlab.prod.kvint.io/kvint-core/kvint-caller-go/types"
)

type (
	TGMessageIn struct {
		IsCmd  bool
		ChatId int64
		Text   string
	}

	TGMessageOut struct {
		ChatId        int64
		FaledAttempts int64
		Text          string
	}

	DialerTGMessageIn struct {
		IsFinalState bool
		Func         string
		Text         string
		SessionId    string
		DialerQueue  string
	}

	DialerTGMessageOut struct {
		Func      string
		TaskId    string
		SessionId string
		Text      string
		ReplyTo   string
	}

	DialerResultResponse struct {
		t.RmqCeleryPacket
		Data t.CallResult `json:"data"`
	}

	BotInfo struct {
		Token      string
		CampaignId string
		BuildId    string
	}

	TSLogType uint8

	TgSessionLog struct {
		Type TSLogType
		Func string
		Text string
		Time time.Time
	}
)

type (
	BotRequestParams struct {
		CallId   string `json:"call_id" bson:"call_id"`
		FuncType string `json:"func" bson:"func"`
	}

	BotRequest_CloseSession struct {
		ReqParams     BotRequestParams `json:"req_params"`
		ReplyTo       string           `json:"reply_to"`
		Func          string           `json:"func"`
		SessionId     string           `json:"session_id" bson:"session_id"`
		Status        string           `json:"status"`
		FastDown      bool             `json:"fast_down" bson:"fast_down"`
		Error         string           `json:"error"`
		SummaryResult string           `json:"summary_result" bson:"summary_result"`
	}

	BotRequest_SendMessage struct {
		ReqParams  BotRequestParams `json:"req_params" bson:"req_params"`
		Func       string           `json:"func"`
		SessionId  string           `json:"session_id" bson:"session_id"`
		Message    string           `json:"message"`
		ReplyTo    string           `json:"reply_to"`
		XRequestId string           `json:"x_request_id,omitempty" bson:"x_request_id,omitempty"`
	}
)

type Campaign struct {
	t.Campaign `json:"inline" bson:"inline"`
	TgToken    string `json:"tgToken" bson:"tgToken"`
}

const (
	TSLogTypeUserMessage TSLogType = iota
	TSLogTypeUserCommand
	TSLogTypeBotMessage
)
