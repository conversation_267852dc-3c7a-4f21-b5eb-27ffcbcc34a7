package tg_caller

import (
	"context"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/callers/tg_caller/types"
	"gitlab.prod.kvint.io/kvint-core/kvint-caller-go/config"
	"sync"
	"sync/atomic"
	"time"

	"golang.org/x/time/rate"
)

type (
	TelegramBot struct {
		Id         int64
		Token      string
		CampaignId string
		BuildId    string
		ctx        context.Context
		ctxCancel  context.CancelFunc
		// сообщения из телеги
		TgChIn <-chan *types.TGMessageIn
		// сообщения в телегу
		TgChOut chan<- *types.TGMessageOut
		// входящая очередь сообщений от дайлера
		CallerQueue string
		// сообщения из дайлера
		DialerChIn <-chan *types.DialerTGMessageIn
		// ключ chatId
		Sessions       map[int64]*Session
		sessionsMu     sync.RWMutex
		SessionChatMap map[string]int64
		scMu           sync.RWMutex
	}

	Session struct {
		SessionId    string
		TaskId       string
		ChatId       int64
		LastActivity time.Time
		// сообщения из телеги для отправки в нужный слот дайлера
		// отдельный канал для контроля отправки сообщений после начала и до конца сессии
		ChIn        chan *types.TGMessageIn
		DialerQueue string
		StartCh     chan struct{}
		CloseCh     chan struct{}
		// ограничитель отправки сообщений в дайлер
		limiter *rate.Limiter
		// буфер сообщений
		buffer []string
		bufMu  sync.RWMutex
		// последняя команда
		lastCmd atomic.Value
		//лог сообщений
		logs  []types.TgSessionLog
		logMu sync.RWMutex
	}

	ITelegramBot interface {
		GetMessagesCh() (<-chan *types.TGMessageIn, chan<- *types.TGMessageOut)
	}

	ITelegramProvider interface {
		RunBot(ctx context.Context, token string) (<-chan *types.TGMessageIn, chan<- *types.TGMessageOut, int64, error)
	}

	ITelegramRepo interface {
		GetBotInfoList(ctx context.Context) ([]*types.BotInfo, error)
		SaveSessionLogs(campaignId string, chatId int64, sessionId string, logs []types.TgSessionLog) error
		// todo
	}

	IDialerClient interface {
		SendTask(message *types.DialerTGMessageOut, queue string) error
		SendMessage(message *types.DialerTGMessageOut, queue string) error
		RunConsumer(ctx context.Context, queue string) (<-chan *types.DialerTGMessageIn, error)
		ConsumeResults(ctx context.Context) (chan *types.DialerResultResponse, error)
		// todo
	}
)

func (b *TelegramBot) GetSession(chatId int64) (*Session, bool) {
	b.sessionsMu.RLock()
	defer b.sessionsMu.RUnlock()
	session, ok := b.Sessions[chatId]
	return session, ok
}

func (b *TelegramBot) GetSessionByTask(taskId string) (*Session, bool) {
	b.scMu.RLock()
	chatId, ok := b.SessionChatMap[taskId]
	b.scMu.RUnlock()
	if !ok {
		return nil, false
	}
	b.sessionsMu.RLock()
	defer b.sessionsMu.RUnlock()
	session, ok := b.Sessions[chatId]
	return session, ok
}

func (b *TelegramBot) CreateSession(chatId int64, sessionId string) *Session {
	b.sessionsMu.Lock()
	session := &Session{
		SessionId:    sessionId,
		ChatId:       chatId,
		LastActivity: time.Now(),
		ChIn:         make(chan *types.TGMessageIn, 10),
		StartCh:      make(chan struct{}, 1),
		CloseCh:      make(chan struct{}, 1),
		limiter:      rate.NewLimiter(rate.Every(time.Duration(config.Cfg.TgCaller.LimitSecondsPerTgMessageIn)*time.Second), 1),
		buffer:       make([]string, 0, 10),
		lastCmd:      atomic.Value{},
		logs:         make([]types.TgSessionLog, 0, 2),
		logMu:        sync.RWMutex{},
	}
	session.lastCmd.Store("")
	b.Sessions[chatId] = session
	b.sessionsMu.Unlock()
	b.scMu.Lock()
	b.SessionChatMap[sessionId] = chatId
	b.scMu.Unlock()
	return session
}

func (b *TelegramBot) CloseSession(taskId string) {
	b.scMu.RLock()
	chatId, ok := b.SessionChatMap[taskId]
	b.scMu.RUnlock()
	if !ok {
		return
	}
	b.scMu.Lock()
	delete(b.SessionChatMap, taskId)
	b.scMu.Unlock()

	b.sessionsMu.Lock()
	delete(b.Sessions, chatId)
	b.sessionsMu.Unlock()
}

func (s *Session) Start() {
	s.StartCh <- struct{}{}
}

func (s *Session) Close() {
	s.CloseCh <- struct{}{}
}

func (s *Session) AddLog(t types.TSLogType, text, function string) {
	s.logMu.Lock()
	defer s.logMu.Unlock()
	s.logs = append(s.logs, types.TgSessionLog{
		Type: t,
		Text: text,
		Func: function,
		Time: time.Now(),
	})
}

func (s *Session) GetLogs() []types.TgSessionLog {
	newLogs := make([]types.TgSessionLog, len(s.logs))
	copy(newLogs, s.logs)
	return s.logs
}
